{"logs": [{"outputFile": "com.falaileh.nisso.test.app-mergeDebugAndroidTestResources-34:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8dca2d17bb97bd73368ea919040cc370\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1026,1109,1183,1259,1334,1407,1490,1558", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,75,74,72,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1021,1104,1178,1254,1329,1402,1485,1553,1670"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "841,940,1025,1129,1236,1332,1415,1505,1598,1681,1762,1845,1919,1995,2070,2244,2327,2395", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,75,74,72,82,67,116", "endOffsets": "935,1020,1124,1231,1327,1410,1500,1593,1676,1757,1840,1914,1990,2065,2138,2322,2390,2507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\910260a50c4cc0fe03b922548d59c7fb\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,309,410,510,618,722,2143", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "202,304,405,505,613,717,836,2239"}}]}]}