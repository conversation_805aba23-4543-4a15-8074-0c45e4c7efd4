{"logs": [{"outputFile": "com.falaileh.nisso.app-mergeReleaseResources-54:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0714f79536ad61645fc1954b99bc485\\transformed\\activity-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "251,272", "startColumns": "4,4", "startOffsets": "16487,17574", "endColumns": "41,59", "endOffsets": "16524,17629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a6578356e73273a33e87485d3aa259d5\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "20034", "endColumns": "82", "endOffsets": "20112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8cbec5eff64fe905f4db1ea5b946f7f4\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "244,248", "startColumns": "4,4", "startOffsets": "16153,16324", "endColumns": "53,66", "endOffsets": "16202,16386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd78f57ea890de7588788587cddbfd94\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "1997,2013,2019,3019,3035", "startColumns": "4,4,4,4,4", "startOffsets": "130075,130500,130678,164737,165148", "endLines": "2012,2018,2028,3034,3038", "endColumns": "24,24,24,24,24", "endOffsets": "130495,130673,130957,165143,165270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf42671a75f982be109a068148eb8bcc\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "274", "startColumns": "4", "startOffsets": "17688", "endColumns": "49", "endOffsets": "17733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d78d52374479b344b4520d482b99f827\\transformed\\lottie-6.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,118,165", "endLines": "2,3,38", "endColumns": "62,46,24", "endOffsets": "113,160,1884"}, "to": {"startLines": "5,247,2756", "startColumns": "4,4,4", "startOffsets": "299,16277,155760", "endLines": "5,247,2790", "endColumns": "62,46,24", "endOffsets": "357,16319,157479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b75ff885a4387959cc1f8838035c0761\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "240", "startColumns": "4", "startOffsets": "15947", "endColumns": "65", "endOffsets": "16008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\482ffcf29b6e8d892d0acd21c537d2fa\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "271", "startColumns": "4", "startOffsets": "17531", "endColumns": "42", "endOffsets": "17569"}}, {"source": "E:\\Nisso\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "42", "endOffsets": "54"}, "to": {"startLines": "310", "startColumns": "4", "startOffsets": "20117", "endColumns": "42", "endOffsets": "20155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b9f26681ff02d0a312dda2531b926043\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "239,252,275,2672,2677", "startColumns": "4,4,4,4,4", "startOffsets": "15890,16529,17738,153379,153549", "endLines": "239,252,275,2676,2680", "endColumns": "56,64,63,24,24", "endOffsets": "15942,16589,17797,153544,153693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4f6a67e73b1379ea40f79c239dcad68e\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "273", "startColumns": "4", "startOffsets": "17634", "endColumns": "53", "endOffsets": "17683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9360fea9cc06fc380f90ebb846dd3e6a\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "347,348", "startColumns": "4,4", "startOffsets": "22464,22520", "endColumns": "55,54", "endOffsets": "22515,22570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\910260a50c4cc0fe03b922548d59c7fb\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "6,17,18,31,32,55,56,158,159,160,161,162,163,164,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,245,246,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,281,311,312,313,314,315,316,317,343,1718,1719,1723,1724,1728,1881,1882,2528,2562,2618,2651,2681,2714", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "362,1081,1153,2201,2266,3685,3754,10672,10742,10810,10882,10952,11013,11087,11944,12005,12066,12128,12192,12254,12315,12383,12483,12543,12609,12682,12751,12808,12860,13375,13447,13523,13588,13647,13706,13766,13826,13886,13946,14006,14066,14126,14186,14246,14306,14365,14425,14485,14545,14605,14665,14725,14785,14845,14905,14965,15024,15084,15144,15203,15262,15321,15380,15439,16207,16242,16640,16695,16758,16813,16871,16929,16990,17053,17110,17161,17211,17272,17329,17395,17429,17464,18098,20160,20227,20299,20368,20437,20511,20583,22220,113319,113436,113637,113747,113948,125896,125968,147164,148737,150967,152698,153698,154380", "endLines": "6,17,18,31,32,55,56,158,159,160,161,162,163,164,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,245,246,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,281,311,312,313,314,315,316,317,343,1718,1722,1723,1727,1728,1881,1882,2533,2571,2650,2671,2713,2719", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "417,1148,1236,2261,2327,3749,3812,10737,10805,10877,10947,11008,11082,11155,12000,12061,12123,12187,12249,12310,12378,12478,12538,12604,12677,12746,12803,12855,12917,13442,13518,13583,13642,13701,13761,13821,13881,13941,14001,14061,14121,14181,14241,14301,14360,14420,14480,14540,14600,14660,14720,14780,14840,14900,14960,15019,15079,15139,15198,15257,15316,15375,15434,15493,16237,16272,16690,16753,16808,16866,16924,16985,17048,17105,17156,17206,17267,17324,17390,17424,17459,17494,18163,20222,20294,20363,20432,20506,20578,20666,22286,113431,113632,113742,113943,114072,125963,126030,147362,149033,152693,153374,154375,154542"}}, {"source": "E:\\Nisso\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2,4", "startColumns": "4,4", "startOffsets": "17,97", "endLines": "2,11", "endColumns": "78,12", "endOffsets": "91,538"}, "to": {"startLines": "1765,1766", "startColumns": "4,4", "startOffsets": "116713,116791", "endLines": "1765,1773", "endColumns": "77,12", "endOffsets": "116786,117232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8dca2d17bb97bd73368ea919040cc370\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "236,237,238,241,243,276,318,319,320,321,322,332,333,334,335,336,337,339,340,341,342,344,345,346,1437,1440,1443", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15703,15777,15835,16013,16098,17802,20671,20736,20790,20856,20957,21661,21713,21773,21835,21889,21939,22046,22092,22138,22180,22291,22338,22374,92015,92127,92238", "endLines": "236,237,238,241,243,276,318,319,320,321,322,332,333,334,335,336,337,339,340,341,342,344,345,346,1439,1442,1446", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "15772,15830,15885,16059,16148,17850,20731,20785,20851,20952,21010,21708,21768,21830,21884,21934,21988,22087,22133,22175,22215,22333,22369,22459,92122,92233,92428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85e1e1d8941ac4fc444c1da209e0c205\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,23,24,25,26,27,28,29,30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,165,166,167,168,169,170,171,172,173,189,190,191,192,193,194,195,196,232,233,234,235,242,249,250,253,270,277,278,279,280,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,338,349,350,351,352,353,354,362,363,367,371,375,380,386,393,397,401,406,410,414,418,422,426,430,436,440,446,450,456,460,465,469,472,476,482,486,492,496,502,505,509,513,517,521,525,526,527,528,531,534,537,540,544,545,546,547,548,551,553,555,557,562,563,567,573,577,578,580,592,593,597,603,607,608,609,613,640,644,645,649,677,849,875,1046,1072,1103,1111,1117,1133,1155,1160,1165,1175,1184,1193,1197,1204,1223,1230,1231,1240,1243,1246,1250,1254,1258,1261,1262,1267,1272,1282,1287,1294,1300,1301,1304,1308,1313,1315,1317,1320,1323,1325,1329,1332,1339,1342,1345,1349,1351,1355,1357,1359,1361,1365,1373,1381,1393,1399,1408,1411,1422,1425,1426,1431,1432,1447,1516,1586,1587,1597,1606,1607,1609,1613,1616,1619,1622,1625,1628,1631,1634,1638,1641,1644,1647,1651,1654,1658,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1684,1686,1687,1688,1689,1690,1691,1692,1693,1695,1696,1698,1699,1701,1703,1704,1706,1707,1708,1709,1710,1711,1713,1714,1715,1716,1717,1729,1731,1733,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1749,1750,1751,1752,1753,1754,1755,1757,1761,1774,1775,1776,1777,1778,1779,1783,1784,1785,1786,1788,1790,1792,1794,1796,1797,1798,1799,1801,1803,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1819,1820,1821,1822,1824,1826,1827,1829,1830,1832,1834,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1849,1850,1851,1852,1854,1855,1856,1857,1858,1860,1862,1864,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1883,1958,1961,1964,1967,1981,1987,2029,2032,2061,2088,2097,2161,2524,2534,2572,2600,2720,2744,2750,2791,2812,2936,2956,2962,2966,2972,3007,3039,3105,3125,3180,3192,3218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,422,463,518,580,644,714,775,850,926,1003,1241,1326,1408,1484,1560,1637,1715,1821,1927,2006,2086,2143,2332,2406,2481,2546,2612,2672,2733,2805,2878,2945,3013,3072,3131,3190,3249,3308,3362,3416,3469,3523,3577,3631,3817,3891,3970,4043,4117,4188,4260,4332,4405,4462,4520,4593,4667,4741,4816,4888,4961,5031,5102,5162,5223,5292,5361,5431,5505,5581,5645,5722,5798,5875,5940,6009,6086,6161,6230,6298,6375,6441,6502,6599,6664,6733,6832,6903,6962,7020,7077,7136,7200,7271,7343,7415,7487,7559,7626,7694,7762,7821,7884,7948,8038,8129,8189,8255,8322,8388,8458,8522,8575,8642,8703,8770,8883,8941,9004,9069,9134,9209,9282,9354,9398,9445,9491,9540,9601,9662,9723,9785,9849,9913,9977,10042,10105,10165,10226,10292,10351,10411,10473,10544,10604,11160,11246,11333,11423,11510,11598,11680,11763,11853,12922,12974,13032,13077,13143,13207,13264,13321,15498,15555,15603,15652,16064,16391,16438,16594,17499,17855,17919,17981,18041,18168,18242,18312,18390,18444,18514,18599,18647,18693,18754,18817,18883,18947,19018,19081,19146,19210,19271,19332,19384,19457,19531,19600,19675,19749,19823,19964,21993,22575,22653,22743,22831,22927,23017,23599,23688,23935,24216,24468,24753,25146,25623,25845,26067,26343,26570,26800,27030,27260,27490,27717,28136,28362,28787,29017,29445,29664,29947,30155,30286,30513,30939,31164,31591,31812,32237,32357,32633,32934,33258,33549,33863,34000,34131,34236,34478,34645,34849,35057,35328,35440,35552,35657,35774,35988,36134,36274,36360,36708,36796,37042,37460,37709,37791,37889,38546,38646,38898,39322,39577,39671,39760,39997,42021,42263,42365,42618,44774,55455,56971,67666,69194,70951,71577,71997,73258,74523,74779,75015,75562,76056,76661,76859,77439,78807,79182,79300,79838,79995,80191,80464,80720,80890,81031,81095,81460,81827,82503,82767,83105,83458,83552,83738,84044,84306,84431,84558,84797,85008,85127,85320,85497,85952,86133,86255,86514,86627,86814,86916,87023,87152,87427,87935,88431,89308,89602,90172,90321,91053,91225,91309,91645,91737,92433,97664,103035,103097,103675,104259,104350,104463,104692,104852,105004,105175,105341,105510,105677,105840,106083,106253,106426,106597,106871,107070,107275,107605,107689,107785,107881,107979,108079,108181,108283,108385,108487,108589,108689,108785,108897,109026,109149,109280,109411,109509,109623,109717,109857,109991,110087,110199,110299,110415,110511,110623,110723,110863,110999,111163,111293,111451,111601,111742,111886,112021,112133,112283,112411,112539,112675,112807,112937,113067,113179,114077,114223,114367,114505,114571,114661,114737,114841,114931,115033,115141,115249,115349,115429,115521,115619,115729,115781,115859,115965,116057,116161,116271,116393,116556,117237,117317,117417,117507,117617,117707,117948,118042,118148,118240,118340,118452,118566,118682,118798,118892,119006,119118,119220,119340,119462,119544,119648,119768,119894,119992,120086,120174,120286,120402,120524,120636,120811,120927,121013,121105,121217,121341,121408,121534,121602,121730,121874,122002,122071,122166,122281,122394,122493,122602,122713,122824,122925,123030,123130,123260,123351,123474,123568,123680,123766,123870,123966,124054,124172,124276,124380,124506,124594,124702,124802,124892,125002,125086,125188,125272,125326,125390,125496,125582,125692,125776,126035,128651,128769,128884,128964,129325,129558,130962,131040,132384,133745,134133,136976,147029,147367,149038,150395,154547,155298,155560,157484,157863,162141,162747,162976,163127,163342,164425,165275,168301,169045,171176,171516,172827", "endLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,23,24,25,26,27,28,29,30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,165,166,167,168,169,170,171,172,173,189,190,191,192,193,194,195,196,232,233,234,235,242,249,250,253,270,277,278,279,280,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,338,349,350,351,352,353,361,362,366,370,374,379,385,392,396,400,405,409,413,417,421,425,429,435,439,445,449,455,459,464,468,471,475,481,485,491,495,501,504,508,512,516,520,524,525,526,527,530,533,536,539,543,544,545,546,547,550,552,554,556,561,562,566,572,576,577,579,591,592,596,602,606,607,608,612,639,643,644,648,676,848,874,1045,1071,1102,1110,1116,1132,1154,1159,1164,1174,1183,1192,1196,1203,1222,1229,1230,1239,1242,1245,1249,1253,1257,1260,1261,1266,1271,1281,1286,1293,1299,1300,1303,1307,1312,1314,1316,1319,1322,1324,1328,1331,1338,1341,1344,1348,1350,1354,1356,1358,1360,1364,1372,1380,1392,1398,1407,1410,1421,1424,1425,1430,1431,1436,1515,1585,1586,1596,1605,1606,1608,1612,1615,1618,1621,1624,1627,1630,1633,1637,1640,1643,1646,1650,1653,1657,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1683,1685,1686,1687,1688,1689,1690,1691,1692,1694,1695,1697,1698,1700,1702,1703,1705,1706,1707,1708,1709,1710,1712,1713,1714,1715,1716,1717,1730,1732,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1748,1749,1750,1751,1752,1753,1754,1756,1760,1764,1774,1775,1776,1777,1778,1782,1783,1784,1785,1787,1789,1791,1793,1795,1796,1797,1798,1800,1802,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1818,1819,1820,1821,1823,1825,1826,1828,1829,1831,1833,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1848,1849,1850,1851,1853,1854,1855,1856,1857,1859,1861,1863,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1957,1960,1963,1966,1980,1986,1996,2031,2060,2087,2096,2160,2523,2527,2561,2599,2617,2743,2749,2755,2811,2935,2955,2961,2965,2971,3006,3018,3104,3124,3179,3191,3217,3224", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,458,513,575,639,709,770,845,921,998,1076,1321,1403,1479,1555,1632,1710,1816,1922,2001,2081,2138,2196,2401,2476,2541,2607,2667,2728,2800,2873,2940,3008,3067,3126,3185,3244,3303,3357,3411,3464,3518,3572,3626,3680,3886,3965,4038,4112,4183,4255,4327,4400,4457,4515,4588,4662,4736,4811,4883,4956,5026,5097,5157,5218,5287,5356,5426,5500,5576,5640,5717,5793,5870,5935,6004,6081,6156,6225,6293,6370,6436,6497,6594,6659,6728,6827,6898,6957,7015,7072,7131,7195,7266,7338,7410,7482,7554,7621,7689,7757,7816,7879,7943,8033,8124,8184,8250,8317,8383,8453,8517,8570,8637,8698,8765,8878,8936,8999,9064,9129,9204,9277,9349,9393,9440,9486,9535,9596,9657,9718,9780,9844,9908,9972,10037,10100,10160,10221,10287,10346,10406,10468,10539,10599,10667,11241,11328,11418,11505,11593,11675,11758,11848,11939,12969,13027,13072,13138,13202,13259,13316,13370,15550,15598,15647,15698,16093,16433,16482,16635,17526,17914,17976,18036,18093,18237,18307,18385,18439,18509,18594,18642,18688,18749,18812,18878,18942,19013,19076,19141,19205,19266,19327,19379,19452,19526,19595,19670,19744,19818,19959,20029,22041,22648,22738,22826,22922,23012,23594,23683,23930,24211,24463,24748,25141,25618,25840,26062,26338,26565,26795,27025,27255,27485,27712,28131,28357,28782,29012,29440,29659,29942,30150,30281,30508,30934,31159,31586,31807,32232,32352,32628,32929,33253,33544,33858,33995,34126,34231,34473,34640,34844,35052,35323,35435,35547,35652,35769,35983,36129,36269,36355,36703,36791,37037,37455,37704,37786,37884,38541,38641,38893,39317,39572,39666,39755,39992,42016,42258,42360,42613,44769,55450,56966,67661,69189,70946,71572,71992,73253,74518,74774,75010,75557,76051,76656,76854,77434,78802,79177,79295,79833,79990,80186,80459,80715,80885,81026,81090,81455,81822,82498,82762,83100,83453,83547,83733,84039,84301,84426,84553,84792,85003,85122,85315,85492,85947,86128,86250,86509,86622,86809,86911,87018,87147,87422,87930,88426,89303,89597,90167,90316,91048,91220,91304,91640,91732,92010,97659,103030,103092,103670,104254,104345,104458,104687,104847,104999,105170,105336,105505,105672,105835,106078,106248,106421,106592,106866,107065,107270,107600,107684,107780,107876,107974,108074,108176,108278,108380,108482,108584,108684,108780,108892,109021,109144,109275,109406,109504,109618,109712,109852,109986,110082,110194,110294,110410,110506,110618,110718,110858,110994,111158,111288,111446,111596,111737,111881,112016,112128,112278,112406,112534,112670,112802,112932,113062,113174,113314,114218,114362,114500,114566,114656,114732,114836,114926,115028,115136,115244,115344,115424,115516,115614,115724,115776,115854,115960,116052,116156,116266,116388,116551,116708,117312,117412,117502,117612,117702,117943,118037,118143,118235,118335,118447,118561,118677,118793,118887,119001,119113,119215,119335,119457,119539,119643,119763,119889,119987,120081,120169,120281,120397,120519,120631,120806,120922,121008,121100,121212,121336,121403,121529,121597,121725,121869,121997,122066,122161,122276,122389,122488,122597,122708,122819,122920,123025,123125,123255,123346,123469,123563,123675,123761,123865,123961,124049,124167,124271,124375,124501,124589,124697,124797,124887,124997,125081,125183,125267,125321,125385,125491,125577,125687,125771,125891,128646,128764,128879,128959,129320,129553,130070,131035,132379,133740,134128,136971,147024,147159,148732,150390,150962,155293,155555,155755,157858,162136,162742,162971,163122,163337,164420,164732,168296,169040,171171,171511,172822,173025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\54903ff84a4690cb020fc1e9d9860152\\transformed\\media3-exoplayer-1.0.0-beta03\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "323,324,325,326,327,328,329,330,331", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "21015,21085,21147,21212,21276,21353,21418,21508,21592", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "21080,21142,21207,21271,21348,21413,21503,21587,21656"}}]}]}