package com.falaileh.nisso.ui.utils

import androidx.compose.ui.graphics.Color
import kotlin.math.pow

/**
 * Utility object for color parsing and manipulation
 */
object ColorUtils {
    
    /**
     * Parse color string from API (hex format) to Compose Color
     * Supports formats: #RRGGBB, #AARRGGBB, RRGGBB
     */
    fun parseColor(colorString: String): Color {
        return try {
            val cleanColor = colorString.trim().removePrefix("#")
            
            when (cleanColor.length) {
                6 -> {
                    // RRGGBB format
                    val colorLong = cleanColor.toLong(16)
                    Color(
                        red = ((colorLong shr 16) and 0xFF) / 255f,
                        green = ((colorLong shr 8) and 0xFF) / 255f,
                        blue = (colorLong and 0xFF) / 255f,
                        alpha = 1f
                    )
                }
                8 -> {
                    // AARRGGBB format
                    val colorLong = cleanColor.toLong(16)
                    Color(
                        red = ((colorLong shr 16) and 0xFF) / 255f,
                        green = ((colorLong shr 8) and 0xFF) / 255f,
                        blue = (colorLong and 0xFF) / 255f,
                        alpha = ((colorLong shr 24) and 0xFF) / 255f
                    )
                }
                else -> {
                    // Fallback to default romantic color
                    Color(0xFFFF6B6B)
                }
            }
        } catch (e: Exception) {
            // Fallback to default romantic color if parsing fails
            Color(0xFFFF6B6B)
        }
    }
    
    /**
     * Get contrasting text color for better readability
     */
    fun getContrastingTextColor(backgroundColor: Color): Color {
        val luminance = calculateLuminance(backgroundColor)
        return if (luminance > 0.5f) Color.Black else Color.White
    }
    
    /**
     * Calculate luminance of a color
     */
    private fun calculateLuminance(color: Color): Float {
        val r = if (color.red <= 0.03928f) color.red / 12.92f else ((color.red + 0.055f) / 1.055f).pow(2.4f)
        val g = if (color.green <= 0.03928f) color.green / 12.92f else ((color.green + 0.055f) / 1.055f).pow(2.4f)
        val b = if (color.blue <= 0.03928f) color.blue / 12.92f else ((color.blue + 0.055f) / 1.055f).pow(2.4f)

        return 0.2126f * r + 0.7152f * g + 0.0722f * b
    }
    
    /**
     * Create a romantic gradient color list
     */
    fun createRomanticGradient(baseColor: Color): List<Color> {
        return listOf(
            baseColor,
            baseColor.copy(alpha = 0.8f),
            baseColor.copy(red = baseColor.red * 0.9f, green = baseColor.green * 0.9f, blue = baseColor.blue * 0.9f),
            Color.Black.copy(alpha = 0.3f)
        )
    }
    
    /**
     * Predefined romantic colors for fallback
     */
    val romanticColors = listOf(
        Color(0xFFFF6B6B), // Coral
        Color(0xFFFF8E8E), // Light coral
        Color(0xFFFFB3BA), // Pink
        Color(0xFFFFDFBA), // Peach
        Color(0xFFBAE1FF), // Light blue
        Color(0xFFB8B8FF), // Lavender
        Color(0xFFFFB8E6), // Rose
        Color(0xFFFFC0CB)  // Pink
    )
    
    /**
     * Get a random romantic color
     */
    fun getRandomRomanticColor(): Color {
        return romanticColors.random()
    }
}
