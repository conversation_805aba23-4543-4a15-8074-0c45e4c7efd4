[{"key": "androidx/room/AutoCloser$Companion.class", "name": "androidx/room/AutoCloser$Companion.class", "size": 831, "crc": -886783087}, {"key": "androidx/room/AutoCloser.class", "name": "androidx/room/AutoCloser.class", "size": 9923, "crc": 41742595}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$attachedDbs$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$attachedDbs$1.class", "size": 2123, "crc": 42397539}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$delete$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$delete$1.class", "size": 2117, "crc": -957580140}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$execSQL$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$execSQL$1.class", "size": 1848, "crc": 1378150667}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$execSQL$2.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$execSQL$2.class", "size": 1938, "crc": -715424981}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$inTransaction$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$inTransaction$1.class", "size": 1917, "crc": 875232202}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$insert$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$insert$1.class", "size": 2110, "crc": 1347505206}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isDatabaseIntegrityOk$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isDatabaseIntegrityOk$1.class", "size": 1933, "crc": 1929471578}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isDbLockedByCurrentThread$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isDbLockedByCurrentThread$1.class", "size": 1451, "crc": -1688532655}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isReadOnly$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isReadOnly$1.class", "size": 1900, "crc": 425466814}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isWriteAheadLoggingEnabled$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isWriteAheadLoggingEnabled$1.class", "size": 2087, "crc": 2037842762}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$maximumSize$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$maximumSize$1.class", "size": 1409, "crc": -688874211}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$needUpgrade$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$needUpgrade$1.class", "size": 1873, "crc": -1735161340}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$pageSize$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$pageSize$1.class", "size": 1644, "crc": 885451492}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$pageSize$2.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$pageSize$2.class", "size": 1825, "crc": 929496009}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$path$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$path$1.class", "size": 1864, "crc": 798801781}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$pokeOpen$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$pokeOpen$1.class", "size": 1840, "crc": -248733404}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$setForeignKeyConstraintsEnabled$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$setForeignKeyConstraintsEnabled$1.class", "size": 2021, "crc": 524675438}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$setLocale$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$setLocale$1.class", "size": 1857, "crc": 1501821462}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$setMaxSqlCacheSize$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$setMaxSqlCacheSize$1.class", "size": 1846, "crc": -725985632}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$setMaximumSize$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$setMaximumSize$1.class", "size": 1867, "crc": -738922919}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$update$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$update$1.class", "size": 2322, "crc": -740835122}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$version$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$version$1.class", "size": 1643, "crc": 234176732}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$version$2.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$version$2.class", "size": 1814, "crc": 1753880103}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$yieldIfContendedSafely$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$yieldIfContendedSafely$1.class", "size": 1953, "crc": -1077797477}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$yieldIfContendedSafely$2.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$yieldIfContendedSafely$2.class", "size": 1965, "crc": -347966143}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase.class", "size": 15751, "crc": -2031499595}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$execute$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$execute$1.class", "size": 1867, "crc": -1606699771}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$executeInsert$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$executeInsert$1.class", "size": 1905, "crc": 1909244724}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$executeSqliteStatementWithRefCount$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$executeSqliteStatementWithRefCount$1.class", "size": 2924, "crc": 723018715}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$executeUpdateDelete$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$executeUpdateDelete$1.class", "size": 1935, "crc": 1340209850}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$simpleQueryForLong$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$simpleQueryForLong$1.class", "size": 1920, "crc": 771283511}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$simpleQueryForString$1.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$simpleQueryForString$1.class", "size": 1917, "crc": 1549571180}, {"key": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement.class", "name": "androidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement.class", "size": 8299, "crc": 1069681314}, {"key": "androidx/room/AutoClosingRoomOpenHelper$KeepAliveCursor.class", "name": "androidx/room/AutoClosingRoomOpenHelper$KeepAliveCursor.class", "size": 8060, "crc": -1795383382}, {"key": "androidx/room/AutoClosingRoomOpenHelper.class", "name": "androidx/room/AutoClosingRoomOpenHelper.class", "size": 3259, "crc": 390788170}, {"key": "androidx/room/AutoClosingRoomOpenHelperFactory.class", "name": "androidx/room/AutoClosingRoomOpenHelperFactory.class", "size": 2150, "crc": 1550880017}, {"key": "androidx/room/DatabaseConfiguration.class", "name": "androidx/room/DatabaseConfiguration.class", "size": 18666, "crc": -894668030}, {"key": "androidx/room/DelegatingOpenHelper.class", "name": "androidx/room/DelegatingOpenHelper.class", "size": 649, "crc": -877195529}, {"key": "androidx/room/EntityDeletionOrUpdateAdapter.class", "name": "androidx/room/EntityDeletionOrUpdateAdapter.class", "size": 4712, "crc": 2030740437}, {"key": "androidx/room/EntityInsertionAdapter.class", "name": "androidx/room/EntityInsertionAdapter.class", "size": 9372, "crc": 4872262}, {"key": "androidx/room/EntityUpsertionAdapter.class", "name": "androidx/room/EntityUpsertionAdapter.class", "size": 9452, "crc": -1461225324}, {"key": "androidx/room/EntityUpsertionAdapterKt.class", "name": "androidx/room/EntityUpsertionAdapterKt.class", "size": 695, "crc": -1906917754}, {"key": "androidx/room/ExperimentalRoomApi.class", "name": "androidx/room/ExperimentalRoomApi.class", "size": 915, "crc": 835266744}, {"key": "androidx/room/InvalidationLiveDataContainer.class", "name": "androidx/room/InvalidationLiveDataContainer.class", "size": 2946, "crc": -316037920}, {"key": "androidx/room/InvalidationTracker$Companion.class", "name": "androidx/room/InvalidationTracker$Companion.class", "size": 2948, "crc": -1993941696}, {"key": "androidx/room/InvalidationTracker$ObservedTableTracker$Companion.class", "name": "androidx/room/InvalidationTracker$ObservedTableTracker$Companion.class", "size": 1053, "crc": -1597068883}, {"key": "androidx/room/InvalidationTracker$ObservedTableTracker.class", "name": "androidx/room/InvalidationTracker$ObservedTableTracker.class", "size": 5893, "crc": 1506551690}, {"key": "androidx/room/InvalidationTracker$Observer.class", "name": "androidx/room/InvalidationTracker$Observer.class", "size": 3198, "crc": 409068069}, {"key": "androidx/room/InvalidationTracker$ObserverWrapper.class", "name": "androidx/room/InvalidationTracker$ObserverWrapper.class", "size": 6538, "crc": -972342608}, {"key": "androidx/room/InvalidationTracker$WeakObserver.class", "name": "androidx/room/InvalidationTracker$WeakObserver.class", "size": 2471, "crc": 2116572809}, {"key": "androidx/room/InvalidationTracker$refreshRunnable$1.class", "name": "androidx/room/InvalidationTracker$refreshRunnable$1.class", "size": 8462, "crc": -2045936783}, {"key": "androidx/room/InvalidationTracker.class", "name": "androidx/room/InvalidationTracker.class", "size": 25904, "crc": 312085345}, {"key": "androidx/room/MultiInstanceInvalidationClient$1.class", "name": "androidx/room/MultiInstanceInvalidationClient$1.class", "size": 3640, "crc": 1480475313}, {"key": "androidx/room/MultiInstanceInvalidationClient$callback$1.class", "name": "androidx/room/MultiInstanceInvalidationClient$callback$1.class", "size": 2624, "crc": 2058549122}, {"key": "androidx/room/MultiInstanceInvalidationClient$serviceConnection$1.class", "name": "androidx/room/MultiInstanceInvalidationClient$serviceConnection$1.class", "size": 2388, "crc": 1199823932}, {"key": "androidx/room/MultiInstanceInvalidationClient.class", "name": "androidx/room/MultiInstanceInvalidationClient.class", "size": 8659, "crc": -1490191929}, {"key": "androidx/room/MultiInstanceInvalidationService$binder$1.class", "name": "androidx/room/MultiInstanceInvalidationService$binder$1.class", "size": 4938, "crc": 266815565}, {"key": "androidx/room/MultiInstanceInvalidationService$callbackList$1.class", "name": "androidx/room/MultiInstanceInvalidationService$callbackList$1.class", "size": 1991, "crc": -1641443782}, {"key": "androidx/room/MultiInstanceInvalidationService.class", "name": "androidx/room/MultiInstanceInvalidationService.class", "size": 3046, "crc": 1550997237}, {"key": "androidx/room/QueryInterceptorDatabase.class", "name": "androidx/room/QueryInterceptorDatabase.class", "size": 14824, "crc": 197090922}, {"key": "androidx/room/QueryInterceptorOpenHelper.class", "name": "androidx/room/QueryInterceptorOpenHelper.class", "size": 3066, "crc": 593055083}, {"key": "androidx/room/QueryInterceptorOpenHelperFactory.class", "name": "androidx/room/QueryInterceptorOpenHelperFactory.class", "size": 2368, "crc": 1656305334}, {"key": "androidx/room/QueryInterceptorProgram.class", "name": "androidx/room/QueryInterceptorProgram.class", "size": 2924, "crc": -525448761}, {"key": "androidx/room/QueryInterceptorStatement.class", "name": "androidx/room/QueryInterceptorStatement.class", "size": 5786, "crc": -516672156}, {"key": "androidx/room/Room.class", "name": "androidx/room/Room.class", "size": 5724, "crc": -94661755}, {"key": "androidx/room/RoomDatabase$Builder.class", "name": "androidx/room/RoomDatabase$Builder.class", "size": 22602, "crc": 1526752532}, {"key": "androidx/room/RoomDatabase$Callback.class", "name": "androidx/room/RoomDatabase$Callback.class", "size": 1278, "crc": -597015774}, {"key": "androidx/room/RoomDatabase$Companion.class", "name": "androidx/room/RoomDatabase$Companion.class", "size": 851, "crc": 1459395914}, {"key": "androidx/room/RoomDatabase$JournalMode.class", "name": "androidx/room/RoomDatabase$JournalMode.class", "size": 2851, "crc": 1137826896}, {"key": "androidx/room/RoomDatabase$MigrationContainer.class", "name": "androidx/room/RoomDatabase$MigrationContainer.class", "size": 7297, "crc": -557209164}, {"key": "androidx/room/RoomDatabase$PrepackagedDatabaseCallback.class", "name": "androidx/room/RoomDatabase$PrepackagedDatabaseCallback.class", "size": 1103, "crc": 1534179957}, {"key": "androidx/room/RoomDatabase$QueryCallback.class", "name": "androidx/room/RoomDatabase$QueryCallback.class", "size": 815, "crc": -847561078}, {"key": "androidx/room/RoomDatabase$beginTransaction$1.class", "name": "androidx/room/RoomDatabase$beginTransaction$1.class", "size": 1712, "crc": 863213764}, {"key": "androidx/room/RoomDatabase$endTransaction$1.class", "name": "androidx/room/RoomDatabase$endTransaction$1.class", "size": 1704, "crc": -1409570218}, {"key": "androidx/room/RoomDatabase.class", "name": "androidx/room/RoomDatabase.class", "size": 23081, "crc": -1939921118}, {"key": "androidx/room/RoomOpenHelper$Companion.class", "name": "androidx/room/RoomOpenHelper$Companion.class", "size": 3941, "crc": -1346232449}, {"key": "androidx/room/RoomOpenHelper$Delegate.class", "name": "androidx/room/RoomOpenHelper$Delegate.class", "size": 2578, "crc": -612625633}, {"key": "androidx/room/RoomOpenHelper$ValidationResult.class", "name": "androidx/room/RoomOpenHelper$ValidationResult.class", "size": 1273, "crc": -1510292992}, {"key": "androidx/room/RoomOpenHelper.class", "name": "androidx/room/RoomOpenHelper.class", "size": 9742, "crc": -686194947}, {"key": "androidx/room/RoomSQLiteQuery$Binding.class", "name": "androidx/room/RoomSQLiteQuery$Binding.class", "size": 688, "crc": -574707734}, {"key": "androidx/room/RoomSQLiteQuery$Companion$copyFrom$1.class", "name": "androidx/room/RoomSQLiteQuery$Companion$copyFrom$1.class", "size": 2222, "crc": -1934008402}, {"key": "androidx/room/RoomSQLiteQuery$Companion.class", "name": "androidx/room/RoomSQLiteQuery$Companion.class", "size": 4304, "crc": -77202343}, {"key": "androidx/room/RoomSQLiteQuery.class", "name": "androidx/room/RoomSQLiteQuery.class", "size": 7334, "crc": 796908725}, {"key": "androidx/room/RoomTrackingLiveData$observer$1.class", "name": "androidx/room/RoomTrackingLiveData$observer$1.class", "size": 1974, "crc": 1771122716}, {"key": "androidx/room/RoomTrackingLiveData.class", "name": "androidx/room/RoomTrackingLiveData.class", "size": 6716, "crc": 1920302750}, {"key": "androidx/room/SQLiteCopyOpenHelper$createFrameworkOpenHelper$configuration$1.class", "name": "androidx/room/SQLiteCopyOpenHelper$createFrameworkOpenHelper$configuration$1.class", "size": 1967, "crc": -1441040496}, {"key": "androidx/room/SQLiteCopyOpenHelper.class", "name": "androidx/room/SQLiteCopyOpenHelper.class", "size": 11657, "crc": -1779788728}, {"key": "androidx/room/SQLiteCopyOpenHelperFactory.class", "name": "androidx/room/SQLiteCopyOpenHelperFactory.class", "size": 2918, "crc": -152099175}, {"key": "androidx/room/SharedSQLiteStatement$stmt$2.class", "name": "androidx/room/SharedSQLiteStatement$stmt$2.class", "size": 1434, "crc": -1403401683}, {"key": "androidx/room/SharedSQLiteStatement.class", "name": "androidx/room/SharedSQLiteStatement.class", "size": 3375, "crc": -1822056983}, {"key": "androidx/room/TransactionExecutor.class", "name": "androidx/room/TransactionExecutor.class", "size": 3565, "crc": 1640583256}, {"key": "androidx/room/migration/AutoMigrationSpec.class", "name": "androidx/room/migration/AutoMigrationSpec.class", "size": 898, "crc": -1373093638}, {"key": "androidx/room/migration/Migration.class", "name": "androidx/room/migration/Migration.class", "size": 1037, "crc": 1843241453}, {"key": "androidx/room/util/CursorUtil$wrapMappedColumns$2.class", "name": "androidx/room/util/CursorUtil$wrapMappedColumns$2.class", "size": 2764, "crc": -956758487}, {"key": "androidx/room/util/CursorUtil.class", "name": "androidx/room/util/CursorUtil.class", "size": 8568, "crc": -142132949}, {"key": "androidx/room/util/DBUtil.class", "name": "androidx/room/util/DBUtil.class", "size": 10337, "crc": 1675763365}, {"key": "androidx/room/util/FileUtil.class", "name": "androidx/room/util/FileUtil.class", "size": 2946, "crc": 115181961}, {"key": "androidx/room/util/FtsTableInfo$Companion.class", "name": "androidx/room/util/FtsTableInfo$Companion.class", "size": 10963, "crc": 1765679916}, {"key": "androidx/room/util/FtsTableInfo.class", "name": "androidx/room/util/FtsTableInfo.class", "size": 4289, "crc": -1670157657}, {"key": "androidx/room/util/RelationUtil.class", "name": "androidx/room/util/RelationUtil.class", "size": 4428, "crc": -1236915023}, {"key": "androidx/room/util/StringUtil.class", "name": "androidx/room/util/StringUtil.class", "size": 5302, "crc": -596611911}, {"key": "androidx/room/util/TableInfo$Column$Companion.class", "name": "androidx/room/util/TableInfo$Column$Companion.class", "size": 3531, "crc": -1785599815}, {"key": "androidx/room/util/TableInfo$Column.class", "name": "androidx/room/util/TableInfo$Column.class", "size": 5535, "crc": -384634448}, {"key": "androidx/room/util/TableInfo$Companion.class", "name": "androidx/room/util/TableInfo$Companion.class", "size": 1687, "crc": 309798801}, {"key": "androidx/room/util/TableInfo$CreatedFrom.class", "name": "androidx/room/util/TableInfo$CreatedFrom.class", "size": 691, "crc": 699006883}, {"key": "androidx/room/util/TableInfo$ForeignKey.class", "name": "androidx/room/util/TableInfo$ForeignKey.class", "size": 3302, "crc": 336872392}, {"key": "androidx/room/util/TableInfo$ForeignKeyWithSequence.class", "name": "androidx/room/util/TableInfo$ForeignKeyWithSequence.class", "size": 2251, "crc": 1642294496}, {"key": "androidx/room/util/TableInfo$Index$Companion.class", "name": "androidx/room/util/TableInfo$Index$Companion.class", "size": 912, "crc": 1344210577}, {"key": "androidx/room/util/TableInfo$Index.class", "name": "androidx/room/util/TableInfo$Index.class", "size": 5152, "crc": 1122532109}, {"key": "androidx/room/util/TableInfo.class", "name": "androidx/room/util/TableInfo.class", "size": 5134, "crc": 584657992}, {"key": "androidx/room/util/TableInfoKt.class", "name": "androidx/room/util/TableInfoKt.class", "size": 16603, "crc": 318210954}, {"key": "androidx/room/util/UUIDUtil.class", "name": "androidx/room/util/UUIDUtil.class", "size": 1786, "crc": 408666569}, {"key": "androidx/room/util/ViewInfo$Companion.class", "name": "androidx/room/util/ViewInfo$Companion.class", "size": 3700, "crc": 1526941807}, {"key": "androidx/room/util/ViewInfo.class", "name": "androidx/room/util/ViewInfo.class", "size": 2789, "crc": -1693382010}, {"key": "androidx/room/IMultiInstanceInvalidationCallback$Default.class", "name": "androidx/room/IMultiInstanceInvalidationCallback$Default.class", "size": 757, "crc": -2074588616}, {"key": "androidx/room/IMultiInstanceInvalidationCallback$Stub$Proxy.class", "name": "androidx/room/IMultiInstanceInvalidationCallback$Stub$Proxy.class", "size": 1567, "crc": 1393563597}, {"key": "androidx/room/IMultiInstanceInvalidationCallback$Stub.class", "name": "androidx/room/IMultiInstanceInvalidationCallback$Stub.class", "size": 2031, "crc": 2087108784}, {"key": "androidx/room/IMultiInstanceInvalidationCallback.class", "name": "androidx/room/IMultiInstanceInvalidationCallback.class", "size": 980, "crc": -880679517}, {"key": "androidx/room/IMultiInstanceInvalidationService$Default.class", "name": "androidx/room/IMultiInstanceInvalidationService$Default.class", "size": 1224, "crc": 262936090}, {"key": "androidx/room/IMultiInstanceInvalidationService$Stub$Proxy.class", "name": "androidx/room/IMultiInstanceInvalidationService$Stub$Proxy.class", "size": 2766, "crc": -1847082446}, {"key": "androidx/room/IMultiInstanceInvalidationService$Stub.class", "name": "androidx/room/IMultiInstanceInvalidationService$Stub.class", "size": 2966, "crc": -581555723}, {"key": "androidx/room/IMultiInstanceInvalidationService.class", "name": "androidx/room/IMultiInstanceInvalidationService.class", "size": 1190, "crc": -649141430}, {"key": "androidx/room/paging/LimitOffsetDataSource$1.class", "name": "androidx/room/paging/LimitOffsetDataSource$1.class", "size": 1258, "crc": 1885319284}, {"key": "androidx/room/paging/LimitOffsetDataSource.class", "name": "androidx/room/paging/LimitOffsetDataSource.class", "size": 8309, "crc": -1483084550}, {"key": "META-INF/androidx.room_room-runtime.version", "name": "META-INF/androidx.room_room-runtime.version", "size": 6, "crc": 1201321051}, {"key": "META-INF/room-runtime_release.kotlin_module", "name": "META-INF/room-runtime_release.kotlin_module", "size": 168, "crc": -1409635252}]