[{"key": "androidx/test/runner/lifecycle/Stage.class", "name": "androidx/test/runner/lifecycle/Stage.class", "size": 1504, "crc": 337205409}, {"key": "androidx/test/runner/lifecycle/ApplicationStage.class", "name": "androidx/test/runner/lifecycle/ApplicationStage.class", "size": 1249, "crc": 1101742463}, {"key": "androidx/test/runner/lifecycle/ApplicationLifecycleMonitorRegistry.class", "name": "androidx/test/runner/lifecycle/ApplicationLifecycleMonitorRegistry.class", "size": 1527, "crc": 1235964880}, {"key": "androidx/test/runner/lifecycle/ApplicationLifecycleMonitor.class", "name": "androidx/test/runner/lifecycle/ApplicationLifecycleMonitor.class", "size": 350, "crc": 1890388129}, {"key": "androidx/test/runner/lifecycle/ApplicationLifecycleCallback.class", "name": "androidx/test/runner/lifecycle/ApplicationLifecycleCallback.class", "size": 336, "crc": 10551155}, {"key": "androidx/test/runner/lifecycle/ActivityLifecycleMonitorRegistry.class", "name": "androidx/test/runner/lifecycle/ActivityLifecycleMonitorRegistry.class", "size": 1503, "crc": 1531913843}, {"key": "androidx/test/runner/lifecycle/ActivityLifecycleMonitor.class", "name": "androidx/test/runner/lifecycle/ActivityLifecycleMonitor.class", "size": 682, "crc": 1211285073}, {"key": "androidx/test/runner/lifecycle/ActivityLifecycleCallback.class", "name": "androidx/test/runner/lifecycle/ActivityLifecycleCallback.class", "size": 318, "crc": 906952067}, {"key": "androidx/test/runner/intercepting/InterceptingActivityFactory.class", "name": "androidx/test/runner/intercepting/InterceptingActivityFactory.class", "size": 467, "crc": -2045587825}, {"key": "androidx/test/runner/intent/IntentStubberRegistry.class", "name": "androidx/test/runner/intent/IntentStubberRegistry.class", "size": 1810, "crc": 915791341}, {"key": "androidx/test/runner/intent/IntentStubber.class", "name": "androidx/test/runner/intent/IntentStubber.class", "size": 413, "crc": -2055212472}, {"key": "androidx/test/runner/intent/IntentMonitorRegistry.class", "name": "androidx/test/runner/intent/IntentMonitorRegistry.class", "size": 1386, "crc": 1364960488}, {"key": "androidx/test/runner/intent/IntentMonitor.class", "name": "androidx/test/runner/intent/IntentMonitor.class", "size": 296, "crc": 1279067698}, {"key": "androidx/test/runner/intent/IntentCallback.class", "name": "androidx/test/runner/intent/IntentCallback.class", "size": 229, "crc": 431018194}, {"key": "androidx/test/runner/MonitoringInstrumentation.class", "name": "androidx/test/runner/MonitoringInstrumentation.class", "size": 31976, "crc": 140624467}, {"key": "androidx/test/runner/MonitoringInstrumentation$StubResultCallable.class", "name": "androidx/test/runner/MonitoringInstrumentation$StubResultCallable.class", "size": 1435, "crc": 1926404013}, {"key": "androidx/test/runner/MonitoringInstrumentation$ActivityFinisher.class", "name": "androidx/test/runner/MonitoringInstrumentation$ActivityFinisher.class", "size": 2700, "crc": 409313858}, {"key": "androidx/test/runner/MonitoringInstrumentation$5.class", "name": "androidx/test/runner/MonitoringInstrumentation$5.class", "size": 2358, "crc": -19727827}, {"key": "androidx/test/runner/MonitoringInstrumentation$4.class", "name": "androidx/test/runner/MonitoringInstrumentation$4.class", "size": 1308, "crc": 1111020914}, {"key": "androidx/test/runner/MonitoringInstrumentation$3.class", "name": "androidx/test/runner/MonitoringInstrumentation$3.class", "size": 2682, "crc": 1708955701}, {"key": "androidx/test/runner/MonitoringInstrumentation$2.class", "name": "androidx/test/runner/MonitoringInstrumentation$2.class", "size": 1185, "crc": -1874855239}, {"key": "androidx/test/runner/MonitoringInstrumentation$1.class", "name": "androidx/test/runner/MonitoringInstrumentation$1.class", "size": 1076, "crc": 323976533}, {"key": "androidx/test/platform/view/inspector/WindowInspectorCompat.class", "name": "androidx/test/platform/view/inspector/WindowInspectorCompat.class", "size": 3609, "crc": 780741684}, {"key": "androidx/test/platform/view/inspector/WindowInspectorCompat$ViewRetrievalException.class", "name": "androidx/test/platform/view/inspector/WindowInspectorCompat$ViewRetrievalException.class", "size": 962, "crc": 1139676680}, {"key": "androidx/test/platform/ui/UiController.class", "name": "androidx/test/platform/ui/UiController.class", "size": 568, "crc": -1339910652}, {"key": "androidx/test/platform/ui/InjectEventSecurityException.class", "name": "androidx/test/platform/ui/InjectEventSecurityException.class", "size": 824, "crc": 948611246}, {"key": "androidx/test/platform/tracing/Tracing.class", "name": "androidx/test/platform/tracing/Tracing.class", "size": 4653, "crc": 1278975950}, {"key": "androidx/test/platform/tracing/Tracing$TracerSpan.class", "name": "androidx/test/platform/tracing/Tracing$TracerSpan.class", "size": 2968, "crc": 538556523}, {"key": "androidx/test/platform/tracing/Tracing$TracerSpan-IA.class", "name": "androidx/test/platform/tracing/Tracing$TracerSpan-IA.class", "size": 262, "crc": -706804200}, {"key": "androidx/test/platform/tracing/Tracer.class", "name": "androidx/test/platform/tracing/Tracer.class", "size": 784, "crc": 255358247}, {"key": "androidx/test/platform/tracing/Tracer$Span.class", "name": "androidx/test/platform/tracing/Tracer$Span.class", "size": 836, "crc": 16542080}, {"key": "androidx/test/platform/tracing/AndroidXTracer.class", "name": "androidx/test/platform/tracing/AndroidXTracer.class", "size": 2014, "crc": -597273549}, {"key": "androidx/test/platform/tracing/AndroidXTracer$AndroidXTracerSpan.class", "name": "androidx/test/platform/tracing/AndroidXTracer$AndroidXTracerSpan.class", "size": 1711, "crc": 2078530599}, {"key": "androidx/test/platform/tracing/AndroidXTracer$AndroidXTracerSpan-IA.class", "name": "androidx/test/platform/tracing/AndroidXTracer$AndroidXTracerSpan-IA.class", "size": 277, "crc": 2109058268}, {"key": "androidx/test/platform/io/PlatformTestStorageRegistry.class", "name": "androidx/test/platform/io/PlatformTestStorageRegistry.class", "size": 2012, "crc": 47256083}, {"key": "androidx/test/platform/io/PlatformTestStorageRegistry$NoOpPlatformTestStorage.class", "name": "androidx/test/platform/io/PlatformTestStorageRegistry$NoOpPlatformTestStorage.class", "size": 2446, "crc": 1578086263}, {"key": "androidx/test/platform/io/PlatformTestStorageRegistry$NoOpPlatformTestStorage$NullOutputStream.class", "name": "androidx/test/platform/io/PlatformTestStorageRegistry$NoOpPlatformTestStorage$NullOutputStream.class", "size": 790, "crc": -221944123}, {"key": "androidx/test/platform/io/PlatformTestStorageRegistry$NoOpPlatformTestStorage$NullInputStream.class", "name": "androidx/test/platform/io/PlatformTestStorageRegistry$NoOpPlatformTestStorage$NullInputStream.class", "size": 737, "crc": 541549695}, {"key": "androidx/test/platform/io/PlatformTestStorageRegistry$$ExternalSyntheticLambda0.class", "name": "androidx/test/platform/io/PlatformTestStorageRegistry$$ExternalSyntheticLambda0.class", "size": 794, "crc": 328716501}, {"key": "androidx/test/platform/io/PlatformTestStorage.class", "name": "androidx/test/platform/io/PlatformTestStorage.class", "size": 1130, "crc": -394931642}, {"key": "androidx/test/platform/io/OutputDirCalculator.class", "name": "androidx/test/platform/io/OutputDirCalculator.class", "size": 3209, "crc": 1596031068}, {"key": "androidx/test/platform/io/OutputDirCalculator$outputDir$2.class", "name": "androidx/test/platform/io/OutputDirCalculator$outputDir$2.class", "size": 1383, "crc": -434688092}, {"key": "androidx/test/platform/io/FileTestStorage.class", "name": "androidx/test/platform/io/FileTestStorage.class", "size": 4192, "crc": -1838500737}, {"key": "androidx/test/platform/graphics/HardwareRendererCompat.class", "name": "androidx/test/platform/graphics/HardwareRendererCompat.class", "size": 2288, "crc": -1855241632}, {"key": "androidx/test/platform/device/DeviceController.class", "name": "androidx/test/platform/device/DeviceController.class", "size": 491, "crc": 1002415273}, {"key": "androidx/test/platform/device/DeviceController$ScreenOrientation.class", "name": "androidx/test/platform/device/DeviceController$ScreenOrientation.class", "size": 1451, "crc": -937264131}, {"key": "androidx/test/platform/app/InstrumentationRegistry.class", "name": "androidx/test/platform/app/InstrumentationRegistry.class", "size": 1832, "crc": -487854794}, {"key": "androidx/test/platform/TestFrameworkException.class", "name": "androidx/test/platform/TestFrameworkException.class", "size": 148, "crc": -558261507}, {"key": "androidx/test/internal/util/ReflectionUtil.class", "name": "androidx/test/internal/util/ReflectionUtil.class", "size": 3488, "crc": -419535554}, {"key": "androidx/test/internal/util/ReflectionUtil$ReflectionParams.class", "name": "androidx/test/internal/util/ReflectionUtil$ReflectionParams.class", "size": 1623, "crc": 897582961}, {"key": "androidx/test/internal/util/ReflectionUtil$ReflectionException.class", "name": "androidx/test/internal/util/ReflectionUtil$ReflectionException.class", "size": 886, "crc": 110494134}, {"key": "androidx/test/internal/util/ProcSummary.class", "name": "androidx/test/internal/util/ProcSummary.class", "size": 5589, "crc": 1769056503}, {"key": "androidx/test/internal/util/ProcSummary-IA.class", "name": "androidx/test/internal/util/ProcSummary-IA.class", "size": 252, "crc": -175329062}, {"key": "androidx/test/internal/util/ProcSummary$SummaryException.class", "name": "androidx/test/internal/util/ProcSummary$SummaryException.class", "size": 732, "crc": 1000991268}, {"key": "androidx/test/internal/util/ProcSummary$Builder.class", "name": "androidx/test/internal/util/ProcSummary$Builder.class", "size": 2887, "crc": -1578755253}, {"key": "androidx/test/internal/util/ParcelableIBinder.class", "name": "androidx/test/internal/util/ParcelableIBinder.class", "size": 1819, "crc": 1866333982}, {"key": "androidx/test/internal/util/ParcelableIBinder$1.class", "name": "androidx/test/internal/util/ParcelableIBinder$1.class", "size": 1297, "crc": -99688960}, {"key": "androidx/test/internal/util/LogUtil.class", "name": "androidx/test/internal/util/LogUtil.class", "size": 3449, "crc": -1236152695}, {"key": "androidx/test/internal/util/LogUtil$Supplier.class", "name": "androidx/test/internal/util/LogUtil$Supplier.class", "size": 331, "crc": 537242891}, {"key": "androidx/test/internal/util/LogUtil$$ExternalSyntheticLambda1.class", "name": "androidx/test/internal/util/LogUtil$$ExternalSyntheticLambda1.class", "size": 635, "crc": 1968330029}, {"key": "androidx/test/internal/util/LogUtil$$ExternalSyntheticLambda0.class", "name": "androidx/test/internal/util/LogUtil$$ExternalSyntheticLambda0.class", "size": 646, "crc": 1855665996}, {"key": "androidx/test/internal/util/Checks.class", "name": "androidx/test/internal/util/Checks.class", "size": 4580, "crc": -1811721888}, {"key": "androidx/test/internal/util/Checks$1.class", "name": "androidx/test/internal/util/Checks$1.class", "size": 1185, "crc": 1265642747}, {"key": "androidx/test/internal/runner/lifecycle/ApplicationLifecycleMonitorImpl.class", "name": "androidx/test/internal/runner/lifecycle/ApplicationLifecycleMonitorImpl.class", "size": 4060, "crc": 2123993500}, {"key": "androidx/test/internal/runner/lifecycle/ActivityLifecycleMonitorImpl.class", "name": "androidx/test/internal/runner/lifecycle/ActivityLifecycleMonitorImpl.class", "size": 6963, "crc": 349434108}, {"key": "androidx/test/internal/runner/lifecycle/ActivityLifecycleMonitorImpl$ActivityStatus.class", "name": "androidx/test/internal/runner/lifecycle/ActivityLifecycleMonitorImpl$ActivityStatus.class", "size": 1757, "crc": 1915700679}, {"key": "androidx/test/internal/runner/intercepting/package-info.class", "name": "androidx/test/internal/runner/intercepting/package-info.class", "size": 411, "crc": 1188957693}, {"key": "androidx/test/internal/runner/intercepting/DefaultInterceptingActivityFactory.class", "name": "androidx/test/internal/runner/intercepting/DefaultInterceptingActivityFactory.class", "size": 1070, "crc": 1768135247}, {"key": "androidx/test/internal/runner/intent/IntentMonitorImpl.class", "name": "androidx/test/internal/runner/intent/IntentMonitorImpl.class", "size": 3318, "crc": -924367739}, {"key": "androidx/test/internal/runner/hidden/ExposedInstrumentationApi.class", "name": "androidx/test/internal/runner/hidden/ExposedInstrumentationApi.class", "size": 648, "crc": -317533685}, {"key": "androidx/test/internal/runner/InstrumentationConnection.class", "name": "androidx/test/internal/runner/InstrumentationConnection.class", "size": 9914, "crc": 1472038627}, {"key": "androidx/test/internal/runner/InstrumentationConnection$MessengerReceiver.class", "name": "androidx/test/internal/runner/InstrumentationConnection$MessengerReceiver.class", "size": 2485, "crc": -551640314}, {"key": "androidx/test/internal/runner/InstrumentationConnection$IncomingHandler.class", "name": "androidx/test/internal/runner/InstrumentationConnection$IncomingHandler.class", "size": 14124, "crc": -2031815117}, {"key": "androidx/test/internal/runner/InstrumentationConnection$IncomingHandler$3.class", "name": "androidx/test/internal/runner/InstrumentationConnection$IncomingHandler$3.class", "size": 1579, "crc": 377057636}, {"key": "androidx/test/internal/runner/InstrumentationConnection$IncomingHandler$2.class", "name": "androidx/test/internal/runner/InstrumentationConnection$IncomingHandler$2.class", "size": 1557, "crc": -1405694594}, {"key": "androidx/test/internal/runner/InstrumentationConnection$IncomingHandler$1.class", "name": "androidx/test/internal/runner/InstrumentationConnection$IncomingHandler$1.class", "size": 1726, "crc": -1184583519}, {"key": "androidx/test/internal/runner/InstrumentationConnection$1.class", "name": "androidx/test/internal/runner/InstrumentationConnection$1.class", "size": 1378, "crc": 595640618}, {"key": "androidx/test/internal/platform/util/TestOutputHandler.class", "name": "androidx/test/internal/platform/util/TestOutputHandler.class", "size": 508, "crc": -1844620600}, {"key": "androidx/test/internal/platform/util/TestOutputEmitter.class", "name": "androidx/test/internal/platform/util/TestOutputEmitter.class", "size": 2379, "crc": 1434559812}, {"key": "androidx/test/internal/platform/util/TestOutputEmitter$1.class", "name": "androidx/test/internal/platform/util/TestOutputEmitter$1.class", "size": 1272, "crc": -845518396}, {"key": "androidx/test/internal/platform/util/TestOutputEmitter$$ExternalSyntheticLambda0.class", "name": "androidx/test/internal/platform/util/TestOutputEmitter$$ExternalSyntheticLambda0.class", "size": 799, "crc": 249031582}, {"key": "androidx/test/internal/platform/util/InstrumentationParameterUtil.class", "name": "androidx/test/internal/platform/util/InstrumentationParameterUtil.class", "size": 1229, "crc": 581440316}, {"key": "androidx/test/internal/platform/reflect/ReflectiveMethod.class", "name": "androidx/test/internal/platform/reflect/ReflectiveMethod.class", "size": 2896, "crc": 1801514191}, {"key": "androidx/test/internal/platform/reflect/ReflectiveField.class", "name": "androidx/test/internal/platform/reflect/ReflectiveField.class", "size": 2218, "crc": 440692795}, {"key": "androidx/test/internal/platform/reflect/ReflectionException.class", "name": "androidx/test/internal/platform/reflect/ReflectionException.class", "size": 809, "crc": -1506284589}, {"key": "androidx/test/internal/platform/os/ControlledLooper.class", "name": "androidx/test/internal/platform/os/ControlledLooper.class", "size": 826, "crc": 470912913}, {"key": "androidx/test/internal/platform/os/ControlledLooper$1.class", "name": "androidx/test/internal/platform/os/ControlledLooper$1.class", "size": 727, "crc": -1346036559}, {"key": "androidx/test/internal/platform/content/PermissionGranter.class", "name": "androidx/test/internal/platform/content/PermissionGranter.class", "size": 637, "crc": 2087378993}, {"key": "androidx/test/internal/platform/app/ActivityLifecycleTimeout.class", "name": "androidx/test/internal/platform/app/ActivityLifecycleTimeout.class", "size": 1055, "crc": -1092980925}, {"key": "androidx/test/internal/platform/app/ActivityInvoker.class", "name": "androidx/test/internal/platform/app/ActivityInvoker.class", "size": 1364, "crc": -1704484781}, {"key": "androidx/test/internal/platform/app/ActivityInvoker$-CC.class", "name": "androidx/test/internal/platform/app/ActivityInvoker$-CC.class", "size": 1615, "crc": -2082461516}, {"key": "androidx/test/internal/platform/app/ActivityInvoker$$CC.class", "name": "androidx/test/internal/platform/app/ActivityInvoker$$CC.class", "size": 2020, "crc": -779741982}, {"key": "androidx/test/internal/platform/ThreadChecker.class", "name": "androidx/test/internal/platform/ThreadChecker.class", "size": 469, "crc": -668028169}, {"key": "androidx/test/internal/platform/ServiceLoaderWrapper.class", "name": "androidx/test/internal/platform/ServiceLoaderWrapper.class", "size": 3345, "crc": -1722882538}, {"key": "androidx/test/internal/platform/ServiceLoaderWrapper$Factory.class", "name": "androidx/test/internal/platform/ServiceLoaderWrapper$Factory.class", "size": 379, "crc": 1182336067}, {"key": "androidx/test/annotation/Beta.class", "name": "androidx/test/annotation/Beta.class", "size": 499, "crc": 1302023912}, {"key": "androidx/test/InstrumentationRegistry.class", "name": "androidx/test/InstrumentationRegistry.class", "size": 1622, "crc": 1254188033}]