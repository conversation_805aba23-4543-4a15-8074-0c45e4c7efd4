package com.falaileh.nisso.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

/**
 * Floating hearts overlay for romantic atmosphere
 */
@Composable
fun FloatingHeartsOverlay(
    modifier: Modifier = Modifier,
    heartCount: Int = 8,
    animationDurationMs: Int = 8000
) {
    val density = LocalDensity.current
    var hearts by remember { mutableStateOf(emptyList<Heart>()) }
    
    // Animation for heart movement
    val infiniteTransition = rememberInfiniteTransition(label = "hearts_animation")
    val animationProgress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(animationDurationMs, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "hearts_progress"
    )
    
    // Initialize hearts
    LaunchedEffect(Unit) {
        hearts = generateHearts(heartCount)
    }
    
    Canvas(
        modifier = modifier.fillMaxSize()
    ) {
        hearts.forEach { heart ->
            drawHeart(heart, animationProgress, size.width, size.height)
        }
    }
}

/**
 * Data class representing a floating heart
 */
private data class Heart(
    val startX: Float,
    val startY: Float,
    val size: Float,
    val color: Color,
    val speed: Float,
    val swayAmplitude: Float,
    val swayFrequency: Float,
    val rotationSpeed: Float,
    val alpha: Float
)

/**
 * Generate random hearts for animation
 */
private fun generateHearts(count: Int): List<Heart> {
    val romanticColors = listOf(
        Color(0xFFFF6B6B),
        Color(0xFFFF8E8E),
        Color(0xFFFFB3BA),
        Color(0xFFFF69B4),
        Color(0xFFFFC0CB),
        Color(0xFFFFB6C1)
    )
    
    return (0 until count).map {
        Heart(
            startX = Random.nextFloat(),
            startY = 1.2f, // Start below screen
            size = Random.nextFloat() * 20f + 15f,
            color = romanticColors.random(),
            speed = Random.nextFloat() * 0.3f + 0.2f,
            swayAmplitude = Random.nextFloat() * 50f + 30f,
            swayFrequency = Random.nextFloat() * 2f + 1f,
            rotationSpeed = Random.nextFloat() * 2f - 1f,
            alpha = Random.nextFloat() * 0.4f + 0.3f
        )
    }
}

/**
 * Draw a proper heart shape on the canvas
 */
private fun DrawScope.drawHeart(
    heart: Heart,
    progress: Float,
    screenWidth: Float,
    screenHeight: Float
) {
    val currentY = heart.startY * screenHeight - (progress * heart.speed * screenHeight * 1.5f)

    // Reset heart position when it goes off screen
    val y = if (currentY < -100f) {
        screenHeight + 100f - ((currentY + 100f) % (screenHeight + 200f))
    } else {
        currentY
    }

    val swayOffset = sin(progress * heart.swayFrequency * 2 * Math.PI.toFloat()) * heart.swayAmplitude
    val x = heart.startX * screenWidth + swayOffset

    // Draw proper heart shape using path
    val heartSize = heart.size
    val centerX = x
    val centerY = y
    val heartColor = heart.color.copy(alpha = heart.alpha)

    val heartPath = androidx.compose.ui.graphics.Path().apply {
        val width = heartSize
        val height = heartSize

        // Start at bottom point of heart
        moveTo(centerX, centerY + height * 0.3f)

        // Left curve of heart
        cubicTo(
            centerX - width * 0.5f, centerY - height * 0.1f,
            centerX - width * 0.5f, centerY - height * 0.4f,
            centerX - width * 0.25f, centerY - height * 0.4f
        )

        // Top left arc
        cubicTo(
            centerX - width * 0.1f, centerY - height * 0.4f,
            centerX - width * 0.1f, centerY - height * 0.2f,
            centerX, centerY - height * 0.1f
        )

        // Top right arc
        cubicTo(
            centerX + width * 0.1f, centerY - height * 0.2f,
            centerX + width * 0.1f, centerY - height * 0.4f,
            centerX + width * 0.25f, centerY - height * 0.4f
        )

        // Right curve of heart
        cubicTo(
            centerX + width * 0.5f, centerY - height * 0.4f,
            centerX + width * 0.5f, centerY - height * 0.1f,
            centerX, centerY + height * 0.3f
        )

        close()
    }

    drawPath(
        path = heartPath,
        color = heartColor
    )
}

/**
 * Sparkle effect overlay
 */
@Composable
fun SparkleOverlay(
    modifier: Modifier = Modifier,
    sparkleCount: Int = 12
) {
    val infiniteTransition = rememberInfiniteTransition(label = "sparkle_animation")
    
    val sparkleProgress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "sparkle_progress"
    )
    
    val sparkles = remember {
        (0 until sparkleCount).map {
            Sparkle(
                x = Random.nextFloat(),
                y = Random.nextFloat(),
                size = Random.nextFloat() * 4f + 2f,
                twinkleSpeed = Random.nextFloat() * 2f + 1f,
                color = Color.White.copy(alpha = Random.nextFloat() * 0.6f + 0.2f)
            )
        }
    }
    
    Canvas(
        modifier = modifier.fillMaxSize()
    ) {
        sparkles.forEach { sparkle ->
            val alpha = (sin(sparkleProgress * sparkle.twinkleSpeed * 2 * Math.PI.toFloat()) + 1f) / 2f
            drawCircle(
                color = sparkle.color.copy(alpha = alpha * sparkle.color.alpha),
                radius = sparkle.size,
                center = Offset(
                    sparkle.x * size.width,
                    sparkle.y * size.height
                )
            )
        }
    }
}

/**
 * Data class for sparkle effect
 */
private data class Sparkle(
    val x: Float,
    val y: Float,
    val size: Float,
    val twinkleSpeed: Float,
    val color: Color
)
