package com.falaileh.nisso.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\"\u0017\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0002\u0010\u0003\u001a\u0004\b\u0004\u0010\u0005\u00a8\u0006\u0006"}, d2 = {"Typography", "Landroidx/tv/material3/Typography;", "getTypography$annotations", "()V", "getTypography", "()Landroidx/tv/material3/Typography;", "app_release"})
public final class TypeKt {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.tv.material3.Typography Typography = null;
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.tv.material3.Typography getTypography() {
        return null;
    }
    
    @kotlin.OptIn(markerClass = {androidx.tv.material3.ExperimentalTvMaterial3Api.class})
    @java.lang.Deprecated()
    public static void getTypography$annotations() {
    }
}