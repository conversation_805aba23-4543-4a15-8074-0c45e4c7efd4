package com.falaileh.nisso.ui.components;

/**
 * State holder for TV navigation
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0010\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0016\u001a\u00020\u0017J\u000e\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u0004J\u0006\u0010\u001a\u001a\u00020\u0017J\u0006\u0010\u001b\u001a\u00020\u0017R/\u0010\u0005\u001a\u0004\u0018\u00010\u00042\b\u0010\u0003\u001a\u0004\u0018\u00010\u00048F@BX\u0086\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\n\u0010\u000b\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\tR+\u0010\r\u001a\u00020\f2\u0006\u0010\u0003\u001a\u00020\f8F@BX\u0086\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\u0011\u0010\u000b\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010R+\u0010\u0012\u001a\u00020\f2\u0006\u0010\u0003\u001a\u00020\f8F@BX\u0086\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\u0015\u0010\u000b\u001a\u0004\b\u0013\u0010\u000e\"\u0004\b\u0014\u0010\u0010\u00a8\u0006\u001c"}, d2 = {"Lcom/falaileh/nisso/ui/components/TvNavigationState;", "", "()V", "<set-?>", "Lcom/falaileh/nisso/ui/components/NavigationDirection;", "currentDirection", "getCurrentDirection", "()Lcom/falaileh/nisso/ui/components/NavigationDirection;", "setCurrentDirection", "(Lcom/falaileh/nisso/ui/components/NavigationDirection;)V", "currentDirection$delegate", "Landroidx/compose/runtime/MutableState;", "", "isNavigating", "()Z", "setNavigating", "(Z)V", "isNavigating$delegate", "showHints", "getShowHints", "setShowHints", "showHints$delegate", "hideHints", "", "startNavigation", "direction", "stopNavigation", "toggleHints", "app_release"})
public final class TvNavigationState {
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.runtime.MutableState isNavigating$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.runtime.MutableState currentDirection$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.runtime.MutableState showHints$delegate = null;
    
    public TvNavigationState() {
        super();
    }
    
    public final boolean isNavigating() {
        return false;
    }
    
    private final void setNavigating(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.falaileh.nisso.ui.components.NavigationDirection getCurrentDirection() {
        return null;
    }
    
    private final void setCurrentDirection(com.falaileh.nisso.ui.components.NavigationDirection p0) {
    }
    
    public final boolean getShowHints() {
        return false;
    }
    
    private final void setShowHints(boolean p0) {
    }
    
    public final void startNavigation(@org.jetbrains.annotations.NotNull()
    com.falaileh.nisso.ui.components.NavigationDirection direction) {
    }
    
    public final void stopNavigation() {
    }
    
    public final void toggleHints() {
    }
    
    public final void hideHints() {
    }
}