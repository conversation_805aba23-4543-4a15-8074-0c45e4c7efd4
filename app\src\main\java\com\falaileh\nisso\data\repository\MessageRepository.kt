package com.falaileh.nisso.data.repository

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import com.falaileh.nisso.data.api.LoveMessageApiService
import com.falaileh.nisso.data.api.NetworkModule
import com.falaileh.nisso.data.database.LoveMessageDatabase
import com.falaileh.nisso.data.model.LoveMessage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.io.IOException

/**
 * Repository for managing love messages from API and local database
 */
class MessageRepository(private val context: Context) {
    
    private val apiService: LoveMessageApiService = NetworkModule.apiService
    private val database = LoveMessageDatabase.getDatabase(context)
    private val dao = database.loveMessageDao()
    
    /**
     * Get messages from local database as Flow
     */
    fun getMessagesFlow(): Flow<List<LoveMessage>> = dao.getAllMessages()
    
    /**
     * Get messages from local database synchronously
     */
    suspend fun getLocalMessages(): List<LoveMessage> = dao.getAllMessagesSync()
    
    /**
     * Fetch messages from API only (no local caching)
     */
    suspend fun refreshMessages(): Result<List<LoveMessage>> {
        return try {
            if (!isNetworkAvailable()) {
                return Result.failure(IOException("No internet connection"))
            }

            val response = apiService.getLoveMessages()

            if (response.isSuccessful && response.body() != null) {
                val apiResponse = response.body()!!

                if (apiResponse.success && apiResponse.messages.isNotEmpty()) {
                    // Return messages directly without caching
                    Result.success(apiResponse.messages)
                } else {
                    Result.failure(Exception("No messages available from API"))
                }
            } else {
                Result.failure(Exception("API call failed: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get messages from API only (no caching)
     */
    suspend fun getMessages(): Flow<Result<List<LoveMessage>>> = flow {
        try {
            // Only get fresh messages from API
            val refreshResult = refreshMessages()
            emit(refreshResult)
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }
    
    /**
     * Check if network is available
     */
    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        
        return networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
    }
    
    // Removed caching functionality - app now only uses live API data
}
