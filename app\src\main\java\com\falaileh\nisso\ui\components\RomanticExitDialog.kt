package com.falaileh.nisso.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.tv.material3.ExperimentalTvMaterial3Api
import androidx.tv.material3.Text

/**
 * Romantic exit confirmation dialog with TV navigation
 */
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun RomanticExitDialog(
    onConfirmExit: () -> Unit,
    onCancelExit: () -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedOption by remember { mutableStateOf(1) } // 0 = Exit, 1 = Stay
    val confirmFocusRequester = remember { FocusRequester() }
    val cancelFocusRequester = remember { FocusRequester() }
    
    // Heart pulse animation
    val heartScale by rememberInfiniteTransition(label = "heart_pulse").animateFloat(
        initialValue = 1f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "heart_scale"
    )
    
    Dialog(
        onDismissRequest = onCancelExit,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(Color.Transparent)
                .onKeyEvent { keyEvent ->
                    if (keyEvent.type == KeyEventType.KeyDown) {
                        when (keyEvent.key) {
                            Key.DirectionLeft -> {
                                selectedOption = 0 // Exit
                                true
                            }
                            Key.DirectionRight -> {
                                selectedOption = 1 // Stay
                                true
                            }
                            Key.Enter, Key.DirectionCenter -> {
                                if (selectedOption == 0) {
                                    onConfirmExit()
                                } else {
                                    onCancelExit()
                                }
                                true
                            }
                            Key.Back -> {
                                onCancelExit()
                                true
                            }
                            else -> false
                        }
                    } else false
                }
                .focusable(),
            contentAlignment = Alignment.Center
        ) {
            // Floating card with backdrop blur effect
            Box(
                modifier = Modifier
                    .wrapContentSize()
                    .clip(RoundedCornerShape(20.dp))
                    .background(Color.Black.copy(alpha = 0.85f))
                    .border(
                        width = 2.dp,
                        color = Color(0xFFFF6B6B).copy(alpha = 0.8f),
                        shape = RoundedCornerShape(20.dp)
                    )
                    .padding(32.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Animated heart
                    Text(
                        text = "💔",
                        fontSize = 48.sp,
                        modifier = Modifier.scale(heartScale)
                    )

                    // Custom romantic message
                    Text(
                        text = "Don't forget .. love you 💕💉",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.White,
                        textAlign = TextAlign.Center,
                        lineHeight = 24.sp
                    )
                
                    // Action buttons
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(24.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Exit button
                        RomanticButton(
                            text = "Exit",
                            isSelected = selectedOption == 0,
                            onClick = onConfirmExit,
                            backgroundColor = Color(0xFFFF6B6B),
                            modifier = Modifier.focusRequester(confirmFocusRequester)
                        )

                        // Stay button
                        RomanticButton(
                            text = "Stay 💕",
                            isSelected = selectedOption == 1,
                            onClick = onCancelExit,
                            backgroundColor = Color(0xFF4ECDC4),
                            modifier = Modifier.focusRequester(cancelFocusRequester)
                        )
                    }

                    // Navigation hint
                    Text(
                        text = "← → SELECT • ENTER CONFIRM",
                        fontSize = 12.sp,
                        color = Color.White.copy(alpha = 0.6f),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
    
    // Auto-focus the "Stay" button (default choice)
    LaunchedEffect(Unit) {
        if (selectedOption == 1) {
            cancelFocusRequester.requestFocus()
        } else {
            confirmFocusRequester.requestFocus()
        }
    }
}

/**
 * Romantic button component for the dialog
 */
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
private fun RomanticButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    backgroundColor: Color,
    modifier: Modifier = Modifier
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        animationSpec = tween(200),
        label = "button_scale"
    )
    
    val borderAlpha by animateFloatAsState(
        targetValue = if (isSelected) 1f else 0.3f,
        animationSpec = tween(200),
        label = "border_alpha"
    )
    
    Box(
        modifier = modifier
            .scale(scale)
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor.copy(alpha = 0.8f))
            .border(
                width = 2.dp,
                color = Color.White.copy(alpha = borderAlpha),
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 20.dp, vertical = 12.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }
}
