package com.falaileh.nisso.data.api;

/**
 * Retrofit API service interface for love messages
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bf\u0018\u0000 \u00062\u00020\u0001:\u0001\u0006J\u0014\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/falaileh/nisso/data/api/LoveMessageApiService;", "", "getLoveMessages", "Lretrofit2/Response;", "Lcom/falaileh/nisso/data/model/LoveMessageResponse;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public abstract interface LoveMessageApiService {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String BASE_URL = "https://mbfjo.com/loveapp/";
    @org.jetbrains.annotations.NotNull()
    public static final com.falaileh.nisso.data.api.LoveMessageApiService.Companion Companion = null;
    
    @retrofit2.http.GET(value = "api.php")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLoveMessages(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.falaileh.nisso.data.model.LoveMessageResponse>> $completion);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/falaileh/nisso/data/api/LoveMessageApiService$Companion;", "", "()V", "BASE_URL", "", "app_debug"})
    public static final class Companion {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String BASE_URL = "https://mbfjo.com/loveapp/";
        
        private Companion() {
            super();
        }
    }
}