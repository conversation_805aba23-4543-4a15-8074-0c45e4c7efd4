{"logs": [{"outputFile": "com.falaileh.nisso.app-mergeReleaseResources-54:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\910260a50c4cc0fe03b922548d59c7fb\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,61", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2821,2920,3022,3122,3220,3327,3433,5616", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "2915,3017,3117,3215,3322,3428,3548,5712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\54903ff84a4690cb020fc1e9d9860152\\transformed\\media3-exoplayer-1.0.0-beta03\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4024,4099,4162,4227,4296,4373,4447,4536,4624", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "4094,4157,4222,4291,4368,4442,4531,4619,4688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8dca2d17bb97bd73368ea919040cc370\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,1017,1107,1184,1260,1340,1416,1494,1564", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,75,79,75,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,1012,1102,1179,1255,1335,1411,1489,1559,1682"}, "to": {"startLines": "36,37,38,39,40,50,51,52,53,54,55,57,58,59,60,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3553,3652,3734,3832,3935,4693,4772,4868,4960,5047,5134,5307,5384,5460,5540,5717,5795,5865", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,75,79,75,77,69,122", "endOffsets": "3647,3729,3827,3930,4019,4767,4863,4955,5042,5129,5219,5379,5455,5535,5611,5790,5860,5983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9360fea9cc06fc380f90ebb846dd3e6a\\transformed\\foundation-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "65,66", "startColumns": "4,4", "startOffsets": "5988,6088", "endColumns": "99,101", "endOffsets": "6083,6185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85e1e1d8941ac4fc444c1da209e0c205\\transformed\\appcompat-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,5224", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,5302"}}]}]}