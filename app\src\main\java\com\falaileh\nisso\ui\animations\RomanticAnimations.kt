package com.falaileh.nisso.ui.animations

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.rotate
import kotlinx.coroutines.delay
import kotlin.math.*
import kotlin.random.Random

/**
 * Romantic loading animation with hearts
 */
@Composable
fun RomanticLoadingAnimation(
    modifier: Modifier = Modifier,
    color: Color = Color(0xFFFF6B6B)
) {
    val infiniteTransition = rememberInfiniteTransition(label = "loading_animation")
    
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "loading_rotation"
    )
    
    val scale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "loading_scale"
    )
    
    Canvas(modifier = modifier.fillMaxSize()) {
        val center = Offset(size.width / 2f, size.height / 2f)
        val heartSize = 30f * scale
        
        // Draw rotating hearts in a circle
        for (i in 0 until 8) {
            val angle = (rotation + i * 45f) * PI / 180f
            val radius = 80f
            val x = center.x + cos(angle).toFloat() * radius
            val y = center.y + sin(angle).toFloat() * radius
            
            rotate(rotation + i * 45f, Offset(x, y)) {
                drawHeart(Offset(x, y), heartSize, color.copy(alpha = 0.8f))
            }
        }
        
        // Center pulsing heart
        drawHeart(center, heartSize * 1.5f, color)
    }
}

/**
 * Background color transition animation
 */
@Composable
fun animateBackgroundColor(
    targetColor: Color,
    durationMs: Int = 1500
): State<Color> {
    return animateColorAsState(
        targetValue = targetColor,
        animationSpec = tween<Color>(
            durationMillis = durationMs,
            easing = FastOutSlowInEasing
        ),
        label = "background_color"
    )
}

/**
 * Romantic pulse animation for text
 */
@Composable
fun rememberPulseAnimation(
    durationMs: Int = 2000
): State<Float> {
    val infiniteTransition = rememberInfiniteTransition(label = "pulse_animation")
    
    return infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1.1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMs, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_scale"
    )
}

/**
 * Romantic shimmer effect
 */
@Composable
fun RomanticShimmerEffect(
    modifier: Modifier = Modifier,
    colors: List<Color> = listOf(
        Color.Transparent,
        Color.White.copy(alpha = 0.3f),
        Color.Transparent
    )
) {
    val infiniteTransition = rememberInfiniteTransition(label = "shimmer_animation")
    
    val shimmerProgress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "shimmer_progress"
    )
    
    Canvas(modifier = modifier.fillMaxSize()) {
        val gradient = Brush.linearGradient(
            colors = colors,
            start = Offset(size.width * (shimmerProgress - 0.3f), 0f),
            end = Offset(size.width * shimmerProgress, size.height)
        )
        
        drawRect(
            brush = gradient,
            size = size
        )
    }
}

/**
 * Floating particles animation
 */
@Composable
fun FloatingParticlesAnimation(
    modifier: Modifier = Modifier,
    particleCount: Int = 20,
    colors: List<Color> = listOf(
        Color(0xFFFF6B6B),
        Color(0xFFFFB3BA),
        Color(0xFFFFC0CB),
        Color(0xFFFFE4E1)
    )
) {
    val particles = remember {
        (0 until particleCount).map {
            Particle(
                x = Random.nextFloat(),
                y = Random.nextFloat(),
                size = Random.nextFloat() * 8f + 2f,
                speed = Random.nextFloat() * 0.5f + 0.1f,
                color = colors.random(),
                phase = Random.nextFloat() * 2f * PI.toFloat()
            )
        }
    }
    
    val infiniteTransition = rememberInfiniteTransition(label = "particles_animation")
    
    val animationProgress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(10000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "particles_progress"
    )
    
    Canvas(modifier = modifier.fillMaxSize()) {
        particles.forEach { particle ->
            val currentY = (particle.y + animationProgress * particle.speed) % 1f
            val currentX = particle.x + sin(animationProgress * 2f + particle.phase) * 0.1f
            
            val alpha = sin(animationProgress * 3f + particle.phase) * 0.5f + 0.5f
            
            drawCircle(
                color = particle.color.copy(alpha = alpha * 0.6f),
                radius = particle.size,
                center = Offset(
                    currentX * size.width,
                    currentY * size.height
                )
            )
        }
    }
}

/**
 * Message transition animation
 */
@Composable
fun rememberMessageTransition(): MessageTransitionState {
    return remember { MessageTransitionState() }
}

class MessageTransitionState {
    var isTransitioning by mutableStateOf(false)
        private set
    
    var transitionProgress by mutableStateOf(0f)
        private set
    
    suspend fun startTransition(durationMs: Long = 800L) {
        isTransitioning = true
        
        val steps = 60
        val stepDuration = durationMs / steps
        
        for (i in 0..steps) {
            transitionProgress = i.toFloat() / steps
            delay(stepDuration)
        }
        
        isTransitioning = false
        transitionProgress = 0f
    }
}

/**
 * Romantic glow effect
 */
@Composable
fun RomanticGlowEffect(
    modifier: Modifier = Modifier,
    glowColor: Color = Color(0xFFFF6B6B),
    intensity: Float = 0.5f
) {
    val infiniteTransition = rememberInfiniteTransition(label = "glow_animation")
    
    val glowIntensity by infiniteTransition.animateFloat(
        initialValue = intensity * 0.5f,
        targetValue = intensity,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glow_intensity"
    )
    
    Canvas(modifier = modifier.fillMaxSize()) {
        val center = Offset(size.width / 2f, size.height / 2f)
        val maxRadius = maxOf(size.width, size.height) / 2f
        
        val gradient = Brush.radialGradient(
            colors = listOf(
                glowColor.copy(alpha = glowIntensity),
                glowColor.copy(alpha = glowIntensity * 0.5f),
                Color.Transparent
            ),
            center = center,
            radius = maxRadius
        )
        
        drawCircle(
            brush = gradient,
            radius = maxRadius,
            center = center
        )
    }
}

/**
 * Helper function to draw proper heart shape
 */
private fun DrawScope.drawHeart(
    center: Offset,
    size: Float,
    color: Color
) {
    val heartPath = Path().apply {
        val width = size
        val height = size

        // Start at bottom point of heart
        moveTo(center.x, center.y + height * 0.3f)

        // Left curve of heart
        cubicTo(
            center.x - width * 0.5f, center.y - height * 0.1f,
            center.x - width * 0.5f, center.y - height * 0.4f,
            center.x - width * 0.25f, center.y - height * 0.4f
        )

        // Top left arc
        cubicTo(
            center.x - width * 0.1f, center.y - height * 0.4f,
            center.x - width * 0.1f, center.y - height * 0.2f,
            center.x, center.y - height * 0.1f
        )

        // Top right arc
        cubicTo(
            center.x + width * 0.1f, center.y - height * 0.2f,
            center.x + width * 0.1f, center.y - height * 0.4f,
            center.x + width * 0.25f, center.y - height * 0.4f
        )

        // Right curve of heart
        cubicTo(
            center.x + width * 0.5f, center.y - height * 0.4f,
            center.x + width * 0.5f, center.y - height * 0.1f,
            center.x, center.y + height * 0.3f
        )

        close()
    }

    drawPath(
        path = heartPath,
        color = color
    )
}

/**
 * Data class for floating particles
 */
private data class Particle(
    val x: Float,
    val y: Float,
    val size: Float,
    val speed: Float,
    val color: Color,
    val phase: Float
)
