[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 95, "crc": -1935940547}, {"key": "javax/annotation/CheckForNull.class", "name": "javax/annotation/CheckForNull.class", "size": 518, "crc": 494455401}, {"key": "javax/annotation/CheckForSigned.class", "name": "javax/annotation/CheckForSigned.class", "size": 526, "crc": -922402916}, {"key": "javax/annotation/CheckReturnValue.class", "name": "javax/annotation/CheckReturnValue.class", "size": 607, "crc": -1898020793}, {"key": "javax/annotation/Detainted.class", "name": "javax/annotation/Detainted.class", "size": 515, "crc": 1268316728}, {"key": "javax/annotation/MatchesPattern$Checker.class", "name": "javax/annotation/MatchesPattern$Checker.class", "size": 1550, "crc": -1643335364}, {"key": "javax/annotation/MatchesPattern.class", "name": "javax/annotation/MatchesPattern.class", "size": 643, "crc": -2142883496}, {"key": "javax/annotation/Nonnegative$Checker.class", "name": "javax/annotation/Nonnegative$Checker.class", "size": 1594, "crc": -1641219598}, {"key": "javax/annotation/Nonnegative.class", "name": "javax/annotation/Nonnegative.class", "size": 627, "crc": 2101765137}, {"key": "javax/annotation/Nonnull$Checker.class", "name": "javax/annotation/Nonnull$Checker.class", "size": 1139, "crc": -1475578214}, {"key": "javax/annotation/Nonnull.class", "name": "javax/annotation/Nonnull.class", "size": 574, "crc": 1454166174}, {"key": "javax/annotation/Nullable.class", "name": "javax/annotation/Nullable.class", "size": 512, "crc": 746386018}, {"key": "javax/annotation/OverridingMethodsMustInvokeSuper.class", "name": "javax/annotation/OverridingMethodsMustInvokeSuper.class", "size": 474, "crc": 552659504}, {"key": "javax/annotation/ParametersAreNonnullByDefault.class", "name": "javax/annotation/ParametersAreNonnullByDefault.class", "size": 519, "crc": 667248111}, {"key": "javax/annotation/PropertyKey.class", "name": "javax/annotation/PropertyKey.class", "size": 503, "crc": -*********}, {"key": "javax/annotation/RegEx$Checker.class", "name": "javax/annotation/RegEx$Checker.class", "size": 1372, "crc": -*********}, {"key": "javax/annotation/RegEx.class", "name": "javax/annotation/RegEx.class", "size": 659, "crc": -1597889481}, {"key": "javax/annotation/Signed.class", "name": "javax/annotation/Signed.class", "size": 512, "crc": *********}, {"key": "javax/annotation/Syntax.class", "name": "javax/annotation/Syntax.class", "size": 565, "crc": *********}, {"key": "javax/annotation/Tainted.class", "name": "javax/annotation/Tainted.class", "size": 510, "crc": 1823738814}, {"key": "javax/annotation/Untainted.class", "name": "javax/annotation/Untainted.class", "size": 499, "crc": -1914956567}, {"key": "javax/annotation/WillClose.class", "name": "javax/annotation/WillClose.class", "size": 336, "crc": -*********}, {"key": "javax/annotation/WillCloseWhenClosed.class", "name": "javax/annotation/WillCloseWhenClosed.class", "size": 356, "crc": -*********}, {"key": "javax/annotation/WillNotClose.class", "name": "javax/annotation/WillNotClose.class", "size": 342, "crc": 1441664687}, {"key": "javax/annotation/concurrent/GuardedBy.class", "name": "javax/annotation/concurrent/GuardedBy.class", "size": 441, "crc": -*********}, {"key": "javax/annotation/concurrent/Immutable.class", "name": "javax/annotation/concurrent/Immutable.class", "size": 435, "crc": 2007743002}, {"key": "javax/annotation/concurrent/NotThreadSafe.class", "name": "javax/annotation/concurrent/NotThreadSafe.class", "size": 443, "crc": 1921721169}, {"key": "javax/annotation/concurrent/ThreadSafe.class", "name": "javax/annotation/concurrent/ThreadSafe.class", "size": 437, "crc": 2078530901}, {"key": "javax/annotation/meta/Exclusive.class", "name": "javax/annotation/meta/Exclusive.class", "size": 341, "crc": 1615223597}, {"key": "javax/annotation/meta/Exhaustive.class", "name": "javax/annotation/meta/Exhaustive.class", "size": 343, "crc": -1378103656}, {"key": "javax/annotation/meta/TypeQualifier.class", "name": "javax/annotation/meta/TypeQualifier.class", "size": 590, "crc": 697759296}, {"key": "javax/annotation/meta/TypeQualifierDefault.class", "name": "javax/annotation/meta/TypeQualifierDefault.class", "size": 541, "crc": 1011810305}, {"key": "javax/annotation/meta/TypeQualifierNickname.class", "name": "javax/annotation/meta/TypeQualifierNickname.class", "size": 369, "crc": 641828811}, {"key": "javax/annotation/meta/TypeQualifierValidator.class", "name": "javax/annotation/meta/TypeQualifierValidator.class", "size": 520, "crc": 1767865458}, {"key": "javax/annotation/meta/When.class", "name": "javax/annotation/meta/When.class", "size": 1081, "crc": 637871657}, {"key": "javax/annotation/CheckForNull.java", "name": "javax/annotation/CheckForNull.java", "size": 375, "crc": 644808890}, {"key": "javax/annotation/CheckForSigned.java", "name": "javax/annotation/CheckForSigned.java", "size": 698, "crc": -651941188}, {"key": "javax/annotation/CheckReturnValue.java", "name": "javax/annotation/CheckReturnValue.java", "size": 494, "crc": 761173099}, {"key": "javax/annotation/Detainted.java", "name": "javax/annotation/Detainted.java", "size": 375, "crc": -1094912357}, {"key": "javax/annotation/MatchesPattern.java", "name": "javax/annotation/MatchesPattern.java", "size": 883, "crc": 974170160}, {"key": "javax/annotation/Nonnegative.java", "name": "javax/annotation/Nonnegative.java", "size": 1303, "crc": 1661976749}, {"key": "javax/annotation/Nonnull.java", "name": "javax/annotation/Nonnull.java", "size": 706, "crc": -1116704706}, {"key": "javax/annotation/Nullable.java", "name": "javax/annotation/Nullable.java", "size": 373, "crc": -816379449}, {"key": "javax/annotation/OverridingMethodsMustInvokeSuper.java", "name": "javax/annotation/OverridingMethodsMustInvokeSuper.java", "size": 580, "crc": -1521465386}, {"key": "javax/annotation/ParametersAreNonnullByDefault.java", "name": "javax/annotation/ParametersAreNonnullByDefault.java", "size": 866, "crc": -*********}, {"key": "javax/annotation/PropertyKey.java", "name": "javax/annotation/PropertyKey.java", "size": 366, "crc": 1370743949}, {"key": "javax/annotation/RegEx.java", "name": "javax/annotation/RegEx.java", "size": 1064, "crc": -*********}, {"key": "javax/annotation/Signed.java", "name": "javax/annotation/Signed.java", "size": 424, "crc": -*********}, {"key": "javax/annotation/Syntax.java", "name": "javax/annotation/Syntax.java", "size": 1406, "crc": 1799001529}, {"key": "javax/annotation/Tainted.java", "name": "javax/annotation/Tainted.java", "size": 372, "crc": -1775266045}, {"key": "javax/annotation/Untainted.java", "name": "javax/annotation/Untainted.java", "size": 364, "crc": -*********}, {"key": "javax/annotation/WillClose.java", "name": "javax/annotation/WillClose.java", "size": 337, "crc": 1528772292}, {"key": "javax/annotation/WillCloseWhenClosed.java", "name": "javax/annotation/WillCloseWhenClosed.java", "size": 385, "crc": -*********}, {"key": "javax/annotation/WillNotClose.java", "name": "javax/annotation/WillNotClose.java", "size": 344, "crc": 1598901612}, {"key": "javax/annotation/concurrent/GuardedBy.java", "name": "javax/annotation/concurrent/GuardedBy.java", "size": 1611, "crc": -1001210258}, {"key": "javax/annotation/concurrent/Immutable.java", "name": "javax/annotation/concurrent/Immutable.java", "size": 1333, "crc": 1107471227}, {"key": "javax/annotation/concurrent/NotThreadSafe.java", "name": "javax/annotation/concurrent/NotThreadSafe.java", "size": 892, "crc": 1175845665}, {"key": "javax/annotation/concurrent/ThreadSafe.java", "name": "javax/annotation/concurrent/ThreadSafe.java", "size": 752, "crc": 2105265368}, {"key": "javax/annotation/meta/Exclusive.java", "name": "javax/annotation/meta/Exclusive.java", "size": 650, "crc": -2102595138}, {"key": "javax/annotation/meta/Exhaustive.java", "name": "javax/annotation/meta/Exhaustive.java", "size": 1097, "crc": 51790900}, {"key": "javax/annotation/meta/TypeQualifier.java", "name": "javax/annotation/meta/TypeQualifier.java", "size": 796, "crc": -1382501031}, {"key": "javax/annotation/meta/TypeQualifierDefault.java", "name": "javax/annotation/meta/TypeQualifierDefault.java", "size": 590, "crc": 2055384079}, {"key": "javax/annotation/meta/TypeQualifierNickname.java", "name": "javax/annotation/meta/TypeQualifierNickname.java", "size": 822, "crc": -544782671}, {"key": "javax/annotation/meta/TypeQualifierValidator.java", "name": "javax/annotation/meta/TypeQualifierValidator.java", "size": 687, "crc": 1870901072}, {"key": "javax/annotation/meta/When.java", "name": "javax/annotation/meta/When.java", "size": 639, "crc": -2084870089}]