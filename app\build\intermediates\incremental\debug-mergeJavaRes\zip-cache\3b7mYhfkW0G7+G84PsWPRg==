[{"key": "androidx/compose/foundation/layout/AddedInsets.class", "name": "androidx/compose/foundation/layout/AddedInsets.class", "size": 2934, "crc": -811729543}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$alignmentLineOffsetMeasure$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$alignmentLineOffsetMeasure$1.class", "size": 3004, "crc": -1352026872}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-4j6BHR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-4j6BHR0$$inlined$debugInspectorInfo$1.class", "size": 3272, "crc": -1439700292}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-Y_r0B1c$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-Y_r0B1c$$inlined$debugInspectorInfo$1.class", "size": 3287, "crc": 1051655981}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt.class", "size": 9698, "crc": -798316957}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetDpElement.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetDpElement.class", "size": 5685, "crc": 884055174}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetDpNode.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetDpNode.class", "size": 3255, "crc": -175190270}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitElement.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitElement.class", "size": 5188, "crc": 24464800}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitNode.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitNode.class", "size": 3902, "crc": -**********}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider$Block.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider$Block.class", "size": 3857, "crc": **********}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider$Value.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider$Value.class", "size": 3268, "crc": -134238460}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider.class", "size": 1568, "crc": -36230199}, {"key": "androidx/compose/foundation/layout/AndroidFlingSpline$FlingResult.class", "name": "androidx/compose/foundation/layout/AndroidFlingSpline$FlingResult.class", "size": 4089, "crc": **********}, {"key": "androidx/compose/foundation/layout/AndroidFlingSpline.class", "name": "androidx/compose/foundation/layout/AndroidFlingSpline.class", "size": 3718, "crc": **********}, {"key": "androidx/compose/foundation/layout/AndroidWindowInsets.class", "name": "androidx/compose/foundation/layout/AndroidWindowInsets.class", "size": 6131, "crc": -6775225}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Center$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Center$1.class", "size": 1833, "crc": -442127141}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Left$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Left$1.class", "size": 1828, "crc": **********}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Right$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Right$1.class", "size": 1837, "crc": 518350846}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceAround$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceAround$1.class", "size": 1853, "crc": 2098153500}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceBetween$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceBetween$1.class", "size": 1857, "crc": 2119394810}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceEvenly$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceEvenly$1.class", "size": 1853, "crc": 1239335962}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$aligned$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$aligned$1.class", "size": 2232, "crc": -812151617}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$1.class", "size": 2244, "crc": 152679370}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$2.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$2.class", "size": 2185, "crc": -2056068360}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute.class", "size": 6415, "crc": -1588842174}, {"key": "androidx/compose/foundation/layout/Arrangement$Bottom$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Bottom$1.class", "size": 1586, "crc": 1803815771}, {"key": "androidx/compose/foundation/layout/Arrangement$Center$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Center$1.class", "size": 3147, "crc": -783576337}, {"key": "androidx/compose/foundation/layout/Arrangement$End$1.class", "name": "androidx/compose/foundation/layout/Arrangement$End$1.class", "size": 1910, "crc": 1283330564}, {"key": "androidx/compose/foundation/layout/Arrangement$Horizontal$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$Horizontal$DefaultImpls.class", "size": 915, "crc": 1118790077}, {"key": "androidx/compose/foundation/layout/Arrangement$Horizontal.class", "name": "androidx/compose/foundation/layout/Arrangement$Horizontal.class", "size": 2404, "crc": 2120744057}, {"key": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical$DefaultImpls.class", "size": 965, "crc": -1721520488}, {"key": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical.class", "name": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical.class", "size": 2334, "crc": -2052107497}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceAround$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceAround$1.class", "size": 3187, "crc": -540291642}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceBetween$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceBetween$1.class", "size": 3195, "crc": 1922303546}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceEvenly$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceEvenly$1.class", "size": 3187, "crc": -926029258}, {"key": "androidx/compose/foundation/layout/Arrangement$SpacedAligned.class", "name": "androidx/compose/foundation/layout/Arrangement$SpacedAligned.class", "size": 8460, "crc": -1538518671}, {"key": "androidx/compose/foundation/layout/Arrangement$Start$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Start$1.class", "size": 1916, "crc": -967891837}, {"key": "androidx/compose/foundation/layout/Arrangement$Top$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Top$1.class", "size": 1571, "crc": -699887751}, {"key": "androidx/compose/foundation/layout/Arrangement$Vertical$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$Vertical$DefaultImpls.class", "size": 905, "crc": -236069378}, {"key": "androidx/compose/foundation/layout/Arrangement$Vertical.class", "name": "androidx/compose/foundation/layout/Arrangement$Vertical.class", "size": 2254, "crc": -901308484}, {"key": "androidx/compose/foundation/layout/Arrangement$aligned$1.class", "name": "androidx/compose/foundation/layout/Arrangement$aligned$1.class", "size": 2134, "crc": 1832446804}, {"key": "androidx/compose/foundation/layout/Arrangement$aligned$2.class", "name": "androidx/compose/foundation/layout/Arrangement$aligned$2.class", "size": 2075, "crc": -2021087795}, {"key": "androidx/compose/foundation/layout/Arrangement$spacedBy$1.class", "name": "androidx/compose/foundation/layout/Arrangement$spacedBy$1.class", "size": 2290, "crc": -1059302418}, {"key": "androidx/compose/foundation/layout/Arrangement$spacedBy$2.class", "name": "androidx/compose/foundation/layout/Arrangement$spacedBy$2.class", "size": 2146, "crc": 654182400}, {"key": "androidx/compose/foundation/layout/Arrangement$spacedBy$3.class", "name": "androidx/compose/foundation/layout/Arrangement$spacedBy$3.class", "size": 2087, "crc": 2139226108}, {"key": "androidx/compose/foundation/layout/Arrangement.class", "name": "androidx/compose/foundation/layout/Arrangement.class", "size": 17338, "crc": 210935702}, {"key": "androidx/compose/foundation/layout/AspectRatioElement.class", "name": "androidx/compose/foundation/layout/AspectRatioElement.class", "size": 4901, "crc": 1345388090}, {"key": "androidx/compose/foundation/layout/AspectRatioKt$aspectRatio$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AspectRatioKt$aspectRatio$$inlined$debugInspectorInfo$1.class", "size": 3076, "crc": 1464458797}, {"key": "androidx/compose/foundation/layout/AspectRatioKt.class", "name": "androidx/compose/foundation/layout/AspectRatioKt.class", "size": 2608, "crc": 148537336}, {"key": "androidx/compose/foundation/layout/AspectRatioNode$measure$1.class", "name": "androidx/compose/foundation/layout/AspectRatioNode$measure$1.class", "size": 1953, "crc": 1551689492}, {"key": "androidx/compose/foundation/layout/AspectRatioNode.class", "name": "androidx/compose/foundation/layout/AspectRatioNode.class", "size": 10477, "crc": 426761337}, {"key": "androidx/compose/foundation/layout/BoxChildDataElement.class", "name": "androidx/compose/foundation/layout/BoxChildDataElement.class", "size": 4229, "crc": 1572912022}, {"key": "androidx/compose/foundation/layout/BoxChildDataNode.class", "name": "androidx/compose/foundation/layout/BoxChildDataNode.class", "size": 2294, "crc": 502474037}, {"key": "androidx/compose/foundation/layout/BoxKt$Box$2.class", "name": "androidx/compose/foundation/layout/BoxKt$Box$2.class", "size": 1669, "crc": -1904307695}, {"key": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1$1.class", "size": 1737, "crc": -822923401}, {"key": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1.class", "name": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1.class", "size": 2151, "crc": -1883931393}, {"key": "androidx/compose/foundation/layout/BoxKt.class", "name": "androidx/compose/foundation/layout/BoxKt.class", "size": 16336, "crc": 1838777245}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$1.class", "size": 1706, "crc": -1578372564}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$2.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$2.class", "size": 2853, "crc": 1805125805}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$5.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$5.class", "size": 4890, "crc": -703444555}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy.class", "size": 8425, "crc": 603527251}, {"key": "androidx/compose/foundation/layout/BoxScope.class", "name": "androidx/compose/foundation/layout/BoxScope.class", "size": 1071, "crc": 279748067}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance$align$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance$align$$inlined$debugInspectorInfo$1.class", "size": 2732, "crc": 2070949685}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance$matchParentSize$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance$matchParentSize$$inlined$debugInspectorInfo$1.class", "size": 2585, "crc": 927568329}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance.class", "size": 3514, "crc": 1767300928}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1$measurables$1.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1$measurables$1.class", "size": 3274, "crc": 1987144519}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1.class", "size": 4505, "crc": -1974102435}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$2.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$2.class", "size": 2612, "crc": -2009515386}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt.class", "size": 6320, "crc": 1302590694}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsScope.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsScope.class", "size": 1146, "crc": 2055382071}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsScopeImpl.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsScopeImpl.class", "size": 6624, "crc": 52665767}, {"key": "androidx/compose/foundation/layout/ColumnKt.class", "name": "androidx/compose/foundation/layout/ColumnKt.class", "size": 11183, "crc": -1602510629}, {"key": "androidx/compose/foundation/layout/ColumnMeasurePolicy$placeHelper$1$1.class", "name": "androidx/compose/foundation/layout/ColumnMeasurePolicy$placeHelper$1$1.class", "size": 4434, "crc": -192568973}, {"key": "androidx/compose/foundation/layout/ColumnMeasurePolicy.class", "name": "androidx/compose/foundation/layout/ColumnMeasurePolicy.class", "size": 11298, "crc": -1171046190}, {"key": "androidx/compose/foundation/layout/ColumnScope$DefaultImpls.class", "name": "androidx/compose/foundation/layout/ColumnScope$DefaultImpls.class", "size": 605, "crc": 1467583531}, {"key": "androidx/compose/foundation/layout/ColumnScope.class", "name": "androidx/compose/foundation/layout/ColumnScope.class", "size": 2644, "crc": 1632299538}, {"key": "androidx/compose/foundation/layout/ColumnScopeInstance.class", "name": "androidx/compose/foundation/layout/ColumnScopeInstance.class", "size": 4455, "crc": 1546936897}, {"key": "androidx/compose/foundation/layout/ConsumedInsetsModifier.class", "name": "androidx/compose/foundation/layout/ConsumedInsetsModifier.class", "size": 3001, "crc": -52517804}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3781, "crc": 1854534408}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "size": 3123, "crc": 964603547}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3853, "crc": 1919597222}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "size": 3261, "crc": -2044534949}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3848, "crc": 1895132481}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "size": 3256, "crc": 666844876}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion.class", "size": 9124, "crc": -403663422}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow.class", "size": 3790, "crc": 2012686032}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScope.class", "size": 803, "crc": 2127209947}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScopeImpl.class", "size": 4093, "crc": 561838430}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnScope.class", "size": 1157, "crc": 907761579}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnScopeImpl.class", "size": 4571, "crc": -441642712}, {"key": "androidx/compose/foundation/layout/ContextualFlowItemIterator.class", "name": "androidx/compose/foundation/layout/ContextualFlowItemIterator.class", "size": 4998, "crc": -1538718382}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$1.class", "size": 3707, "crc": 2135191676}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$measurePolicy$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$measurePolicy$1.class", "size": 4377, "crc": 69897622}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$1.class", "size": 3677, "crc": 1259131666}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$measurePolicy$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$measurePolicy$1.class", "size": 4293, "crc": -1765401391}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt.class", "size": 20540, "crc": 1165855920}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3751, "crc": -446713002}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "size": 3099, "crc": -1929391302}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3823, "crc": -2065581150}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "size": 3237, "crc": 1665949033}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3818, "crc": -400616557}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "size": 3232, "crc": 366349963}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion.class", "size": 9058, "crc": 1858133141}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow.class", "size": 3775, "crc": -574315643}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScope.class", "size": 791, "crc": -207772579}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScopeImpl.class", "size": 4289, "crc": -169798726}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowScope.class", "size": 1145, "crc": -18124550}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowScopeImpl.class", "size": 4767, "crc": 2136371385}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$AlignmentLineCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$AlignmentLineCrossAxisAlignment.class", "size": 2739, "crc": -269196442}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$CenterCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$CenterCrossAxisAlignment.class", "size": 1540, "crc": 1308409161}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$Companion.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$Companion.class", "size": 4203, "crc": 335405823}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$EndCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$EndCrossAxisAlignment.class", "size": 1629, "crc": 748830971}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$HorizontalCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$HorizontalCrossAxisAlignment.class", "size": 3548, "crc": -1430930318}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$StartCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$StartCrossAxisAlignment.class", "size": 1635, "crc": 2022035045}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$VerticalCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$VerticalCrossAxisAlignment.class", "size": 3471, "crc": -551526987}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment.class", "size": 3879, "crc": 1867357970}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$1.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$1.class", "size": 1757, "crc": -1248397845}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$2.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$2.class", "size": 1976, "crc": 635133380}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier.class", "size": 7832, "crc": 280953892}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$1.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$1.class", "size": 1754, "crc": 1712189589}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$2.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$2.class", "size": 1973, "crc": -1086161386}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier.class", "size": 8073, "crc": 797190691}, {"key": "androidx/compose/foundation/layout/Direction.class", "name": "androidx/compose/foundation/layout/Direction.class", "size": 1480, "crc": 1502865803}, {"key": "androidx/compose/foundation/layout/DoNothingNestedScrollConnection.class", "name": "androidx/compose/foundation/layout/DoNothingNestedScrollConnection.class", "size": 906, "crc": 1720204429}, {"key": "androidx/compose/foundation/layout/ExcludeInsets.class", "name": "androidx/compose/foundation/layout/ExcludeInsets.class", "size": 3029, "crc": -1154971614}, {"key": "androidx/compose/foundation/layout/ExperimentalLayoutApi.class", "name": "androidx/compose/foundation/layout/ExperimentalLayoutApi.class", "size": 831, "crc": -1163375034}, {"key": "androidx/compose/foundation/layout/FillCrossAxisSizeElement.class", "name": "androidx/compose/foundation/layout/FillCrossAxisSizeElement.class", "size": 3377, "crc": 1784671674}, {"key": "androidx/compose/foundation/layout/FillCrossAxisSizeNode.class", "name": "androidx/compose/foundation/layout/FillCrossAxisSizeNode.class", "size": 2940, "crc": -251425142}, {"key": "androidx/compose/foundation/layout/FillElement$Companion.class", "name": "androidx/compose/foundation/layout/FillElement$Companion.class", "size": 1827, "crc": 939023600}, {"key": "androidx/compose/foundation/layout/FillElement.class", "name": "androidx/compose/foundation/layout/FillElement.class", "size": 3837, "crc": 460064110}, {"key": "androidx/compose/foundation/layout/FillNode$measure$1.class", "name": "androidx/compose/foundation/layout/FillNode$measure$1.class", "size": 1925, "crc": -2026697405}, {"key": "androidx/compose/foundation/layout/FillNode.class", "name": "androidx/compose/foundation/layout/FillNode.class", "size": 4775, "crc": 1525821128}, {"key": "androidx/compose/foundation/layout/FixedDpInsets.class", "name": "androidx/compose/foundation/layout/FixedDpInsets.class", "size": 4410, "crc": 298049651}, {"key": "androidx/compose/foundation/layout/FixedIntInsets.class", "name": "androidx/compose/foundation/layout/FixedIntInsets.class", "size": 2890, "crc": -1446408832}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3681, "crc": -519889886}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "size": 3043, "crc": -1617501551}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3753, "crc": 225002508}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "size": 3181, "crc": 715149218}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3748, "crc": 1899385449}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "size": 3176, "crc": -1694075868}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion.class", "size": 8894, "crc": 1589114947}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow.class", "size": 3740, "crc": 1042718294}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflowScope.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflowScope.class", "size": 1131, "crc": -1593964691}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl.class", "size": 4197, "crc": 814787317}, {"key": "androidx/compose/foundation/layout/FlowColumnScope.class", "name": "androidx/compose/foundation/layout/FlowColumnScope.class", "size": 1604, "crc": -290036109}, {"key": "androidx/compose/foundation/layout/FlowColumnScopeInstance.class", "name": "androidx/compose/foundation/layout/FlowColumnScopeInstance.class", "size": 4791, "crc": 1142902959}, {"key": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapEllipsisInfo.class", "name": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapEllipsisInfo.class", "size": 2846, "crc": 1329758656}, {"key": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapInfo.class", "name": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapInfo.class", "size": 1467, "crc": -1581199347}, {"key": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks.class", "name": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks.class", "size": 7227, "crc": -1550241481}, {"key": "androidx/compose/foundation/layout/FlowLayoutData.class", "name": "androidx/compose/foundation/layout/FlowLayoutData.class", "size": 2438, "crc": 1145514951}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$1.class", "size": 3484, "crc": 417674841}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$list$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$list$1$1.class", "size": 3518, "crc": 713429711}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$1.class", "size": 3454, "crc": 1370091858}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$list$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$list$1$1.class", "size": 3491, "crc": 1775701267}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$1$1.class", "size": 2077, "crc": -689197978}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$nextSize$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$nextSize$1$1.class", "size": 2095, "crc": -50139733}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$columnMeasurementHelper$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$columnMeasurementHelper$1$1.class", "size": 2498, "crc": 1337967642}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$intrinsicCrossAxisSize$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$intrinsicCrossAxisSize$1.class", "size": 1969, "crc": 1683951113}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$intrinsicCrossAxisSize$2.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$intrinsicCrossAxisSize$2.class", "size": 1970, "crc": 1259926242}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$placeHelper$5.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$placeHelper$5.class", "size": 3536, "crc": 591598673}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$rowMeasurementHelper$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$rowMeasurementHelper$1$1.class", "size": 2488, "crc": -835560580}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt.class", "size": 59208, "crc": 1281637344}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflow$OverflowType.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflow$OverflowType.class", "size": 1822, "crc": 257590615}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflow$WhenMappings.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflow$WhenMappings.class", "size": 972, "crc": -254438090}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflow.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflow.class", "size": 6781, "crc": 1321376318}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState$WhenMappings.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState$WhenMappings.class", "size": 1132, "crc": -2069293770}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState$setOverflowMeasurables$3$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState$setOverflowMeasurables$3$1.class", "size": 3080, "crc": -1134265311}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState$setOverflowMeasurables$4$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState$setOverflowMeasurables$4$1.class", "size": 3082, "crc": 193422348}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState.class", "size": 14493, "crc": -1464988754}, {"key": "androidx/compose/foundation/layout/FlowLineInfo.class", "name": "androidx/compose/foundation/layout/FlowLineInfo.class", "size": 3882, "crc": 597278389}, {"key": "androidx/compose/foundation/layout/FlowLineMeasurePolicy$placeHelper$1$1.class", "name": "androidx/compose/foundation/layout/FlowLineMeasurePolicy$placeHelper$1$1.class", "size": 3694, "crc": -1226877900}, {"key": "androidx/compose/foundation/layout/FlowLineMeasurePolicy.class", "name": "androidx/compose/foundation/layout/FlowLineMeasurePolicy.class", "size": 6780, "crc": -1571853130}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$getMeasurePolicy$1.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$getMeasurePolicy$1.class", "size": 2284, "crc": 754893594}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$1.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$1.class", "size": 1732, "crc": 1211461815}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$2.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$2.class", "size": 3011, "crc": -344451860}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1$1.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1$1.class", "size": 3125, "crc": -398298974}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1.class", "size": 2971, "crc": 1566291295}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy.class", "size": 15811, "crc": -1926188529}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxCrossAxisIntrinsicItemSize$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxCrossAxisIntrinsicItemSize$1.class", "size": 2387, "crc": 1946212535}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxCrossAxisIntrinsicItemSize$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxCrossAxisIntrinsicItemSize$2.class", "size": 2386, "crc": -194393831}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxMainAxisIntrinsicItemSize$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxMainAxisIntrinsicItemSize$1.class", "size": 2384, "crc": -1286650891}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxMainAxisIntrinsicItemSize$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxMainAxisIntrinsicItemSize$2.class", "size": 2385, "crc": 620750143}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$1.class", "size": 1716, "crc": 1246020742}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$2.class", "size": 1716, "crc": 734489058}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$minCrossAxisIntrinsicItemSize$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$minCrossAxisIntrinsicItemSize$1.class", "size": 2387, "crc": 1916781896}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$minCrossAxisIntrinsicItemSize$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$minCrossAxisIntrinsicItemSize$2.class", "size": 2386, "crc": -1647764845}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$minMainAxisIntrinsicItemSize$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$minMainAxisIntrinsicItemSize$1.class", "size": 2384, "crc": -1457429335}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$minMainAxisIntrinsicItemSize$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$minMainAxisIntrinsicItemSize$2.class", "size": 2385, "crc": -440354884}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy.class", "size": 20131, "crc": 1083584081}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3650, "crc": -2018270416}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1.class", "size": 3019, "crc": -1792357831}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3722, "crc": -318366006}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1.class", "size": 3157, "crc": 1414882995}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3717, "crc": 214393758}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1.class", "size": 3152, "crc": 293285542}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion.class", "size": 8826, "crc": -99530270}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow.class", "size": 3725, "crc": 176891434}, {"key": "androidx/compose/foundation/layout/FlowRowOverflowScope.class", "name": "androidx/compose/foundation/layout/FlowRowOverflowScope.class", "size": 1119, "crc": -1608952913}, {"key": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl.class", "size": 4393, "crc": -219891541}, {"key": "androidx/compose/foundation/layout/FlowRowScope.class", "name": "androidx/compose/foundation/layout/FlowRowScope.class", "size": 1583, "crc": -438714966}, {"key": "androidx/compose/foundation/layout/FlowRowScopeInstance.class", "name": "androidx/compose/foundation/layout/FlowRowScopeInstance.class", "size": 4906, "crc": 708224759}, {"key": "androidx/compose/foundation/layout/HorizontalAlignElement.class", "name": "androidx/compose/foundation/layout/HorizontalAlignElement.class", "size": 3416, "crc": -1355581677}, {"key": "androidx/compose/foundation/layout/HorizontalAlignNode.class", "name": "androidx/compose/foundation/layout/HorizontalAlignNode.class", "size": 3210, "crc": -1826648409}, {"key": "androidx/compose/foundation/layout/InsetsConsumingModifier.class", "name": "androidx/compose/foundation/layout/InsetsConsumingModifier.class", "size": 5331, "crc": 822576948}, {"key": "androidx/compose/foundation/layout/InsetsListener.class", "name": "androidx/compose/foundation/layout/InsetsListener.class", "size": 5810, "crc": -1680132943}, {"key": "androidx/compose/foundation/layout/InsetsPaddingModifier$measure$1.class", "name": "androidx/compose/foundation/layout/InsetsPaddingModifier$measure$1.class", "size": 2064, "crc": 1042195719}, {"key": "androidx/compose/foundation/layout/InsetsPaddingModifier.class", "name": "androidx/compose/foundation/layout/InsetsPaddingModifier.class", "size": 8452, "crc": 265049665}, {"key": "androidx/compose/foundation/layout/InsetsPaddingValues.class", "name": "androidx/compose/foundation/layout/InsetsPaddingValues.class", "size": 4215, "crc": -966126219}, {"key": "androidx/compose/foundation/layout/InsetsValues.class", "name": "androidx/compose/foundation/layout/InsetsValues.class", "size": 2350, "crc": -24157792}, {"key": "androidx/compose/foundation/layout/IntrinsicHeightElement.class", "name": "androidx/compose/foundation/layout/IntrinsicHeightElement.class", "size": 4263, "crc": 794606058}, {"key": "androidx/compose/foundation/layout/IntrinsicHeightNode.class", "name": "androidx/compose/foundation/layout/IntrinsicHeightNode.class", "size": 3517, "crc": 911523577}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$height$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$height$$inlined$debugInspectorInfo$1.class", "size": 2952, "crc": -177785266}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$requiredHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$requiredHeight$$inlined$debugInspectorInfo$1.class", "size": 2994, "crc": 691851962}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$requiredWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$requiredWidth$$inlined$debugInspectorInfo$1.class", "size": 2987, "crc": 1686383091}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$width$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$width$$inlined$debugInspectorInfo$1.class", "size": 2947, "crc": -1275846168}, {"key": "androidx/compose/foundation/layout/IntrinsicKt.class", "name": "androidx/compose/foundation/layout/IntrinsicKt.class", "size": 3872, "crc": -1600289280}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks.class", "size": 16613, "crc": -1948243158}, {"key": "androidx/compose/foundation/layout/IntrinsicSize.class", "name": "androidx/compose/foundation/layout/IntrinsicSize.class", "size": 1443, "crc": 358141639}, {"key": "androidx/compose/foundation/layout/IntrinsicSizeModifier$measure$1.class", "name": "androidx/compose/foundation/layout/IntrinsicSizeModifier$measure$1.class", "size": 2183, "crc": -865433512}, {"key": "androidx/compose/foundation/layout/IntrinsicSizeModifier.class", "name": "androidx/compose/foundation/layout/IntrinsicSizeModifier.class", "size": 3942, "crc": 1627977476}, {"key": "androidx/compose/foundation/layout/IntrinsicWidthElement.class", "name": "androidx/compose/foundation/layout/IntrinsicWidthElement.class", "size": 4253, "crc": -527285212}, {"key": "androidx/compose/foundation/layout/IntrinsicWidthNode.class", "name": "androidx/compose/foundation/layout/IntrinsicWidthNode.class", "size": 3508, "crc": -1146736258}, {"key": "androidx/compose/foundation/layout/LayoutOrientation.class", "name": "androidx/compose/foundation/layout/LayoutOrientation.class", "size": 1487, "crc": 1347481713}, {"key": "androidx/compose/foundation/layout/LayoutScopeMarker.class", "name": "androidx/compose/foundation/layout/LayoutScopeMarker.class", "size": 628, "crc": -1348536247}, {"key": "androidx/compose/foundation/layout/LayoutWeightElement.class", "name": "androidx/compose/foundation/layout/LayoutWeightElement.class", "size": 3679, "crc": -738456057}, {"key": "androidx/compose/foundation/layout/LayoutWeightNode.class", "name": "androidx/compose/foundation/layout/LayoutWeightNode.class", "size": 2758, "crc": -250846559}, {"key": "androidx/compose/foundation/layout/LimitInsets.class", "name": "androidx/compose/foundation/layout/LimitInsets.class", "size": 4583, "crc": -905006986}, {"key": "androidx/compose/foundation/layout/MutableWindowInsets.class", "name": "androidx/compose/foundation/layout/MutableWindowInsets.class", "size": 4218, "crc": -1252222458}, {"key": "androidx/compose/foundation/layout/OffsetElement.class", "name": "androidx/compose/foundation/layout/OffsetElement.class", "size": 5071, "crc": 1106817153}, {"key": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$1.class", "name": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$1.class", "size": 1986, "crc": -562145818}, {"key": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$2.class", "name": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$2.class", "size": 2139, "crc": 1799264784}, {"key": "androidx/compose/foundation/layout/OffsetKt$offset$1.class", "name": "androidx/compose/foundation/layout/OffsetKt$offset$1.class", "size": 1954, "crc": -514628315}, {"key": "androidx/compose/foundation/layout/OffsetKt$offset$2.class", "name": "androidx/compose/foundation/layout/OffsetKt$offset$2.class", "size": 2103, "crc": 974397725}, {"key": "androidx/compose/foundation/layout/OffsetKt.class", "name": "androidx/compose/foundation/layout/OffsetKt.class", "size": 4215, "crc": -549607851}, {"key": "androidx/compose/foundation/layout/OffsetNode$measure$1.class", "name": "androidx/compose/foundation/layout/OffsetNode$measure$1.class", "size": 2532, "crc": -2052193526}, {"key": "androidx/compose/foundation/layout/OffsetNode.class", "name": "androidx/compose/foundation/layout/OffsetNode.class", "size": 3426, "crc": -945287518}, {"key": "androidx/compose/foundation/layout/OffsetPxElement.class", "name": "androidx/compose/foundation/layout/OffsetPxElement.class", "size": 4890, "crc": 1485171554}, {"key": "androidx/compose/foundation/layout/OffsetPxNode$measure$1.class", "name": "androidx/compose/foundation/layout/OffsetPxNode$measure$1.class", "size": 2657, "crc": 1697003229}, {"key": "androidx/compose/foundation/layout/OffsetPxNode.class", "name": "androidx/compose/foundation/layout/OffsetPxNode.class", "size": 3739, "crc": 1728286375}, {"key": "androidx/compose/foundation/layout/OrientationIndependentConstraints.class", "name": "androidx/compose/foundation/layout/OrientationIndependentConstraints.class", "size": 7037, "crc": 1470366656}, {"key": "androidx/compose/foundation/layout/PaddingElement.class", "name": "androidx/compose/foundation/layout/PaddingElement.class", "size": 7295, "crc": -1382744912}, {"key": "androidx/compose/foundation/layout/PaddingKt$absolutePadding$1.class", "name": "androidx/compose/foundation/layout/PaddingKt$absolutePadding$1.class", "size": 2157, "crc": -465048990}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$1.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$1.class", "size": 2123, "crc": -1436312793}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$2.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$2.class", "size": 1994, "crc": 94379963}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$3.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$3.class", "size": 1735, "crc": -1406577933}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$4.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$4.class", "size": 1956, "crc": -77587057}, {"key": "androidx/compose/foundation/layout/PaddingKt.class", "name": "androidx/compose/foundation/layout/PaddingKt.class", "size": 8158, "crc": 987909815}, {"key": "androidx/compose/foundation/layout/PaddingNode$measure$1.class", "name": "androidx/compose/foundation/layout/PaddingNode$measure$1.class", "size": 2544, "crc": 1117757812}, {"key": "androidx/compose/foundation/layout/PaddingNode.class", "name": "androidx/compose/foundation/layout/PaddingNode.class", "size": 5610, "crc": 817717200}, {"key": "androidx/compose/foundation/layout/PaddingValues$Absolute.class", "name": "androidx/compose/foundation/layout/PaddingValues$Absolute.class", "size": 5932, "crc": 1304695590}, {"key": "androidx/compose/foundation/layout/PaddingValues.class", "name": "androidx/compose/foundation/layout/PaddingValues.class", "size": 1301, "crc": -424052114}, {"key": "androidx/compose/foundation/layout/PaddingValuesConsumingModifier.class", "name": "androidx/compose/foundation/layout/PaddingValuesConsumingModifier.class", "size": 2351, "crc": -1728674904}, {"key": "androidx/compose/foundation/layout/PaddingValuesElement.class", "name": "androidx/compose/foundation/layout/PaddingValuesElement.class", "size": 3952, "crc": -1364898448}, {"key": "androidx/compose/foundation/layout/PaddingValuesImpl.class", "name": "androidx/compose/foundation/layout/PaddingValuesImpl.class", "size": 6272, "crc": -1359083546}, {"key": "androidx/compose/foundation/layout/PaddingValuesInsets.class", "name": "androidx/compose/foundation/layout/PaddingValuesInsets.class", "size": 3914, "crc": -2033768269}, {"key": "androidx/compose/foundation/layout/PaddingValuesModifier$measure$2.class", "name": "androidx/compose/foundation/layout/PaddingValuesModifier$measure$2.class", "size": 2760, "crc": -86029856}, {"key": "androidx/compose/foundation/layout/PaddingValuesModifier.class", "name": "androidx/compose/foundation/layout/PaddingValuesModifier.class", "size": 5082, "crc": 1259242563}, {"key": "androidx/compose/foundation/layout/RowColumnImplKt.class", "name": "androidx/compose/foundation/layout/RowColumnImplKt.class", "size": 8190, "crc": -1772110671}, {"key": "androidx/compose/foundation/layout/RowColumnMeasurePolicy.class", "name": "androidx/compose/foundation/layout/RowColumnMeasurePolicy.class", "size": 2448, "crc": 1927979599}, {"key": "androidx/compose/foundation/layout/RowColumnMeasurePolicyKt.class", "name": "androidx/compose/foundation/layout/RowColumnMeasurePolicyKt.class", "size": 13575, "crc": 1821877750}, {"key": "androidx/compose/foundation/layout/RowColumnParentData.class", "name": "androidx/compose/foundation/layout/RowColumnParentData.class", "size": 5464, "crc": -1696232387}, {"key": "androidx/compose/foundation/layout/RowKt.class", "name": "androidx/compose/foundation/layout/RowKt.class", "size": 11085, "crc": -1331232250}, {"key": "androidx/compose/foundation/layout/RowMeasurePolicy$placeHelper$1$1.class", "name": "androidx/compose/foundation/layout/RowMeasurePolicy$placeHelper$1$1.class", "size": 4060, "crc": -202731656}, {"key": "androidx/compose/foundation/layout/RowMeasurePolicy.class", "name": "androidx/compose/foundation/layout/RowMeasurePolicy.class", "size": 11169, "crc": -1116659453}, {"key": "androidx/compose/foundation/layout/RowScope$DefaultImpls.class", "name": "androidx/compose/foundation/layout/RowScope$DefaultImpls.class", "size": 593, "crc": -1406900227}, {"key": "androidx/compose/foundation/layout/RowScope.class", "name": "androidx/compose/foundation/layout/RowScope.class", "size": 2763, "crc": 1569635416}, {"key": "androidx/compose/foundation/layout/RowScopeInstance.class", "name": "androidx/compose/foundation/layout/RowScopeInstance.class", "size": 4715, "crc": 1961860586}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineBlockNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineBlockNode.class", "size": 3511, "crc": -839653203}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineNode.class", "size": 3116, "crc": -1991078968}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode.class", "size": 1965, "crc": -329294771}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$BottomSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$BottomSideCalculator$1.class", "size": 2503, "crc": 555122046}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$LeftSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$LeftSideCalculator$1.class", "size": 2498, "crc": 2120317566}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$RightSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$RightSideCalculator$1.class", "size": 2501, "crc": -1608513612}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$TopSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$TopSideCalculator$1.class", "size": 2496, "crc": -756432457}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion.class", "size": 3490, "crc": -57778062}, {"key": "androidx/compose/foundation/layout/SideCalculator.class", "name": "androidx/compose/foundation/layout/SideCalculator.class", "size": 2041, "crc": -1395001170}, {"key": "androidx/compose/foundation/layout/SizeElement.class", "name": "androidx/compose/foundation/layout/SizeElement.class", "size": 4853, "crc": 1970514275}, {"key": "androidx/compose/foundation/layout/SizeKt$height-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$height-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2717, "crc": -186436958}, {"key": "androidx/compose/foundation/layout/SizeKt$heightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$heightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2961, "crc": -122928057}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredHeight-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredHeight-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2767, "crc": -1157694225}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredHeightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredHeightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 3010, "crc": 409170026}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSize-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSize-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2753, "crc": 406648407}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSize-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSize-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2996, "crc": -641399945}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "size": 3181, "crc": 1525085941}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredWidth-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredWidth-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2760, "crc": -1136898003}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredWidthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredWidthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 3004, "crc": -216658507}, {"key": "androidx/compose/foundation/layout/SizeKt$size-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$size-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2705, "crc": 1609893514}, {"key": "androidx/compose/foundation/layout/SizeKt$size-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$size-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2947, "crc": 366400842}, {"key": "androidx/compose/foundation/layout/SizeKt$sizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$sizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "size": 3132, "crc": -2077179141}, {"key": "androidx/compose/foundation/layout/SizeKt$width-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$width-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2710, "crc": -1630583427}, {"key": "androidx/compose/foundation/layout/SizeKt$widthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$widthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2955, "crc": 1958032173}, {"key": "androidx/compose/foundation/layout/SizeKt.class", "name": "androidx/compose/foundation/layout/SizeKt.class", "size": 17986, "crc": 64966965}, {"key": "androidx/compose/foundation/layout/SizeNode$measure$1.class", "name": "androidx/compose/foundation/layout/SizeNode$measure$1.class", "size": 1925, "crc": 368458913}, {"key": "androidx/compose/foundation/layout/SizeNode.class", "name": "androidx/compose/foundation/layout/SizeNode.class", "size": 7944, "crc": 1662491018}, {"key": "androidx/compose/foundation/layout/SpacerKt.class", "name": "androidx/compose/foundation/layout/SpacerKt.class", "size": 5849, "crc": -2103604172}, {"key": "androidx/compose/foundation/layout/SpacerMeasurePolicy$measure$1$1.class", "name": "androidx/compose/foundation/layout/SpacerMeasurePolicy$measure$1$1.class", "size": 1722, "crc": -1063229416}, {"key": "androidx/compose/foundation/layout/SpacerMeasurePolicy.class", "name": "androidx/compose/foundation/layout/SpacerMeasurePolicy.class", "size": 2543, "crc": -1677704817}, {"key": "androidx/compose/foundation/layout/SplineBasedFloatDecayAnimationSpec.class", "name": "androidx/compose/foundation/layout/SplineBasedFloatDecayAnimationSpec.class", "size": 3347, "crc": -434370327}, {"key": "androidx/compose/foundation/layout/UnionInsets.class", "name": "androidx/compose/foundation/layout/UnionInsets.class", "size": 3004, "crc": -1673419114}, {"key": "androidx/compose/foundation/layout/UnionInsetsConsumingModifier.class", "name": "androidx/compose/foundation/layout/UnionInsetsConsumingModifier.class", "size": 2149, "crc": -307325624}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsElement.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsElement.class", "size": 4101, "crc": 1976972896}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode$measure$1.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode$measure$1.class", "size": 1979, "crc": -703199809}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode.class", "size": 5623, "crc": -1284078528}, {"key": "androidx/compose/foundation/layout/ValueInsets.class", "name": "androidx/compose/foundation/layout/ValueInsets.class", "size": 5089, "crc": 222124637}, {"key": "androidx/compose/foundation/layout/VerticalAlignElement.class", "name": "androidx/compose/foundation/layout/VerticalAlignElement.class", "size": 3388, "crc": 846462364}, {"key": "androidx/compose/foundation/layout/VerticalAlignNode.class", "name": "androidx/compose/foundation/layout/VerticalAlignNode.class", "size": 3184, "crc": -412756935}, {"key": "androidx/compose/foundation/layout/WindowInsets$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsets$Companion.class", "size": 757, "crc": -1615628904}, {"key": "androidx/compose/foundation/layout/WindowInsets.class", "name": "androidx/compose/foundation/layout/WindowInsets.class", "size": 1370, "crc": -1634164549}, {"key": "androidx/compose/foundation/layout/WindowInsetsAnimationCancelledException.class", "name": "androidx/compose/foundation/layout/WindowInsetsAnimationCancelledException.class", "size": 2092, "crc": 995044726}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$$inlined$debugInspectorInfo$1.class", "size": 2733, "crc": 2084315247}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$2.class", "size": 4041, "crc": 163108160}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$1$invoke$$inlined$onDispose$1.class", "size": 2498, "crc": 337412595}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$1.class", "size": 3383, "crc": -628902448}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt.class", "size": 10022, "crc": 1787335250}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$1$invoke$$inlined$onDispose$1.class", "size": 2394, "crc": -134697446}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$1.class", "size": 3228, "crc": 1425602443}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion.class", "size": 8986, "crc": 1467740356}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder.class", "size": 12498, "crc": -1701649347}, {"key": "androidx/compose/foundation/layout/WindowInsetsKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsKt.class", "size": 7151, "crc": -866422973}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$animationEnded$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$animationEnded$1.class", "size": 1500, "crc": 461441919}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$dispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$dispose$1.class", "size": 1479, "crc": -1950673793}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$1.class", "size": 2269, "crc": -626836277}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1$1.class", "size": 3245, "crc": -806344051}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1.class", "size": 5163, "crc": 1474448533}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2.class", "size": 5805, "crc": -1503853626}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1$1.class", "size": 2395, "crc": -1477046305}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1.class", "size": 5230, "crc": 441049972}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3.class", "size": 4745, "crc": 1324487137}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$onReady$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$onReady$1.class", "size": 1532, "crc": 517385037}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection.class", "size": 18910, "crc": -836334992}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$ModifierLocalConsumedWindowInsets$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$ModifierLocalConsumedWindowInsets$1.class", "size": 1503, "crc": 1901690911}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$1.class", "size": 3080, "crc": -154326894}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$2.class", "size": 3099, "crc": 37617189}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$2.class", "size": 4667, "crc": 1283589773}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$4.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$4.class", "size": 4684, "crc": -1840336307}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$$inlined$debugInspectorInfo$1.class", "size": 3079, "crc": 1753067235}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$2.class", "size": 4904, "crc": 946278117}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$$inlined$debugInspectorInfo$1.class", "size": 3080, "crc": 1934299884}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$2.class", "size": 4660, "crc": 1328140566}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt.class", "size": 6206, "crc": -1750851259}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$debugInspectorInfo$1.class", "size": 2713, "crc": -1822173851}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$windowInsetsPadding$1.class", "size": 6002, "crc": -942743695}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$debugInspectorInfo$1.class", "size": 2727, "crc": 1453504640}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$windowInsetsPadding$1.class", "size": 6019, "crc": -887952876}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$debugInspectorInfo$1.class", "size": 2678, "crc": 2126370921}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$windowInsetsPadding$1.class", "size": 5960, "crc": 767627720}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2778, "crc": 2101982102}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 6080, "crc": 1191257055}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2733, "crc": -2120279863}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 6026, "crc": -1403460684}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$debugInspectorInfo$1.class", "size": 2715, "crc": 1544463740}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$windowInsetsPadding$1.class", "size": 5942, "crc": -570582799}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$debugInspectorInfo$1.class", "size": 2715, "crc": 1211945363}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$windowInsetsPadding$1.class", "size": 5942, "crc": 314494104}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2720, "crc": 421691928}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 5948, "crc": -196105950}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2713, "crc": -2885481}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 6002, "crc": -677056404}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2710, "crc": 1978778364}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 5999, "crc": 663560830}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2733, "crc": 1921600967}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 6026, "crc": 173464779}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$debugInspectorInfo$1.class", "size": 2708, "crc": -1653750348}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$windowInsetsPadding$1.class", "size": 5988, "crc": 39524641}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$windowInsetsPadding$1.class", "size": 5781, "crc": 1249545283}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt.class", "size": 10606, "crc": -1761367235}, {"key": "androidx/compose/foundation/layout/WindowInsetsSides$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsetsSides$Companion.class", "size": 3081, "crc": 569296784}, {"key": "androidx/compose/foundation/layout/WindowInsetsSides.class", "name": "androidx/compose/foundation/layout/WindowInsetsSides.class", "size": 5167, "crc": -1288636488}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$$inlined$debugInspectorInfo$1.class", "size": 3104, "crc": -2118850110}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$2.class", "size": 2085, "crc": 762147538}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$$inlined$debugInspectorInfo$1.class", "size": 3078, "crc": -1821405839}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$2.class", "size": 2480, "crc": 79819505}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$$inlined$debugInspectorInfo$1.class", "size": 3090, "crc": -1031787181}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$2.class", "size": 2486, "crc": 1095429760}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$$inlined$debugInspectorInfo$1.class", "size": 3084, "crc": 755222846}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$2.class", "size": 2073, "crc": 1204688320}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt.class", "size": 5128, "crc": 1828333658}, {"key": "androidx/compose/foundation/layout/WindowInsets_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsets_androidKt.class", "size": 18201, "crc": -1464729215}, {"key": "androidx/compose/foundation/layout/WithAlignmentLineBlockElement.class", "name": "androidx/compose/foundation/layout/WithAlignmentLineBlockElement.class", "size": 3830, "crc": 1988943813}, {"key": "androidx/compose/foundation/layout/WithAlignmentLineElement.class", "name": "androidx/compose/foundation/layout/WithAlignmentLineElement.class", "size": 3555, "crc": -216479695}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion$height$1.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion$height$1.class", "size": 2358, "crc": -535907828}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion$size$1.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion$size$1.class", "size": 2341, "crc": 1745619964}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion$width$1.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion$width$1.class", "size": 2409, "crc": -820130886}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion.class", "size": 3113, "crc": 1099188765}, {"key": "androidx/compose/foundation/layout/WrapContentElement.class", "name": "androidx/compose/foundation/layout/WrapContentElement.class", "size": 5173, "crc": -1732916634}, {"key": "androidx/compose/foundation/layout/WrapContentNode$measure$1.class", "name": "androidx/compose/foundation/layout/WrapContentNode$measure$1.class", "size": 3028, "crc": 1085189657}, {"key": "androidx/compose/foundation/layout/WrapContentNode.class", "name": "androidx/compose/foundation/layout/WrapContentNode.class", "size": 5292, "crc": 446139296}, {"key": "androidx/compose/foundation/layout/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/foundation/layout/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 580, "crc": 843432404}, {"key": "META-INF/androidx.compose.foundation_foundation-layout.version", "name": "META-INF/androidx.compose.foundation_foundation-layout.version", "size": 6, "crc": 1621725393}, {"key": "META-INF/foundation-layout_release.kotlin_module", "name": "META-INF/foundation-layout_release.kotlin_module", "size": 504, "crc": 1403913241}]