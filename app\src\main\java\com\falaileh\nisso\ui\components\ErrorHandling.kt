package com.falaileh.nisso.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.tv.material3.ExperimentalTvMaterial3Api
import androidx.tv.material3.Text
import com.falaileh.nisso.ui.animations.RomanticLoadingAnimation
import com.falaileh.nisso.ui.utils.TypewriterText
import kotlinx.coroutines.delay

/**
 * Comprehensive error handling screen with romantic styling
 */
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun RomanticErrorScreen(
    error: String,
    isOffline: Boolean = false,
    onRetry: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val pulseScale by rememberInfiniteTransition(label = "error_pulse").animateFloat(
        initialValue = 1f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_scale"
    )
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.radialGradient(
                    colors = listOf(
                        Color(0xFF2C1810),
                        Color(0xFF1A0F0A),
                        Color.Black
                    ),
                    radius = 800f
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(24.dp),
            modifier = Modifier.padding(48.dp)
        ) {
            // Error icon with animation
            Box(
                modifier = Modifier.scale(pulseScale),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = if (isOffline) "📡" else "💔",
                    fontSize = 72.sp,
                    color = Color(0xFFFF6B6B)
                )
            }
            
            // Error title
            TypewriterText(
                text = if (isOffline) "Connection Lost" else "Oops! Something went wrong",
                textColor = Color.White,
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                startDelay = 500L
            )
            
            // Error message
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(16.dp))
                    .background(Color.Black.copy(alpha = 0.4f))
                    .padding(24.dp)
            ) {
                Text(
                    text = error,
                    color = Color.White.copy(alpha = 0.9f),
                    fontSize = 18.sp,
                    textAlign = TextAlign.Center,
                    lineHeight = 24.sp
                )
            }
            
            // Retry instruction
            RetryInstructionWithAnimation(isOffline = isOffline)
            
            // Offline indicator
            if (isOffline) {
                OfflineIndicator()
            }
        }
        
        // Floating sad hearts
        SadHeartsOverlay()
    }
}

/**
 * Loading screen with romantic animations
 */
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun RomanticLoadingScreen(
    message: String = "Loading love messages...",
    isRefreshing: Boolean = false,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.radialGradient(
                    colors = listOf(
                        Color(0xFF4A1A2C),
                        Color(0xFF2D1B2E),
                        Color.Black
                    ),
                    radius = 1000f
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(32.dp),
            modifier = Modifier.padding(48.dp)
        ) {
            // Main loading animation
            Box(
                modifier = Modifier.size(120.dp),
                contentAlignment = Alignment.Center
            ) {
                if (isRefreshing) {
                    RefreshingAnimation()
                } else {
                    RomanticLoadingAnimation(
                        color = Color(0xFFFF6B6B)
                    )
                }
            }
            
            // Loading message with typewriter effect
            TypewriterText(
                text = message,
                textColor = Color.White,
                fontSize = 24.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                typingDelayMs = 80L
            )
            
            // Loading dots animation
            LoadingDotsAnimation()
        }
        
        // Background sparkles
        SparkleOverlay(
            modifier = Modifier.fillMaxSize(),
            sparkleCount = 15
        )
    }
}

/**
 * Retry instruction with pulsing animation
 */
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
private fun RetryInstructionWithAnimation(
    isOffline: Boolean,
    modifier: Modifier = Modifier
) {
    val glowIntensity by rememberInfiniteTransition(label = "retry_glow").animateFloat(
        initialValue = 0.3f,
        targetValue = 0.8f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glow_intensity"
    )
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0xFF4ECDC4).copy(alpha = glowIntensity),
                        Color(0xFF44A08D).copy(alpha = glowIntensity)
                    )
                )
            )
            .padding(horizontal = 20.dp, vertical = 12.dp)
    ) {
        Text(
            text = if (isOffline) {
                "Check your connection and press SELECT to retry"
            } else {
                "Press SELECT to try again"
            },
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * Offline indicator with animation
 */
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
private fun OfflineIndicator(
    modifier: Modifier = Modifier
) {
    val blinkAlpha by rememberInfiniteTransition(label = "offline_blink").animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "blink_alpha"
    )
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .clip(RoundedCornerShape(6.dp))
                .background(Color.Red.copy(alpha = blinkAlpha))
        )
        
        Text(
            text = "Offline Mode",
            color = Color.White.copy(alpha = 0.8f),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * Refreshing animation
 */
@Composable
private fun RefreshingAnimation(
    modifier: Modifier = Modifier
) {
    val rotation by rememberInfiniteTransition(label = "refresh_rotation").animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        // Custom loading indicator using RomanticLoadingAnimation
        RomanticLoadingAnimation(
            modifier = Modifier.size(60.dp),
            color = Color(0xFF4ECDC4)
        )

        Text(
            text = "💕",
            fontSize = 24.sp,
            modifier = Modifier.scale(1.2f)
        )
    }
}

/**
 * Loading dots animation
 */
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
private fun LoadingDotsAnimation(
    modifier: Modifier = Modifier
) {
    val dots = remember { listOf(".", ".", ".") }
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        dots.forEachIndexed { index, dot ->
            val delay = index * 200L
            val alpha by rememberInfiniteTransition(label = "dot_$index").animateFloat(
                initialValue = 0.3f,
                targetValue = 1f,
                animationSpec = infiniteRepeatable(
                    animation = tween(600, delayMillis = delay.toInt()),
                    repeatMode = RepeatMode.Reverse
                ),
                label = "dot_alpha_$index"
            )
            
            Text(
                text = dot,
                color = Color.White.copy(alpha = alpha),
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

/**
 * Sad hearts overlay for error states
 */
@Composable
private fun SadHeartsOverlay(
    modifier: Modifier = Modifier
) {
    val hearts = remember {
        (0 until 5).map {
            SadHeart(
                x = kotlin.random.Random.nextFloat(),
                y = kotlin.random.Random.nextFloat(),
                speed = kotlin.random.Random.nextFloat() * 0.1f + 0.05f,
                size = kotlin.random.Random.nextFloat() * 15f + 10f
            )
        }
    }
    
    // Implementation would be similar to FloatingHeartsOverlay but with sad/broken hearts
    // For brevity, using a simple representation
    Box(modifier = modifier.fillMaxSize()) {
        // Sad hearts would float here
    }
}

/**
 * Data class for sad hearts
 */
private data class SadHeart(
    val x: Float,
    val y: Float,
    val speed: Float,
    val size: Float
)
