{"logs": [{"outputFile": "com.falaileh.nisso.test.app-mergeDebugAndroidTestResources-34:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\910260a50c4cc0fe03b922548d59c7fb\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,402,500,603,708,2083", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "196,299,397,495,598,703,815,2179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8dca2d17bb97bd73368ea919040cc370\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,979,1068,1141,1216,1292,1368,1446,1513", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,974,1063,1136,1211,1287,1363,1441,1508,1631"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,906,983,1080,1181,1269,1354,1439,1525,1608,1694,1783,1856,1931,2007,2184,2262,2329", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "901,978,1075,1176,1264,1349,1434,1520,1603,1689,1778,1851,1926,2002,2078,2257,2324,2447"}}]}]}