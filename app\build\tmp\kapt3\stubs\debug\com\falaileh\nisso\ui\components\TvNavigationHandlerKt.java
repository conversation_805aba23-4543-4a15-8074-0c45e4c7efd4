package com.falaileh.nisso.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\u001a*\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0003\u001a\"\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0003\u001a\u001a\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a\u001a\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u000e2\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a$\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u000e2\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a\b\u0010\u0013\u001a\u00020\u0014H\u0007\u00a8\u0006\u0015"}, d2 = {"ControlHintRow", "", "icon", "", "description", "alpha", "", "modifier", "Landroidx/compose/ui/Modifier;", "NavigationIndicator", "direction", "Lcom/falaileh/nisso/ui/components/NavigationDirection;", "TvControlHints", "isVisible", "", "TvHapticFeedback", "trigger", "TvNavigationFeedback", "isNavigating", "rememberTvNavigationState", "Lcom/falaileh/nisso/ui/components/TvNavigationState;", "app_debug"})
public final class TvNavigationHandlerKt {
    
    /**
     * TV Navigation visual feedback component
     */
    @androidx.compose.runtime.Composable()
    public static final void TvNavigationFeedback(boolean isNavigating, @org.jetbrains.annotations.Nullable()
    com.falaileh.nisso.ui.components.NavigationDirection direction, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Visual indicator for navigation actions
     */
    @kotlin.OptIn(markerClass = {androidx.tv.material3.ExperimentalTvMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void NavigationIndicator(com.falaileh.nisso.ui.components.NavigationDirection direction, float alpha, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * TV Remote control hints overlay
     */
    @kotlin.OptIn(markerClass = {androidx.tv.material3.ExperimentalTvMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void TvControlHints(boolean isVisible, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.tv.material3.ExperimentalTvMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void ControlHintRow(java.lang.String icon, java.lang.String description, float alpha, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Focus management for TV navigation
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final com.falaileh.nisso.ui.components.TvNavigationState rememberTvNavigationState() {
        return null;
    }
    
    /**
     * Haptic feedback simulation for TV navigation
     */
    @androidx.compose.runtime.Composable()
    public static final void TvHapticFeedback(boolean trigger, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}