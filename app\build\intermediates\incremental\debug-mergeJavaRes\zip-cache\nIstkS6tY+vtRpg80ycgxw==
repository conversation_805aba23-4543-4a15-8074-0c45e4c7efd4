[{"key": "androidx/lifecycle/AndroidViewModel.class", "name": "androidx/lifecycle/AndroidViewModel.class", "size": 1329, "crc": **********}, {"key": "androidx/lifecycle/HasDefaultViewModelProviderFactory.class", "name": "androidx/lifecycle/HasDefaultViewModelProviderFactory.class", "size": 1433, "crc": 68758757}, {"key": "androidx/lifecycle/ViewModel.class", "name": "androidx/lifecycle/ViewModel.class", "size": 3615, "crc": 374934470}, {"key": "androidx/lifecycle/ViewModelKt.class", "name": "androidx/lifecycle/ViewModelKt.class", "size": 3316, "crc": 654811225}, {"key": "androidx/lifecycle/ViewModelLazy$1.class", "name": "androidx/lifecycle/ViewModelLazy$1.class", "size": 1590, "crc": 386025531}, {"key": "androidx/lifecycle/ViewModelLazy.class", "name": "androidx/lifecycle/ViewModelLazy.class", "size": 5211, "crc": **********}, {"key": "androidx/lifecycle/ViewModelProvider$AndroidViewModelFactory$Companion$APPLICATION_KEY$1.class", "name": "androidx/lifecycle/ViewModelProvider$AndroidViewModelFactory$Companion$APPLICATION_KEY$1.class", "size": 1166, "crc": 473308465}, {"key": "androidx/lifecycle/ViewModelProvider$AndroidViewModelFactory$Companion.class", "name": "androidx/lifecycle/ViewModelProvider$AndroidViewModelFactory$Companion.class", "size": 2100, "crc": -944582417}, {"key": "androidx/lifecycle/ViewModelProvider$AndroidViewModelFactory.class", "name": "androidx/lifecycle/ViewModelProvider$AndroidViewModelFactory.class", "size": 6211, "crc": **********}, {"key": "androidx/lifecycle/ViewModelProvider$Companion.class", "name": "androidx/lifecycle/ViewModelProvider$Companion.class", "size": 4157, "crc": -**********}, {"key": "androidx/lifecycle/ViewModelProvider$Factory$Companion.class", "name": "androidx/lifecycle/ViewModelProvider$Factory$Companion.class", "size": 1982, "crc": -142908227}, {"key": "androidx/lifecycle/ViewModelProvider$Factory.class", "name": "androidx/lifecycle/ViewModelProvider$Factory.class", "size": 3132, "crc": **********}, {"key": "androidx/lifecycle/ViewModelProvider$NewInstanceFactory$Companion.class", "name": "androidx/lifecycle/ViewModelProvider$NewInstanceFactory$Companion.class", "size": 2055, "crc": 972088855}, {"key": "androidx/lifecycle/ViewModelProvider$NewInstanceFactory.class", "name": "androidx/lifecycle/ViewModelProvider$NewInstanceFactory.class", "size": 4167, "crc": -**********}, {"key": "androidx/lifecycle/ViewModelProvider$OnRequeryFactory.class", "name": "androidx/lifecycle/ViewModelProvider$OnRequeryFactory.class", "size": 1337, "crc": 375861427}, {"key": "androidx/lifecycle/ViewModelProvider.class", "name": "androidx/lifecycle/ViewModelProvider.class", "size": 7167, "crc": -643426921}, {"key": "androidx/lifecycle/ViewModelProviderGetKt.class", "name": "androidx/lifecycle/ViewModelProviderGetKt.class", "size": 1415, "crc": -**********}, {"key": "androidx/lifecycle/ViewModelStore.class", "name": "androidx/lifecycle/ViewModelStore.class", "size": 2830, "crc": 929790919}, {"key": "androidx/lifecycle/ViewModelStoreOwner.class", "name": "androidx/lifecycle/ViewModelStoreOwner.class", "size": 657, "crc": **********}, {"key": "androidx/lifecycle/ViewTreeViewModelKt.class", "name": "androidx/lifecycle/ViewTreeViewModelKt.class", "size": 1316, "crc": -778850603}, {"key": "androidx/lifecycle/ViewTreeViewModelStoreOwner$findViewTreeViewModelStoreOwner$1.class", "name": "androidx/lifecycle/ViewTreeViewModelStoreOwner$findViewTreeViewModelStoreOwner$1.class", "size": 1822, "crc": -310187729}, {"key": "androidx/lifecycle/ViewTreeViewModelStoreOwner$findViewTreeViewModelStoreOwner$2.class", "name": "androidx/lifecycle/ViewTreeViewModelStoreOwner$findViewTreeViewModelStoreOwner$2.class", "size": 2020, "crc": **********}, {"key": "androidx/lifecycle/ViewTreeViewModelStoreOwner.class", "name": "androidx/lifecycle/ViewTreeViewModelStoreOwner.class", "size": 2503, "crc": 457237861}, {"key": "androidx/lifecycle/viewmodel/CreationExtras$Empty.class", "name": "androidx/lifecycle/viewmodel/CreationExtras$Empty.class", "size": 1504, "crc": -1393342234}, {"key": "androidx/lifecycle/viewmodel/CreationExtras$Key.class", "name": "androidx/lifecycle/viewmodel/CreationExtras$Key.class", "size": 591, "crc": -1594625217}, {"key": "androidx/lifecycle/viewmodel/CreationExtras.class", "name": "androidx/lifecycle/viewmodel/CreationExtras.class", "size": 1730, "crc": 425937294}, {"key": "androidx/lifecycle/viewmodel/InitializerViewModelFactory.class", "name": "androidx/lifecycle/viewmodel/InitializerViewModelFactory.class", "size": 2651, "crc": -782464419}, {"key": "androidx/lifecycle/viewmodel/InitializerViewModelFactoryBuilder.class", "name": "androidx/lifecycle/viewmodel/InitializerViewModelFactoryBuilder.class", "size": 3501, "crc": -1921042663}, {"key": "androidx/lifecycle/viewmodel/InitializerViewModelFactoryKt.class", "name": "androidx/lifecycle/viewmodel/InitializerViewModelFactoryKt.class", "size": 2809, "crc": 79585420}, {"key": "androidx/lifecycle/viewmodel/MutableCreationExtras.class", "name": "androidx/lifecycle/viewmodel/MutableCreationExtras.class", "size": 2582, "crc": -948619539}, {"key": "androidx/lifecycle/viewmodel/ViewModelFactoryDsl.class", "name": "androidx/lifecycle/viewmodel/ViewModelFactoryDsl.class", "size": 632, "crc": -**********}, {"key": "androidx/lifecycle/viewmodel/ViewModelInitializer.class", "name": "androidx/lifecycle/viewmodel/ViewModelInitializer.class", "size": 2672, "crc": -**********}, {"key": "androidx/lifecycle/viewmodel/ViewModelProviderImpl.class", "name": "androidx/lifecycle/viewmodel/ViewModelProviderImpl.class", "size": 5345, "crc": -794083915}, {"key": "androidx/lifecycle/viewmodel/ViewModelProviderImpl_androidKt.class", "name": "androidx/lifecycle/viewmodel/ViewModelProviderImpl_androidKt.class", "size": 2330, "crc": -**********}, {"key": "androidx/lifecycle/viewmodel/internal/CloseableCoroutineScope.class", "name": "androidx/lifecycle/viewmodel/internal/CloseableCoroutineScope.class", "size": 1792, "crc": -**********}, {"key": "androidx/lifecycle/viewmodel/internal/CloseableCoroutineScopeKt.class", "name": "androidx/lifecycle/viewmodel/internal/CloseableCoroutineScopeKt.class", "size": 2489, "crc": -**********}, {"key": "androidx/lifecycle/viewmodel/internal/DefaultViewModelProviderFactory.class", "name": "androidx/lifecycle/viewmodel/internal/DefaultViewModelProviderFactory.class", "size": 2053, "crc": **********}, {"key": "androidx/lifecycle/viewmodel/internal/JvmViewModelProviders.class", "name": "androidx/lifecycle/viewmodel/internal/JvmViewModelProviders.class", "size": 2480, "crc": 327349424}, {"key": "androidx/lifecycle/viewmodel/internal/SynchronizedObject.class", "name": "androidx/lifecycle/viewmodel/internal/SynchronizedObject.class", "size": 584, "crc": **********}, {"key": "androidx/lifecycle/viewmodel/internal/SynchronizedObjectKt.class", "name": "androidx/lifecycle/viewmodel/internal/SynchronizedObjectKt.class", "size": 2554, "crc": 50098708}, {"key": "androidx/lifecycle/viewmodel/internal/SynchronizedObject_jvmKt.class", "name": "androidx/lifecycle/viewmodel/internal/SynchronizedObject_jvmKt.class", "size": 1641, "crc": -852223038}, {"key": "androidx/lifecycle/viewmodel/internal/ViewModelImpl.class", "name": "androidx/lifecycle/viewmodel/internal/ViewModelImpl.class", "size": 7716, "crc": -167973132}, {"key": "androidx/lifecycle/viewmodel/internal/ViewModelProviders$ViewModelKey.class", "name": "androidx/lifecycle/viewmodel/internal/ViewModelProviders$ViewModelKey.class", "size": 1171, "crc": -396670233}, {"key": "androidx/lifecycle/viewmodel/internal/ViewModelProviders.class", "name": "androidx/lifecycle/viewmodel/internal/ViewModelProviders.class", "size": 8696, "crc": **********}, {"key": "androidx/lifecycle/viewmodel/internal/ViewModelProviders_jvmKt.class", "name": "androidx/lifecycle/viewmodel/internal/ViewModelProviders_jvmKt.class", "size": 1162, "crc": **********}, {"key": "META-INF/androidx.lifecycle_lifecycle-viewmodel.version", "name": "META-INF/androidx.lifecycle_lifecycle-viewmodel.version", "size": 6, "crc": -**********}, {"key": "META-INF/lifecycle-viewmodel_release.kotlin_module", "name": "META-INF/lifecycle-viewmodel_release.kotlin_module", "size": 372, "crc": **********}]