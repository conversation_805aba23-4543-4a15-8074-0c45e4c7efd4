package com.falaileh.nisso.ui.components

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.tv.material3.ExperimentalTvMaterial3Api
import androidx.tv.material3.Text
import com.falaileh.nisso.data.model.LoveMessage
import com.falaileh.nisso.data.model.LoveMessageUiState
import com.falaileh.nisso.ui.animations.RomanticLoadingAnimation
import com.falaileh.nisso.ui.utils.ColorUtils
import com.falaileh.nisso.ui.utils.TypewriterText

/**
 * Main screen for displaying love messages with romantic styling
 */
@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
fun LoveMessageScreen(
    uiState: LoveMessageUiState,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when {
            uiState.isLoading && uiState.messages.isEmpty() -> {
                RomanticLoadingScreen(
                    message = "Loading love messages... 💕",
                    isRefreshing = false
                )
            }
            uiState.hasMessages -> {
                uiState.currentMessage?.let { message ->
                    MessageDisplay(
                        message = message,
                        isRefreshing = uiState.isRefreshing,
                        messageIndex = uiState.currentMessageIndex,
                        totalMessages = uiState.messageCount
                    )
                }
            }
            uiState.error != null -> {
                RomanticErrorScreen(
                    error = uiState.error,
                    isOffline = uiState.isOffline
                )
            }
            else -> {
                EmptyScreen()
            }
        }
        
        // Floating hearts animation overlay
        FloatingHeartsOverlay()
    }
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
private fun MessageDisplay(
    message: LoveMessage,
    isRefreshing: Boolean,
    messageIndex: Int,
    totalMessages: Int,
    modifier: Modifier = Modifier
) {
    val backgroundColor = ColorUtils.parseColor(message.bgColor)
    val textColor = ColorUtils.parseColor(message.textColor)
    
    AnimatedContent(
        targetState = message,
        transitionSpec = {
            fadeIn(animationSpec = tween(800)) togetherWith 
            fadeOut(animationSpec = tween(400))
        },
        label = "message_transition"
    ) { targetMessage ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            backgroundColor,
                            backgroundColor.copy(alpha = 0.8f),
                            Color.Black.copy(alpha = 0.3f)
                        ),
                        radius = 1200f
                    )
                )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(48.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // Main message text with typewriter effect
                TypewriterText(
                    text = targetMessage.message,
                    textColor = textColor,
                    fontSize = 48.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 32.dp)
                )
                
                Spacer(modifier = Modifier.height(32.dp))
                
                // Message counter
                MessageCounter(
                    currentIndex = messageIndex,
                    totalCount = totalMessages,
                    textColor = textColor.copy(alpha = 0.7f)
                )
            }
            
            // Refresh indicator
            if (isRefreshing) {
                RefreshIndicator(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(32.dp),
                    color = textColor
                )
            }
            
            // Navigation hints
            NavigationHints(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(24.dp),
                textColor = textColor.copy(alpha = 0.6f)
            )
        }
    }
}



@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
private fun EmptyScreen(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(48.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "💌",
            fontSize = 72.sp
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Text(
            text = "No love messages yet",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Press SELECT to load messages",
            fontSize = 18.sp,
            color = Color.White.copy(alpha = 0.8f),
            textAlign = TextAlign.Center
        )
    }
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
private fun MessageCounter(
    currentIndex: Int,
    totalCount: Int,
    textColor: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(20.dp))
            .background(Color.Black.copy(alpha = 0.3f))
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Text(
            text = "${currentIndex + 1} of $totalCount",
            fontSize = 16.sp,
            color = textColor,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun RefreshIndicator(
    color: Color,
    modifier: Modifier = Modifier
) {
    // Custom refresh indicator using romantic loading animation
    RomanticLoadingAnimation(
        modifier = modifier.size(32.dp),
        color = color
    )
}

@OptIn(ExperimentalTvMaterial3Api::class)
@Composable
private fun NavigationHints(
    textColor: Color,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(32.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "← PREV",
            fontSize = 14.sp,
            color = textColor,
            fontWeight = FontWeight.Medium
        )
        
        Text(
            text = "SELECT to refresh",
            fontSize = 14.sp,
            color = textColor,
            fontWeight = FontWeight.Medium
        )
        
        Text(
            text = "NEXT →",
            fontSize = 14.sp,
            color = textColor,
            fontWeight = FontWeight.Medium
        )
    }
}
