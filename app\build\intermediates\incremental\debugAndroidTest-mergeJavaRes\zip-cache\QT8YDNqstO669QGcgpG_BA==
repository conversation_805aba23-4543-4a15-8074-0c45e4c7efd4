[{"key": "androidx/test/core/view/WindowCapture.class", "name": "androidx/test/core/view/WindowCapture.class", "size": 6117, "crc": 1015428259}, {"key": "androidx/test/core/view/WindowCapture$generateBitmapFromPixelCopy$onCopyFinished$1.class", "name": "androidx/test/core/view/WindowCapture$generateBitmapFromPixelCopy$onCopyFinished$1.class", "size": 2423, "crc": -1031157416}, {"key": "androidx/test/core/view/WindowCapture$captureRegionToBitmap$2.class", "name": "androidx/test/core/view/WindowCapture$captureRegionToBitmap$2.class", "size": 2549, "crc": -1673934630}, {"key": "androidx/test/core/view/WindowCapture$captureRegionToBitmap$2$1.class", "name": "androidx/test/core/view/WindowCapture$captureRegionToBitmap$2$1.class", "size": 1496, "crc": -1153755521}, {"key": "androidx/test/core/view/WindowCapture$captureRegionToBitmap$1.class", "name": "androidx/test/core/view/WindowCapture$captureRegionToBitmap$1.class", "size": 1048, "crc": 1953693285}, {"key": "androidx/test/core/view/ViewCapture.class", "name": "androidx/test/core/view/ViewCapture.class", "size": 9080, "crc": 1536229278}, {"key": "androidx/test/core/view/ViewCapture$generateBitmapFromSurfaceViewPixelCopy$onCopyFinished$1.class", "name": "androidx/test/core/view/ViewCapture$generateBitmapFromSurfaceViewPixelCopy$onCopyFinished$1.class", "size": 2430, "crc": -1004601237}, {"key": "androidx/test/core/view/ViewCapture$forceRedraw$2.class", "name": "androidx/test/core/view/ViewCapture$forceRedraw$2.class", "size": 2207, "crc": 454655774}, {"key": "androidx/test/core/view/ViewCapture$forceRedraw$2$onDraw$1.class", "name": "androidx/test/core/view/ViewCapture$forceRedraw$2$onDraw$1.class", "size": 1332, "crc": 1004585646}, {"key": "androidx/test/core/view/ViewCapture$forceRedraw$1.class", "name": "androidx/test/core/view/ViewCapture$forceRedraw$1.class", "size": 1240, "crc": 2037466102}, {"key": "androidx/test/core/view/ViewCapture$captureToBitmap$2.class", "name": "androidx/test/core/view/ViewCapture$captureToBitmap$2.class", "size": 1988, "crc": 1098979967}, {"key": "androidx/test/core/view/ViewCapture$captureToBitmap$2$1.class", "name": "androidx/test/core/view/ViewCapture$captureToBitmap$2$1.class", "size": 1332, "crc": 1592928452}, {"key": "androidx/test/core/view/ViewCapture$captureToBitmap$1.class", "name": "androidx/test/core/view/ViewCapture$captureToBitmap$1.class", "size": 997, "crc": 297513023}, {"key": "androidx/test/core/view/PointerPropertiesBuilder.class", "name": "androidx/test/core/view/PointerPropertiesBuilder.class", "size": 1165, "crc": -1188554628}, {"key": "androidx/test/core/view/PointerCoordsBuilder.class", "name": "androidx/test/core/view/PointerCoordsBuilder.class", "size": 2010, "crc": 797213476}, {"key": "androidx/test/core/view/MotionEventBuilder.class", "name": "androidx/test/core/view/MotionEventBuilder.class", "size": 4660, "crc": -2129818690}, {"key": "androidx/test/core/os/Parcelables.class", "name": "androidx/test/core/os/Parcelables.class", "size": 1317, "crc": 528075827}, {"key": "androidx/test/core/internal/os/HandlerExecutor.class", "name": "androidx/test/core/internal/os/HandlerExecutor.class", "size": 1983, "crc": -14488748}, {"key": "androidx/test/core/graphics/BitmapStorage.class", "name": "androidx/test/core/graphics/BitmapStorage.class", "size": 2779, "crc": -1970048138}, {"key": "androidx/test/core/content/pm/PackageInfoBuilder.class", "name": "androidx/test/core/content/pm/PackageInfoBuilder.class", "size": 2089, "crc": -988599063}, {"key": "androidx/test/core/content/pm/ApplicationInfoBuilder.class", "name": "androidx/test/core/content/pm/ApplicationInfoBuilder.class", "size": 1437, "crc": 2058496205}, {"key": "androidx/test/core/app/ListFuture.class", "name": "androidx/test/core/app/ListFuture.class", "size": 8829, "crc": 1206435039}, {"key": "androidx/test/core/app/ListFuture$3.class", "name": "androidx/test/core/app/ListFuture$3.class", "size": 1047, "crc": -1599139949}, {"key": "androidx/test/core/app/ListFuture$2.class", "name": "androidx/test/core/app/ListFuture$2.class", "size": 837, "crc": 1973542744}, {"key": "androidx/test/core/app/ListFuture$1.class", "name": "androidx/test/core/app/ListFuture$1.class", "size": 2182, "crc": -1412495215}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker.class", "size": 11838, "crc": -1751504988}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$EmptyFloatingActivity.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$EmptyFloatingActivity.class", "size": 1990, "crc": -315374826}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$EmptyFloatingActivity$1.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$EmptyFloatingActivity$1.class", "size": 1155, "crc": -1446672898}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$EmptyActivity.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$EmptyActivity.class", "size": 1941, "crc": 1602083470}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$EmptyActivity$1.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$EmptyActivity$1.class", "size": 1107, "crc": 348302497}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$BootstrapActivity.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$BootstrapActivity.class", "size": 4855, "crc": 1680150429}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$BootstrapActivity$1.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$BootstrapActivity$1.class", "size": 1177, "crc": -1730801295}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$ActivityResultWaiter.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$ActivityResultWaiter.class", "size": 3302, "crc": -534660316}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$ActivityResultWaiter$1.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$ActivityResultWaiter$1.class", "size": 2564, "crc": -733889986}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$2.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$2.class", "size": 1121, "crc": -1927135206}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$1.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$1.class", "size": 1129, "crc": 430020110}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$$ExternalSyntheticLambda2.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$$ExternalSyntheticLambda2.class", "size": 545, "crc": 1738127080}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$$ExternalSyntheticLambda1.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$$ExternalSyntheticLambda1.class", "size": 669, "crc": 40026582}, {"key": "androidx/test/core/app/InstrumentationActivityInvoker$$ExternalSyntheticLambda0.class", "name": "androidx/test/core/app/InstrumentationActivityInvoker$$ExternalSyntheticLambda0.class", "size": 547, "crc": 689899954}, {"key": "androidx/test/core/app/DirectExecutor.class", "name": "androidx/test/core/app/DirectExecutor.class", "size": 1506, "crc": 1640335263}, {"key": "androidx/test/core/app/DeviceCapture.class", "name": "androidx/test/core/app/DeviceCapture.class", "size": 5705, "crc": -1005817384}, {"key": "androidx/test/core/app/DeviceCapture$takeScreenshotNoSync$2.class", "name": "androidx/test/core/app/DeviceCapture$takeScreenshotNoSync$2.class", "size": 1611, "crc": -1442344023}, {"key": "androidx/test/core/app/DeviceCapture$takeScreenshotNoSync$2$1.class", "name": "androidx/test/core/app/DeviceCapture$takeScreenshotNoSync$2$1.class", "size": 1891, "crc": -**********}, {"key": "androidx/test/core/app/DeviceCapture$takeScreenshotNoSync$1.class", "name": "androidx/test/core/app/DeviceCapture$takeScreenshotNoSync$1.class", "size": 971, "crc": 764315284}, {"key": "androidx/test/core/app/DeviceCapture$forceRedrawGlobalWindowViews$1.class", "name": "androidx/test/core/app/DeviceCapture$forceRedrawGlobalWindowViews$1.class", "size": 2953, "crc": 338429645}, {"key": "androidx/test/core/app/ApplicationProvider.class", "name": "androidx/test/core/app/ApplicationProvider.class", "size": 713, "crc": -324053732}, {"key": "androidx/test/core/app/ActivityScenario.class", "name": "androidx/test/core/app/ActivityScenario.class", "size": 17993, "crc": -**********}, {"key": "androidx/test/core/app/ActivityScenario$ActivityState.class", "name": "androidx/test/core/app/ActivityScenario$ActivityState.class", "size": 1351, "crc": **********}, {"key": "androidx/test/core/app/ActivityScenario$ActivityAction.class", "name": "androidx/test/core/app/ActivityScenario$ActivityAction.class", "size": 415, "crc": -472204870}, {"key": "androidx/test/core/app/ActivityScenario$2.class", "name": "androidx/test/core/app/ActivityScenario$2.class", "size": 1184, "crc": -243628264}, {"key": "androidx/test/core/app/ActivityScenario$1.class", "name": "androidx/test/core/app/ActivityScenario$1.class", "size": 3637, "crc": **********}, {"key": "androidx/test/core/app/ActivityScenario$$ExternalSyntheticLambda2.class", "name": "androidx/test/core/app/ActivityScenario$$ExternalSyntheticLambda2.class", "size": 851, "crc": 1316333544}, {"key": "androidx/test/core/app/ActivityScenario$$ExternalSyntheticLambda1.class", "name": "androidx/test/core/app/ActivityScenario$$ExternalSyntheticLambda1.class", "size": 748, "crc": -1870099326}, {"key": "androidx/test/core/app/ActivityScenario$$ExternalSyntheticLambda0.class", "name": "androidx/test/core/app/ActivityScenario$$ExternalSyntheticLambda0.class", "size": 748, "crc": 1288158534}, {"key": "META-INF/androidx.test.core.kotlin_module", "name": "META-INF/androidx.test.core.kotlin_module", "size": 162, "crc": 1208600694}]