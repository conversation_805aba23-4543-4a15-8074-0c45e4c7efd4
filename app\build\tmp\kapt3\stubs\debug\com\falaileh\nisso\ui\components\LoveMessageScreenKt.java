package com.falaileh.nisso.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\f\u001a\u0012\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u001a\u001a\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u001a4\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\r\u0010\u000e\u001a2\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\t2\u0006\u0010\u0015\u001a\u00020\t2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u001a8\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\u0017\u001a\u00020\u00132\b\b\u0002\u0010\u0018\u001a\u00020\u00132\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0019\u0010\u001a\u001a$\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\f2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001d\u0010\u001e\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u001f"}, d2 = {"EmptyScreen", "", "modifier", "Landroidx/compose/ui/Modifier;", "LoveMessageScreen", "uiState", "Lcom/falaileh/nisso/data/model/LoveMessageUiState;", "MessageCounter", "currentIndex", "", "totalCount", "textColor", "Landroidx/compose/ui/graphics/Color;", "MessageCounter-9LQNqLg", "(IIJLandroidx/compose/ui/Modifier;)V", "MessageDisplay", "message", "Lcom/falaileh/nisso/data/model/LoveMessage;", "isRefreshing", "", "messageIndex", "totalMessages", "NavigationHints", "isFirstMessage", "isLastMessage", "NavigationHints-Iv8Zu3U", "(JZZLandroidx/compose/ui/Modifier;)V", "RefreshIndicator", "color", "RefreshIndicator-DxMtmZc", "(JLandroidx/compose/ui/Modifier;)V", "app_debug"})
public final class LoveMessageScreenKt {
    
    /**
     * Main screen for displaying love messages with romantic styling
     */
    @kotlin.OptIn(markerClass = {androidx.tv.material3.ExperimentalTvMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void LoveMessageScreen(@org.jetbrains.annotations.NotNull()
    com.falaileh.nisso.data.model.LoveMessageUiState uiState, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.tv.material3.ExperimentalTvMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void MessageDisplay(com.falaileh.nisso.data.model.LoveMessage message, boolean isRefreshing, int messageIndex, int totalMessages, androidx.compose.ui.Modifier modifier) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.tv.material3.ExperimentalTvMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void EmptyScreen(androidx.compose.ui.Modifier modifier) {
    }
}