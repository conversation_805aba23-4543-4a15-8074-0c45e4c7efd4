{"logs": [{"outputFile": "com.falaileh.nisso.test.app-mergeDebugAndroidTestResources-34:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\910260a50c4cc0fe03b922548d59c7fb\\transformed\\core-1.13.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,605,707,2072", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "196,298,397,496,600,702,818,2168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8dca2d17bb97bd73368ea919040cc370\\transformed\\ui-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,558,640,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,75,71,69,76,65,119", "endOffsets": "192,274,368,467,553,635,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1612"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "823,915,997,1091,1190,1276,1358,1448,1537,1621,1699,1781,1854,1930,2002,2173,2250,2316", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,75,71,69,76,65,119", "endOffsets": "910,992,1086,1185,1271,1353,1443,1532,1616,1694,1776,1849,1925,1997,2067,2245,2311,2431"}}]}]}