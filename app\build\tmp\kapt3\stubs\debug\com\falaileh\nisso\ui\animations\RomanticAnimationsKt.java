package com.falaileh.nisso.ui.animations;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000F\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a,\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u0007\u001a0\u0010\t\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\b2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\r\u0010\u000e\u001a&\u0010\u000f\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\bH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0011\u0010\u0012\u001a\"\u0010\u0013\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u0007\u001a*\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\b0\u00152\u0006\u0010\u0016\u001a\u00020\b2\b\b\u0002\u0010\u0017\u001a\u00020\u0005H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0018\u0010\u0019\u001a\b\u0010\u001a\u001a\u00020\u001bH\u0007\u001a\u0018\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\f0\u00152\b\b\u0002\u0010\u0017\u001a\u00020\u0005H\u0007\u001a.\u0010\u001d\u001a\u00020\u0001*\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\bH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b\"\u0010#\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006$"}, d2 = {"FloatingParticlesAnimation", "", "modifier", "Landroidx/compose/ui/Modifier;", "particleCount", "", "colors", "", "Landroidx/compose/ui/graphics/Color;", "RomanticGlowEffect", "glowColor", "intensity", "", "RomanticGlowEffect-bw27NRU", "(Landroidx/compose/ui/Modifier;JF)V", "RomanticLoadingAnimation", "color", "RomanticLoadingAnimation-4WTKRHQ", "(Landroidx/compose/ui/Modifier;J)V", "RomanticShimmerEffect", "animateBackgroundColor", "Landroidx/compose/runtime/State;", "targetColor", "durationMs", "animateBackgroundColor-DxMtmZc", "(JI)Landroidx/compose/runtime/State;", "rememberMessageTransition", "Lcom/falaileh/nisso/ui/animations/MessageTransitionState;", "rememberPulseAnimation", "drawHeart", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "center", "Landroidx/compose/ui/geometry/Offset;", "size", "drawHeart-icVWoeI", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;JFJ)V", "app_debug"})
public final class RomanticAnimationsKt {
    
    /**
     * Romantic pulse animation for text
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.runtime.State<java.lang.Float> rememberPulseAnimation(int durationMs) {
        return null;
    }
    
    /**
     * Romantic shimmer effect
     */
    @androidx.compose.runtime.Composable()
    public static final void RomanticShimmerEffect(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    java.util.List<androidx.compose.ui.graphics.Color> colors) {
    }
    
    /**
     * Floating particles animation
     */
    @androidx.compose.runtime.Composable()
    public static final void FloatingParticlesAnimation(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, int particleCount, @org.jetbrains.annotations.NotNull()
    java.util.List<androidx.compose.ui.graphics.Color> colors) {
    }
    
    /**
     * Message transition animation
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final com.falaileh.nisso.ui.animations.MessageTransitionState rememberMessageTransition() {
        return null;
    }
}