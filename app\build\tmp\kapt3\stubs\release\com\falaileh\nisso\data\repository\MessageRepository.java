package com.falaileh.nisso.data.repository;

/**
 * Repository for managing love messages from API and local database
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u0011\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010\rJ \u0010\u0013\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\u00150\u0014H\u0086@\u00a2\u0006\u0002\u0010\rJ\u0012\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\u0014J\b\u0010\u0017\u001a\u00020\u0018H\u0002J\"\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\u0015H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001a\u0010\rR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u001b"}, d2 = {"Lcom/falaileh/nisso/data/repository/MessageRepository;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "apiService", "Lcom/falaileh/nisso/data/api/LoveMessageApiService;", "dao", "Lcom/falaileh/nisso/data/database/LoveMessageDao;", "database", "Lcom/falaileh/nisso/data/database/LoveMessageDatabase;", "cleanOldCache", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getLocalMessages", "", "Lcom/falaileh/nisso/data/model/LoveMessage;", "getMessageCount", "", "getMessages", "Lkotlinx/coroutines/flow/Flow;", "Lkotlin/Result;", "getMessagesFlow", "isNetworkAvailable", "", "refreshMessages", "refreshMessages-IoAF18A", "app_release"})
public final class MessageRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.falaileh.nisso.data.api.LoveMessageApiService apiService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.falaileh.nisso.data.database.LoveMessageDatabase database = null;
    @org.jetbrains.annotations.NotNull()
    private final com.falaileh.nisso.data.database.LoveMessageDao dao = null;
    
    public MessageRepository(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Get messages from local database as Flow
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.falaileh.nisso.data.model.LoveMessage>> getMessagesFlow() {
        return null;
    }
    
    /**
     * Get messages from local database synchronously
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getLocalMessages(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.falaileh.nisso.data.model.LoveMessage>> $completion) {
        return null;
    }
    
    /**
     * Get messages with automatic fallback to cache
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMessages(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends kotlin.Result<? extends java.util.List<com.falaileh.nisso.data.model.LoveMessage>>>> $completion) {
        return null;
    }
    
    /**
     * Check if network is available
     */
    private final boolean isNetworkAvailable() {
        return false;
    }
    
    /**
     * Get message count
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMessageCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    /**
     * Clean old cached messages (older than 24 hours)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cleanOldCache(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}