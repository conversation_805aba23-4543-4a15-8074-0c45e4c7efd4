[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 59, "crc": -1734853537}, {"key": "retrofit2/ParameterHandler$2.class", "name": "retrofit2/ParameterHandler$2.class", "size": 1250, "crc": -827011544}, {"key": "retrofit2/OptionalConverterFactory$OptionalConverter.class", "name": "retrofit2/OptionalConverterFactory$OptionalConverter.class", "size": 1609, "crc": 785836709}, {"key": "retrofit2/CompletableFutureCallAdapterFactory$CallCancelCompletableFuture.class", "name": "retrofit2/CompletableFutureCallAdapterFactory$CallCancelCompletableFuture.class", "size": 1238, "crc": -1257502954}, {"key": "retrofit2/CompletableFutureCallAdapterFactory$BodyCallAdapter.class", "name": "retrofit2/CompletableFutureCallAdapterFactory$BodyCallAdapter.class", "size": 2094, "crc": -87750248}, {"key": "retrofit2/CompletableFutureCallAdapterFactory$ResponseCallAdapter.class", "name": "retrofit2/CompletableFutureCallAdapterFactory$ResponseCallAdapter.class", "size": 2192, "crc": -822673046}, {"key": "retrofit2/Utils$WildcardTypeImpl.class", "name": "retrofit2/Utils$WildcardTypeImpl.class", "size": 2095, "crc": -2109722762}, {"key": "retrofit2/ParameterHandler$Field.class", "name": "retrofit2/ParameterHandler$Field.class", "size": 1761, "crc": -1335366073}, {"key": "retrofit2/OkHttpCall$1.class", "name": "retrofit2/OkHttpCall$1.class", "size": 1896, "crc": -2100988594}, {"key": "retrofit2/OkHttpCall.class", "name": "retrofit2/OkHttpCall.class", "size": 7161, "crc": 765961793}, {"key": "retrofit2/DefaultCallAdapterFactory.class", "name": "retrofit2/DefaultCallAdapterFactory.class", "size": 2148, "crc": -1859690578}, {"key": "retrofit2/Utils$ParameterizedTypeImpl.class", "name": "retrofit2/Utils$ParameterizedTypeImpl.class", "size": 2728, "crc": 659793488}, {"key": "retrofit2/HttpServiceMethod.class", "name": "retrofit2/HttpServiceMethod.class", "size": 6892, "crc": -272632262}, {"key": "retrofit2/ParameterHandler$1.class", "name": "retrofit2/ParameterHandler$1.class", "size": 1588, "crc": 784289040}, {"key": "retrofit2/Platform$Android$MainThreadExecutor.class", "name": "retrofit2/Platform$Android$MainThreadExecutor.class", "size": 873, "crc": 189631727}, {"key": "retrofit2/ParameterHandler$QueryName.class", "name": "retrofit2/ParameterHandler$QueryName.class", "size": 1494, "crc": -629087918}, {"key": "retrofit2/CompletableFutureCallAdapterFactory$ResponseCallAdapter$ResponseCallback.class", "name": "retrofit2/CompletableFutureCallAdapterFactory$ResponseCallAdapter$ResponseCallback.class", "size": 2192, "crc": -91395789}, {"key": "retrofit2/HttpServiceMethod$SuspendForBody.class", "name": "retrofit2/HttpServiceMethod$SuspendForBody.class", "size": 2519, "crc": 688513414}, {"key": "retrofit2/ParameterHandler$Query.class", "name": "retrofit2/ParameterHandler$Query.class", "size": 1762, "crc": 398957198}, {"key": "retrofit2/ParameterHandler$RawPart.class", "name": "retrofit2/ParameterHandler$RawPart.class", "size": 1229, "crc": 173263797}, {"key": "retrofit2/internal/EverythingIsNonNull.class", "name": "retrofit2/internal/EverythingIsNonNull.class", "size": 528, "crc": 157704995}, {"key": "retrofit2/ParameterHandler$RelativeUrl.class", "name": "retrofit2/ParameterHandler$RelativeUrl.class", "size": 1226, "crc": -1816519411}, {"key": "retrofit2/HttpException.class", "name": "retrofit2/HttpException.class", "size": 1595, "crc": 1587594588}, {"key": "retrofit2/HttpServiceMethod$SuspendForResponse.class", "name": "retrofit2/HttpServiceMethod$SuspendForResponse.class", "size": 2444, "crc": 2095670636}, {"key": "retrofit2/Converter$Factory.class", "name": "retrofit2/Converter$Factory.class", "size": 2077, "crc": 1112777970}, {"key": "retrofit2/OkHttpCall$NoContentResponseBody.class", "name": "retrofit2/OkHttpCall$NoContentResponseBody.class", "size": 1066, "crc": -941324218}, {"key": "retrofit2/ParameterHandler$Header.class", "name": "retrofit2/ParameterHandler$Header.class", "size": 1705, "crc": -460077722}, {"key": "retrofit2/OptionalConverterFactory.class", "name": "retrofit2/OptionalConverterFactory.class", "size": 1910, "crc": -1005480419}, {"key": "retrofit2/HttpServiceMethod$CallAdapted.class", "name": "retrofit2/HttpServiceMethod$CallAdapted.class", "size": 1800, "crc": 601832732}, {"key": "retrofit2/RequestBuilder.class", "name": "retrofit2/RequestBuilder.class", "size": 8669, "crc": 631627994}, {"key": "retrofit2/Invocation.class", "name": "retrofit2/Invocation.class", "size": 1801, "crc": -1648156312}, {"key": "retrofit2/Retrofit$Builder.class", "name": "retrofit2/Retrofit$Builder.class", "size": 5790, "crc": -400404257}, {"key": "retrofit2/ParameterHandler$Part.class", "name": "retrofit2/ParameterHandler$Part.class", "size": 2263, "crc": -1680565301}, {"key": "retrofit2/ServiceMethod.class", "name": "retrofit2/ServiceMethod.class", "size": 1913, "crc": -596137454}, {"key": "retrofit2/Response.class", "name": "retrofit2/Response.class", "size": 5358, "crc": 944934801}, {"key": "retrofit2/package-info.class", "name": "retrofit2/package-info.class", "size": 198, "crc": -884426283}, {"key": "retrofit2/DefaultCallAdapterFactory$ExecutorCallbackCall.class", "name": "retrofit2/DefaultCallAdapterFactory$ExecutorCallbackCall.class", "size": 2694, "crc": -1835750567}, {"key": "retrofit2/ParameterHandler$Tag.class", "name": "retrofit2/ParameterHandler$Tag.class", "size": 1143, "crc": 508631001}, {"key": "retrofit2/http/Streaming.class", "name": "retrofit2/http/Streaming.class", "size": 426, "crc": 121197808}, {"key": "retrofit2/http/Query.class", "name": "retrofit2/http/Query.class", "size": 510, "crc": -650175794}, {"key": "retrofit2/http/OPTIONS.class", "name": "retrofit2/http/OPTIONS.class", "size": 485, "crc": 25999090}, {"key": "retrofit2/http/FormUrlEncoded.class", "name": "retrofit2/http/FormUrlEncoded.class", "size": 436, "crc": -673386069}, {"key": "retrofit2/http/HeaderMap.class", "name": "retrofit2/http/HeaderMap.class", "size": 429, "crc": 841759200}, {"key": "retrofit2/http/PartMap.class", "name": "retrofit2/http/PartMap.class", "size": 505, "crc": -1625643048}, {"key": "retrofit2/http/HEAD.class", "name": "retrofit2/http/HEAD.class", "size": 479, "crc": -1426523995}, {"key": "retrofit2/http/Field.class", "name": "retrofit2/http/Field.class", "size": 510, "crc": -1678215637}, {"key": "retrofit2/http/Headers.class", "name": "retrofit2/http/Headers.class", "size": 454, "crc": 217998752}, {"key": "retrofit2/http/DELETE.class", "name": "retrofit2/http/DELETE.class", "size": 483, "crc": -1090570809}, {"key": "retrofit2/http/FieldMap.class", "name": "retrofit2/http/FieldMap.class", "size": 485, "crc": 2014267287}, {"key": "retrofit2/http/Header.class", "name": "retrofit2/http/Header.class", "size": 454, "crc": 1055551437}, {"key": "retrofit2/http/HTTP.class", "name": "retrofit2/http/HTTP.class", "size": 541, "crc": -779940246}, {"key": "retrofit2/http/PATCH.class", "name": "retrofit2/http/PATCH.class", "size": 481, "crc": -1052952484}, {"key": "retrofit2/http/Path.class", "name": "retrofit2/http/Path.class", "size": 508, "crc": 1547731951}, {"key": "retrofit2/http/POST.class", "name": "retrofit2/http/POST.class", "size": 479, "crc": 1841501264}, {"key": "retrofit2/http/Part.class", "name": "retrofit2/http/Part.class", "size": 519, "crc": 142261089}, {"key": "retrofit2/http/Url.class", "name": "retrofit2/http/Url.class", "size": 417, "crc": 2105738887}, {"key": "retrofit2/http/Multipart.class", "name": "retrofit2/http/Multipart.class", "size": 426, "crc": 1286377671}, {"key": "retrofit2/http/QueryMap.class", "name": "retrofit2/http/QueryMap.class", "size": 485, "crc": -1922723308}, {"key": "retrofit2/http/PUT.class", "name": "retrofit2/http/PUT.class", "size": 477, "crc": -297605700}, {"key": "retrofit2/http/Tag.class", "name": "retrofit2/http/Tag.class", "size": 417, "crc": -2048934829}, {"key": "retrofit2/http/GET.class", "name": "retrofit2/http/GET.class", "size": 477, "crc": 1030554344}, {"key": "retrofit2/http/Body.class", "name": "retrofit2/http/Body.class", "size": 419, "crc": 1017640160}, {"key": "retrofit2/http/QueryName.class", "name": "retrofit2/http/QueryName.class", "size": 487, "crc": -1753343601}, {"key": "retrofit2/ParameterHandler$QueryMap.class", "name": "retrofit2/ParameterHandler$QueryMap.class", "size": 3419, "crc": 1951667661}, {"key": "retrofit2/ParameterHandler$Headers.class", "name": "retrofit2/ParameterHandler$Headers.class", "size": 1430, "crc": -1425703174}, {"key": "retrofit2/SkipCallbackExecutorImpl.class", "name": "retrofit2/SkipCallbackExecutorImpl.class", "size": 1692, "crc": -1104705763}, {"key": "retrofit2/Platform.class", "name": "retrofit2/Platform.class", "size": 4829, "crc": -1253995690}, {"key": "retrofit2/SkipCallbackExecutor.class", "name": "retrofit2/SkipCallbackExecutor.class", "size": 443, "crc": 1092332262}, {"key": "retrofit2/BuiltInConverters$StreamingResponseBodyConverter.class", "name": "retrofit2/BuiltInConverters$StreamingResponseBodyConverter.class", "size": 1034, "crc": 656878527}, {"key": "retrofit2/CallAdapter$Factory.class", "name": "retrofit2/CallAdapter$Factory.class", "size": 1150, "crc": -1062496971}, {"key": "retrofit2/ParameterHandler$Path.class", "name": "retrofit2/ParameterHandler$Path.class", "size": 2300, "crc": 731938632}, {"key": "retrofit2/ParameterHandler$PartMap.class", "name": "retrofit2/ParameterHandler$PartMap.class", "size": 3393, "crc": -462741488}, {"key": "retrofit2/Retrofit.class", "name": "retrofit2/Retrofit.class", "size": 11735, "crc": -473214435}, {"key": "retrofit2/RequestFactory$Builder.class", "name": "retrofit2/RequestFactory$Builder.class", "size": 22726, "crc": -1043513669}, {"key": "retrofit2/CompletableFutureCallAdapterFactory$BodyCallAdapter$BodyCallback.class", "name": "retrofit2/CompletableFutureCallAdapterFactory$BodyCallAdapter$BodyCallback.class", "size": 2327, "crc": -1569914745}, {"key": "retrofit2/BuiltInConverters$BufferingResponseBodyConverter.class", "name": "retrofit2/BuiltInConverters$BufferingResponseBodyConverter.class", "size": 1195, "crc": -1770230298}, {"key": "retrofit2/RequestBuilder$ContentTypeOverridingRequestBody.class", "name": "retrofit2/RequestBuilder$ContentTypeOverridingRequestBody.class", "size": 1039, "crc": -1019858355}, {"key": "retrofit2/BuiltInConverters$ToStringConverter.class", "name": "retrofit2/BuiltInConverters$ToStringConverter.class", "size": 993, "crc": 1307004731}, {"key": "retrofit2/OkHttpCall$ExceptionCatchingResponseBody.class", "name": "retrofit2/OkHttpCall$ExceptionCatchingResponseBody.class", "size": 1539, "crc": 1729883699}, {"key": "retrofit2/DefaultCallAdapterFactory$1.class", "name": "retrofit2/DefaultCallAdapterFactory$1.class", "size": 1751, "crc": 1921843782}, {"key": "retrofit2/Converter.class", "name": "retrofit2/Converter.class", "size": 462, "crc": -356390096}, {"key": "retrofit2/Utils.class", "name": "retrofit2/Utils.class", "size": 12038, "crc": -1485069141}, {"key": "retrofit2/ParameterHandler$Body.class", "name": "retrofit2/ParameterHandler$Body.class", "size": 2330, "crc": -963634566}, {"key": "retrofit2/CompletableFutureCallAdapterFactory.class", "name": "retrofit2/CompletableFutureCallAdapterFactory.class", "size": 2333, "crc": 1595114931}, {"key": "retrofit2/Retrofit$1.class", "name": "retrofit2/Retrofit$1.class", "size": 1903, "crc": 636198698}, {"key": "retrofit2/BuiltInConverters$UnitResponseBodyConverter.class", "name": "retrofit2/BuiltInConverters$UnitResponseBodyConverter.class", "size": 1072, "crc": 85824653}, {"key": "retrofit2/CallAdapter.class", "name": "retrofit2/CallAdapter.class", "size": 416, "crc": -103927309}, {"key": "retrofit2/RequestFactory.class", "name": "retrofit2/RequestFactory.class", "size": 3414, "crc": 990911777}, {"key": "retrofit2/Call.class", "name": "retrofit2/Call.class", "size": 641, "crc": 1341148321}, {"key": "retrofit2/BuiltInConverters$RequestBodyConverter.class", "name": "retrofit2/BuiltInConverters$RequestBodyConverter.class", "size": 998, "crc": 1824324395}, {"key": "retrofit2/ParameterHandler$FieldMap.class", "name": "retrofit2/ParameterHandler$FieldMap.class", "size": 3410, "crc": -577727947}, {"key": "retrofit2/ParameterHandler$HeaderMap.class", "name": "retrofit2/ParameterHandler$HeaderMap.class", "size": 3021, "crc": 565787383}, {"key": "retrofit2/ParameterHandler.class", "name": "retrofit2/ParameterHandler.class", "size": 2031, "crc": 1864848940}, {"key": "retrofit2/Callback.class", "name": "retrofit2/Callback.class", "size": 412, "crc": -48254407}, {"key": "retrofit2/OkHttpCall$ExceptionCatchingResponseBody$1.class", "name": "retrofit2/OkHttpCall$ExceptionCatchingResponseBody$1.class", "size": 1108, "crc": 727579973}, {"key": "retrofit2/Utils$GenericArrayTypeImpl.class", "name": "retrofit2/Utils$GenericArrayTypeImpl.class", "size": 1286, "crc": 675510416}, {"key": "retrofit2/DefaultCallAdapterFactory$ExecutorCallbackCall$1.class", "name": "retrofit2/DefaultCallAdapterFactory$ExecutorCallbackCall$1.class", "size": 3098, "crc": -617193889}, {"key": "retrofit2/BuiltInConverters$VoidResponseBodyConverter.class", "name": "retrofit2/BuiltInConverters$VoidResponseBodyConverter.class", "size": 1033, "crc": 1031413762}, {"key": "retrofit2/BuiltInConverters.class", "name": "retrofit2/BuiltInConverters.class", "size": 3036, "crc": 2105207598}, {"key": "retrofit2/Platform$Android.class", "name": "retrofit2/Platform$Android.class", "size": 1560, "crc": 302176391}, {"key": "retrofit2/KotlinExtensions$suspendAndThrow$1.class", "name": "retrofit2/KotlinExtensions$suspendAndThrow$1.class", "size": 1472, "crc": 1089103305}, {"key": "retrofit2/KotlinExtensions.class", "name": "retrofit2/KotlinExtensions.class", "size": 6702, "crc": 990413451}, {"key": "retrofit2/KotlinExtensions$await$4$2.class", "name": "retrofit2/KotlinExtensions$await$4$2.class", "size": 2790, "crc": 569134982}, {"key": "retrofit2/KotlinExtensions$suspendAndThrow$$inlined$suspendCoroutineUninterceptedOrReturn$lambda$1.class", "name": "retrofit2/KotlinExtensions$suspendAndThrow$$inlined$suspendCoroutineUninterceptedOrReturn$lambda$1.class", "size": 1850, "crc": 1753035022}, {"key": "retrofit2/KotlinExtensions$await$$inlined$suspendCancellableCoroutine$lambda$1.class", "name": "retrofit2/KotlinExtensions$await$$inlined$suspendCancellableCoroutine$lambda$1.class", "size": 1550, "crc": -449948372}, {"key": "retrofit2/KotlinExtensions$await$$inlined$suspendCancellableCoroutine$lambda$2.class", "name": "retrofit2/KotlinExtensions$await$$inlined$suspendCancellableCoroutine$lambda$2.class", "size": 1558, "crc": 1248798962}, {"key": "retrofit2/KotlinExtensions$await$2$2.class", "name": "retrofit2/KotlinExtensions$await$2$2.class", "size": 3942, "crc": -472709394}, {"key": "retrofit2/KotlinExtensions$awaitResponse$2$2.class", "name": "retrofit2/KotlinExtensions$awaitResponse$2$2.class", "size": 2429, "crc": -794835547}, {"key": "retrofit2/KotlinExtensions$awaitResponse$$inlined$suspendCancellableCoroutine$lambda$1.class", "name": "retrofit2/KotlinExtensions$awaitResponse$$inlined$suspendCancellableCoroutine$lambda$1.class", "size": 1580, "crc": 1740672941}, {"key": "META-INF/retrofit.kotlin_module", "name": "META-INF/retrofit.kotlin_module", "size": 47, "crc": 328546631}, {"key": "META-INF/proguard/retrofit2.pro", "name": "META-INF/proguard/retrofit2.pro", "size": 1228, "crc": -369734733}]