package com.falaileh.nisso.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0018\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u001a%\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0011\u0010\u0004\u001a\r\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\u0006H\u0007\u00a8\u0006\u0007"}, d2 = {"NissoTheme", "", "isInDarkTheme", "", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "app_release"})
public final class ThemeKt {
    
    @kotlin.OptIn(markerClass = {androidx.tv.material3.ExperimentalTvMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void NissoTheme(boolean isInDarkTheme, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
}