[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 257, "crc": **********}, {"key": "LICENSE.txt", "name": "LICENSE.txt", "size": 1509, "crc": -**********}, {"key": "org/hamcrest/BaseDescription.class", "name": "org/hamcrest/BaseDescription.class", "size": 5248, "crc": **********}, {"key": "org/hamcrest/BaseMatcher.class", "name": "org/hamcrest/BaseMatcher.class", "size": 1344, "crc": 169813417}, {"key": "org/hamcrest/Condition$1.class", "name": "org/hamcrest/Condition$1.class", "size": 204, "crc": -206454798}, {"key": "org/hamcrest/Condition$Matched.class", "name": "org/hamcrest/Condition$Matched.class", "size": 2143, "crc": 702601468}, {"key": "org/hamcrest/Condition$NotMatched.class", "name": "org/hamcrest/Condition$NotMatched.class", "size": 1480, "crc": -974264793}, {"key": "org/hamcrest/Condition$Step.class", "name": "org/hamcrest/Condition$Step.class", "size": 426, "crc": **********}, {"key": "org/hamcrest/Condition.class", "name": "org/hamcrest/Condition.class", "size": 2345, "crc": 292844409}, {"key": "org/hamcrest/CoreMatchers.class", "name": "org/hamcrest/CoreMatchers.class", "size": 11752, "crc": -1543712955}, {"key": "org/hamcrest/CustomMatcher.class", "name": "org/hamcrest/CustomMatcher.class", "size": 957, "crc": -706307629}, {"key": "org/hamcrest/CustomTypeSafeMatcher.class", "name": "org/hamcrest/CustomTypeSafeMatcher.class", "size": 995, "crc": 1367532456}, {"key": "org/hamcrest/Description$NullDescription.class", "name": "org/hamcrest/Description$NullDescription.class", "size": 2195, "crc": -155904054}, {"key": "org/hamcrest/Description.class", "name": "org/hamcrest/Description.class", "size": 1286, "crc": -734380226}, {"key": "org/hamcrest/DiagnosingMatcher.class", "name": "org/hamcrest/DiagnosingMatcher.class", "size": 965, "crc": -226002141}, {"key": "org/hamcrest/Factory.class", "name": "org/hamcrest/Factory.class", "size": 380, "crc": 678301612}, {"key": "org/hamcrest/FeatureMatcher.class", "name": "org/hamcrest/FeatureMatcher.class", "size": 2213, "crc": 892445096}, {"key": "org/hamcrest/Matcher.class", "name": "org/hamcrest/Matcher.class", "size": 508, "crc": 1583707701}, {"key": "org/hamcrest/MatcherAssert.class", "name": "org/hamcrest/MatcherAssert.class", "size": 1755, "crc": -286701439}, {"key": "org/hamcrest/SelfDescribing.class", "name": "org/hamcrest/SelfDescribing.class", "size": 175, "crc": 1821641089}, {"key": "org/hamcrest/StringDescription.class", "name": "org/hamcrest/StringDescription.class", "size": 1638, "crc": -1094500238}, {"key": "org/hamcrest/TypeSafeDiagnosingMatcher.class", "name": "org/hamcrest/TypeSafeDiagnosingMatcher.class", "size": 2057, "crc": 1697003389}, {"key": "org/hamcrest/TypeSafeMatcher.class", "name": "org/hamcrest/TypeSafeMatcher.class", "size": 2313, "crc": 962425419}, {"key": "org/hamcrest/core/AllOf.class", "name": "org/hamcrest/core/AllOf.class", "size": 5473, "crc": -142510336}, {"key": "org/hamcrest/core/AnyOf.class", "name": "org/hamcrest/core/AnyOf.class", "size": 4878, "crc": -1687026132}, {"key": "org/hamcrest/core/CombinableMatcher$CombinableBothMatcher.class", "name": "org/hamcrest/core/CombinableMatcher$CombinableBothMatcher.class", "size": 1119, "crc": -341650797}, {"key": "org/hamcrest/core/CombinableMatcher$CombinableEitherMatcher.class", "name": "org/hamcrest/core/CombinableMatcher$CombinableEitherMatcher.class", "size": 1126, "crc": -1674259489}, {"key": "org/hamcrest/core/CombinableMatcher.class", "name": "org/hamcrest/core/CombinableMatcher.class", "size": 3392, "crc": 1312340727}, {"key": "org/hamcrest/core/DescribedAs.class", "name": "org/hamcrest/core/DescribedAs.class", "size": 2857, "crc": -1273337106}, {"key": "org/hamcrest/core/Every.class", "name": "org/hamcrest/core/Every.class", "size": 2459, "crc": -546740549}, {"key": "org/hamcrest/core/Is.class", "name": "org/hamcrest/core/Is.class", "size": 2560, "crc": 1168418785}, {"key": "org/hamcrest/core/IsAnything.class", "name": "org/hamcrest/core/IsAnything.class", "size": 1500, "crc": -1790408299}, {"key": "org/hamcrest/core/IsCollectionContaining.class", "name": "org/hamcrest/core/IsCollectionContaining.class", "size": 4015, "crc": -1449459654}, {"key": "org/hamcrest/core/IsEqual.class", "name": "org/hamcrest/core/IsEqual.class", "size": 2300, "crc": -1474375599}, {"key": "org/hamcrest/core/IsInstanceOf.class", "name": "org/hamcrest/core/IsInstanceOf.class", "size": 2695, "crc": -1110418290}, {"key": "org/hamcrest/core/IsNot.class", "name": "org/hamcrest/core/IsNot.class", "size": 1809, "crc": 1586167345}, {"key": "org/hamcrest/core/IsNull.class", "name": "org/hamcrest/core/IsNull.class", "size": 1671, "crc": 1168719889}, {"key": "org/hamcrest/core/IsSame.class", "name": "org/hamcrest/core/IsSame.class", "size": 1526, "crc": -1774710816}, {"key": "org/hamcrest/core/ShortcutCombination.class", "name": "org/hamcrest/core/ShortcutCombination.class", "size": 1958, "crc": 1710302884}, {"key": "org/hamcrest/core/StringContains.class", "name": "org/hamcrest/core/StringContains.class", "size": 987, "crc": 143051555}, {"key": "org/hamcrest/core/StringEndsWith.class", "name": "org/hamcrest/core/StringEndsWith.class", "size": 949, "crc": -1310485111}, {"key": "org/hamcrest/core/StringStartsWith.class", "name": "org/hamcrest/core/StringStartsWith.class", "size": 959, "crc": 295160498}, {"key": "org/hamcrest/core/SubstringMatcher.class", "name": "org/hamcrest/core/SubstringMatcher.class", "size": 1619, "crc": -1993666288}, {"key": "org/hamcrest/internal/ArrayIterator.class", "name": "org/hamcrest/internal/ArrayIterator.class", "size": 1253, "crc": -344245987}, {"key": "org/hamcrest/internal/ReflectiveTypeFinder.class", "name": "org/hamcrest/internal/ReflectiveTypeFinder.class", "size": 2088, "crc": 466751217}, {"key": "org/hamcrest/internal/SelfDescribingValue.class", "name": "org/hamcrest/internal/SelfDescribingValue.class", "size": 973, "crc": 1450510999}, {"key": "org/hamcrest/internal/SelfDescribingValueIterator.class", "name": "org/hamcrest/internal/SelfDescribingValueIterator.class", "size": 1328, "crc": -1997645832}]