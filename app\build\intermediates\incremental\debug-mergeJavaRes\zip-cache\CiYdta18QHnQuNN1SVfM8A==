[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "META-INF/room-common.kotlin_module", "name": "META-INF/room-common.kotlin_module", "size": 24, "crc": 1613429616}, {"key": "androidx/room/AmbiguousColumnResolver$Match.class", "name": "androidx/room/AmbiguousColumnResolver$Match.class", "size": 1636, "crc": 19683213}, {"key": "androidx/room/AmbiguousColumnResolver$ResultColumn.class", "name": "androidx/room/AmbiguousColumnResolver$ResultColumn.class", "size": 2844, "crc": -1224614070}, {"key": "androidx/room/AmbiguousColumnResolver$Solution$Companion.class", "name": "androidx/room/AmbiguousColumnResolver$Solution$Companion.class", "size": 5424, "crc": 1192654332}, {"key": "androidx/room/AmbiguousColumnResolver$Solution.class", "name": "androidx/room/AmbiguousColumnResolver$Solution.class", "size": 3000, "crc": 204736927}, {"key": "androidx/room/AmbiguousColumnResolver$resolve$1$1.class", "name": "androidx/room/AmbiguousColumnResolver$resolve$1$1.class", "size": 5324, "crc": 238528908}, {"key": "androidx/room/AmbiguousColumnResolver$resolve$1$2.class", "name": "androidx/room/AmbiguousColumnResolver$resolve$1$2.class", "size": 3517, "crc": -298673378}, {"key": "androidx/room/AmbiguousColumnResolver$resolve$4.class", "name": "androidx/room/AmbiguousColumnResolver$resolve$4.class", "size": 2665, "crc": 1051378543}, {"key": "androidx/room/AmbiguousColumnResolver.class", "name": "androidx/room/AmbiguousColumnResolver.class", "size": 13489, "crc": -1979055869}, {"key": "androidx/room/AutoMigration.class", "name": "androidx/room/AutoMigration.class", "size": 1094, "crc": -1343066248}, {"key": "androidx/room/BuiltInTypeConverters$State.class", "name": "androidx/room/BuiltInTypeConverters$State.class", "size": 1555, "crc": -976607025}, {"key": "androidx/room/BuiltInTypeConverters.class", "name": "androidx/room/BuiltInTypeConverters.class", "size": 1118, "crc": 1283859961}, {"key": "androidx/room/ColumnInfo$Collate.class", "name": "androidx/room/ColumnInfo$Collate.class", "size": 755, "crc": 1275605894}, {"key": "androidx/room/ColumnInfo$Companion.class", "name": "androidx/room/ColumnInfo$Companion.class", "size": 1622, "crc": -827944470}, {"key": "androidx/room/ColumnInfo$SQLiteTypeAffinity.class", "name": "androidx/room/ColumnInfo$SQLiteTypeAffinity.class", "size": 700, "crc": -1213876574}, {"key": "androidx/room/ColumnInfo.class", "name": "androidx/room/ColumnInfo.class", "size": 2305, "crc": 985354303}, {"key": "androidx/room/Dao.class", "name": "androidx/room/Dao.class", "size": 748, "crc": 271026242}, {"key": "androidx/room/Database.class", "name": "androidx/room/Database.class", "size": 1367, "crc": -1375516537}, {"key": "androidx/room/DatabaseView.class", "name": "androidx/room/DatabaseView.class", "size": 938, "crc": 395237428}, {"key": "androidx/room/Delete.class", "name": "androidx/room/Delete.class", "size": 982, "crc": 1087334394}, {"key": "androidx/room/DeleteColumn$Entries.class", "name": "androidx/room/DeleteColumn$Entries.class", "size": 1006, "crc": -550580396}, {"key": "androidx/room/DeleteColumn.class", "name": "androidx/room/DeleteColumn.class", "size": 1081, "crc": 810451836}, {"key": "androidx/room/DeleteTable$Entries.class", "name": "androidx/room/DeleteTable$Entries.class", "size": 1000, "crc": -1388051973}, {"key": "androidx/room/DeleteTable.class", "name": "androidx/room/DeleteTable.class", "size": 1026, "crc": 1835139657}, {"key": "androidx/room/Embedded.class", "name": "androidx/room/Embedded.class", "size": 907, "crc": -332188250}, {"key": "androidx/room/Entity.class", "name": "androidx/room/Entity.class", "size": 1430, "crc": -620302573}, {"key": "androidx/room/ForeignKey$Action.class", "name": "androidx/room/ForeignKey$Action.class", "size": 664, "crc": 1204680729}, {"key": "androidx/room/ForeignKey$Companion.class", "name": "androidx/room/ForeignKey$Companion.class", "size": 971, "crc": 8536269}, {"key": "androidx/room/ForeignKey.class", "name": "androidx/room/ForeignKey.class", "size": 1854, "crc": -1589601398}, {"key": "androidx/room/Fts3.class", "name": "androidx/room/Fts3.class", "size": 1077, "crc": -217137227}, {"key": "androidx/room/Fts4.class", "name": "androidx/room/Fts4.class", "size": 1965, "crc": -1848565895}, {"key": "androidx/room/FtsOptions$MatchInfo.class", "name": "androidx/room/FtsOptions$MatchInfo.class", "size": 1425, "crc": 1484091788}, {"key": "androidx/room/FtsOptions$Order.class", "name": "androidx/room/FtsOptions$Order.class", "size": 1396, "crc": -1262178105}, {"key": "androidx/room/FtsOptions.class", "name": "androidx/room/FtsOptions.class", "size": 1234, "crc": -1000531992}, {"key": "androidx/room/Ignore.class", "name": "androidx/room/Ignore.class", "size": 835, "crc": 127609313}, {"key": "androidx/room/Index$Order.class", "name": "androidx/room/Index$Order.class", "size": 1356, "crc": -590365684}, {"key": "androidx/room/Index.class", "name": "androidx/room/Index.class", "size": 1161, "crc": 942671266}, {"key": "androidx/room/Insert.class", "name": "androidx/room/Insert.class", "size": 1148, "crc": 964404296}, {"key": "androidx/room/Junction.class", "name": "androidx/room/Junction.class", "size": 1003, "crc": 480236607}, {"key": "androidx/room/MapColumn.class", "name": "androidx/room/MapColumn.class", "size": 943, "crc": -1985690914}, {"key": "androidx/room/MapInfo.class", "name": "androidx/room/MapInfo.class", "size": 1157, "crc": -1472218474}, {"key": "androidx/room/OnConflictStrategy$Companion.class", "name": "androidx/room/OnConflictStrategy$Companion.class", "size": 1283, "crc": -1447776492}, {"key": "androidx/room/OnConflictStrategy.class", "name": "androidx/room/OnConflictStrategy.class", "size": 1142, "crc": 75718887}, {"key": "androidx/room/PrimaryKey.class", "name": "androidx/room/PrimaryKey.class", "size": 902, "crc": 1611634114}, {"key": "androidx/room/ProvidedAutoMigrationSpec.class", "name": "androidx/room/ProvidedAutoMigrationSpec.class", "size": 814, "crc": 1822649705}, {"key": "androidx/room/ProvidedTypeConverter.class", "name": "androidx/room/ProvidedTypeConverter.class", "size": 802, "crc": 703952666}, {"key": "androidx/room/Query.class", "name": "androidx/room/Query.class", "size": 863, "crc": 858196701}, {"key": "androidx/room/RawQuery.class", "name": "androidx/room/RawQuery.class", "size": 998, "crc": 2084734626}, {"key": "androidx/room/Relation.class", "name": "androidx/room/Relation.class", "size": 1395, "crc": -884991045}, {"key": "androidx/room/RenameColumn$Entries.class", "name": "androidx/room/RenameColumn$Entries.class", "size": 1006, "crc": -1724955537}, {"key": "androidx/room/RenameColumn.class", "name": "androidx/room/RenameColumn.class", "size": 1137, "crc": 43517509}, {"key": "androidx/room/RenameTable$Entries.class", "name": "androidx/room/RenameTable$Entries.class", "size": 1000, "crc": 1124075710}, {"key": "androidx/room/RenameTable.class", "name": "androidx/room/RenameTable.class", "size": 1081, "crc": -471405087}, {"key": "androidx/room/RewriteQueriesToDropUnusedColumns.class", "name": "androidx/room/RewriteQueriesToDropUnusedColumns.class", "size": 868, "crc": -929248590}, {"key": "androidx/room/RoomMasterTable.class", "name": "androidx/room/RoomMasterTable.class", "size": 2277, "crc": 1275784360}, {"key": "androidx/room/RoomWarnings$Companion.class", "name": "androidx/room/RoomWarnings$Companion.class", "size": 1803, "crc": 810108616}, {"key": "androidx/room/RoomWarnings.class", "name": "androidx/room/RoomWarnings.class", "size": 2837, "crc": 1188387891}, {"key": "androidx/room/SkipQueryVerification.class", "name": "androidx/room/SkipQueryVerification.class", "size": 832, "crc": -25533976}, {"key": "androidx/room/Transaction.class", "name": "androidx/room/Transaction.class", "size": 785, "crc": 672383764}, {"key": "androidx/room/TypeConverter.class", "name": "androidx/room/TypeConverter.class", "size": 791, "crc": -684706873}, {"key": "androidx/room/TypeConverters.class", "name": "androidx/room/TypeConverters.class", "size": 1253, "crc": 864845621}, {"key": "androidx/room/Update.class", "name": "androidx/room/Update.class", "size": 1148, "crc": 56545744}, {"key": "androidx/room/Upsert.class", "name": "androidx/room/Upsert.class", "size": 1070, "crc": -104356914}]