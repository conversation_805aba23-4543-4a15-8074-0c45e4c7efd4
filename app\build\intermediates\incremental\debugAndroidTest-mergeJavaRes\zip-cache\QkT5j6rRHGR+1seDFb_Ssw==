[{"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule$AndroidComposeStatement.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule$AndroidComposeStatement.class", "size": 1634, "crc": -1210883028}, {"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule$apply$1$evaluate$1.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule$apply$1$evaluate$1.class", "size": 1883, "crc": -868854465}, {"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule$apply$1.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule$apply$1.class", "size": 1926, "crc": -1068790215}, {"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule$special$$inlined$AndroidComposeUiTestEnvironment$1.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule$special$$inlined$AndroidComposeUiTestEnvironment$1.class", "size": 2919, "crc": 459657239}, {"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule.class", "size": 11746, "crc": 2083484884}, {"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt$createAndroidComposeRule$1.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt$createAndroidComposeRule$1.class", "size": 1961, "crc": -183307990}, {"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt$createAndroidComposeRule$2.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt$createAndroidComposeRule$2.class", "size": 1997, "crc": -1038133035}, {"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt$createEmptyComposeRule$2.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt$createEmptyComposeRule$2.class", "size": 1988, "crc": 1173978594}, {"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt$createEmptyComposeRule$4.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt$createEmptyComposeRule$4.class", "size": 2024, "crc": -1618975549}, {"key": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt.class", "name": "androidx/compose/ui/test/junit4/AndroidComposeTestRule_androidKt.class", "size": 9540, "crc": -994462155}, {"key": "androidx/compose/ui/test/junit4/ComposeContentTestRule$DefaultImpls.class", "name": "androidx/compose/ui/test/junit4/ComposeContentTestRule$DefaultImpls.class", "size": 1188, "crc": -333364059}, {"key": "androidx/compose/ui/test/junit4/ComposeContentTestRule.class", "name": "androidx/compose/ui/test/junit4/ComposeContentTestRule.class", "size": 1563, "crc": 1579607038}, {"key": "androidx/compose/ui/test/junit4/ComposeTestRule$DefaultImpls.class", "name": "androidx/compose/ui/test/junit4/ComposeTestRule$DefaultImpls.class", "size": 2136, "crc": 719165221}, {"key": "androidx/compose/ui/test/junit4/ComposeTestRule.class", "name": "androidx/compose/ui/test/junit4/ComposeTestRule.class", "size": 5432, "crc": -242801939}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester$InjectRestorationRegistry$1.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester$InjectRestorationRegistry$1.class", "size": 3613, "crc": -2034396891}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester$InjectRestorationRegistry$2.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester$InjectRestorationRegistry$2.class", "size": 2693, "crc": 21906150}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester$RestorationRegistry$emitChildrenWithRestoredState$1.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester$RestorationRegistry$emitChildrenWithRestoredState$1.class", "size": 2110, "crc": 730836496}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester$RestorationRegistry.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester$RestorationRegistry.class", "size": 6043, "crc": -403995526}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester$emulateSavedInstanceStateRestore$1.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester$emulateSavedInstanceStateRestore$1.class", "size": 1519, "crc": 280450760}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester$emulateSavedInstanceStateRestore$2.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester$emulateSavedInstanceStateRestore$2.class", "size": 1521, "crc": -794060693}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester$emulateSavedInstanceStateRestore$3.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester$emulateSavedInstanceStateRestore$3.class", "size": 1228, "crc": 1320117710}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester$setContent$1$1.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester$setContent$1$1.class", "size": 3661, "crc": 2088036985}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester$setContent$1.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester$setContent$1.class", "size": 3386, "crc": 590510139}, {"key": "androidx/compose/ui/test/junit4/StateRestorationTester.class", "name": "androidx/compose/ui/test/junit4/StateRestorationTester.class", "size": 9902, "crc": 792102577}, {"key": "META-INF/androidx.compose.ui_ui-test-junit4.version", "name": "META-INF/androidx.compose.ui_ui-test-junit4.version", "size": 6, "crc": 1621725393}, {"key": "META-INF/ui-test-junit4_release.kotlin_module", "name": "META-INF/ui-test-junit4_release.kotlin_module", "size": 93, "crc": -861818070}]