[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 260, "crc": -86050857}, {"key": "LICENSE.txt", "name": "LICENSE.txt", "size": 1509, "crc": -**********}, {"key": "org/hamcrest/Matchers.class", "name": "org/hamcrest/Matchers.class", "size": 25260, "crc": 702644432}, {"key": "org/hamcrest/beans/HasProperty.class", "name": "org/hamcrest/beans/HasProperty.class", "size": 2015, "crc": **********}, {"key": "org/hamcrest/beans/HasPropertyWithValue$1.class", "name": "org/hamcrest/beans/HasPropertyWithValue$1.class", "size": 2089, "crc": -487448515}, {"key": "org/hamcrest/beans/HasPropertyWithValue$2.class", "name": "org/hamcrest/beans/HasPropertyWithValue$2.class", "size": 1866, "crc": **********}, {"key": "org/hamcrest/beans/HasPropertyWithValue.class", "name": "org/hamcrest/beans/HasPropertyWithValue.class", "size": 4284, "crc": -505798184}, {"key": "org/hamcrest/beans/PropertyUtil.class", "name": "org/hamcrest/beans/PropertyUtil.class", "size": 2148, "crc": -139572112}, {"key": "org/hamcrest/beans/SamePropertyValuesAs$PropertyMatcher.class", "name": "org/hamcrest/beans/SamePropertyValuesAs$PropertyMatcher.class", "size": 2226, "crc": 789718960}, {"key": "org/hamcrest/beans/SamePropertyValuesAs.class", "name": "org/hamcrest/beans/SamePropertyValuesAs.class", "size": 5848, "crc": 2065882412}, {"key": "org/hamcrest/collection/IsArray.class", "name": "org/hamcrest/collection/IsArray.class", "size": 3264, "crc": -1275905226}, {"key": "org/hamcrest/collection/IsArrayContaining.class", "name": "org/hamcrest/collection/IsArrayContaining.class", "size": 2832, "crc": -1548884924}, {"key": "org/hamcrest/collection/IsArrayContainingInAnyOrder.class", "name": "org/hamcrest/collection/IsArrayContainingInAnyOrder.class", "size": 3710, "crc": -1917154602}, {"key": "org/hamcrest/collection/IsArrayContainingInOrder.class", "name": "org/hamcrest/collection/IsArrayContainingInOrder.class", "size": 3553, "crc": 1588167037}, {"key": "org/hamcrest/collection/IsArrayWithSize.class", "name": "org/hamcrest/collection/IsArrayWithSize.class", "size": 2359, "crc": 1708236374}, {"key": "org/hamcrest/collection/IsCollectionWithSize.class", "name": "org/hamcrest/collection/IsCollectionWithSize.class", "size": 2148, "crc": 131429067}, {"key": "org/hamcrest/collection/IsEmptyCollection.class", "name": "org/hamcrest/collection/IsEmptyCollection.class", "size": 2507, "crc": 756406124}, {"key": "org/hamcrest/collection/IsEmptyIterable.class", "name": "org/hamcrest/collection/IsEmptyIterable.class", "size": 2662, "crc": -1370873525}, {"key": "org/hamcrest/collection/IsIn.class", "name": "org/hamcrest/collection/IsIn.class", "size": 2237, "crc": 1589306736}, {"key": "org/hamcrest/collection/IsIterableContainingInAnyOrder$Matching.class", "name": "org/hamcrest/collection/IsIterableContainingInAnyOrder$Matching.class", "size": 2788, "crc": 574960358}, {"key": "org/hamcrest/collection/IsIterableContainingInAnyOrder.class", "name": "org/hamcrest/collection/IsIterableContainingInAnyOrder.class", "size": 4455, "crc": 996642489}, {"key": "org/hamcrest/collection/IsIterableContainingInOrder$MatchSeries.class", "name": "org/hamcrest/collection/IsIterableContainingInOrder$MatchSeries.class", "size": 3065, "crc": 1850805750}, {"key": "org/hamcrest/collection/IsIterableContainingInOrder.class", "name": "org/hamcrest/collection/IsIterableContainingInOrder.class", "size": 4274, "crc": -2079380110}, {"key": "org/hamcrest/collection/IsIterableWithSize.class", "name": "org/hamcrest/collection/IsIterableWithSize.class", "size": 2324, "crc": 758131930}, {"key": "org/hamcrest/collection/IsMapContaining.class", "name": "org/hamcrest/collection/IsMapContaining.class", "size": 4693, "crc": 1697732906}, {"key": "org/hamcrest/number/BigDecimalCloseTo.class", "name": "org/hamcrest/number/BigDecimalCloseTo.class", "size": 2486, "crc": -1192181333}, {"key": "org/hamcrest/number/IsCloseTo.class", "name": "org/hamcrest/number/IsCloseTo.class", "size": 2093, "crc": -2014739431}, {"key": "org/hamcrest/number/OrderingComparison.class", "name": "org/hamcrest/number/OrderingComparison.class", "size": 3453, "crc": -1369378801}, {"key": "org/hamcrest/object/HasToString.class", "name": "org/hamcrest/object/HasToString.class", "size": 1870, "crc": -1437172281}, {"key": "org/hamcrest/object/IsCompatibleType.class", "name": "org/hamcrest/object/IsCompatibleType.class", "size": 2298, "crc": 1225956985}, {"key": "org/hamcrest/object/IsEventFrom.class", "name": "org/hamcrest/object/IsEventFrom.class", "size": 2753, "crc": 1168893494}, {"key": "org/hamcrest/text/IsEmptyString.class", "name": "org/hamcrest/text/IsEmptyString.class", "size": 1580, "crc": 470327400}, {"key": "org/hamcrest/text/IsEqualIgnoringCase.class", "name": "org/hamcrest/text/IsEqualIgnoringCase.class", "size": 1970, "crc": 990086970}, {"key": "org/hamcrest/text/IsEqualIgnoringWhiteSpace.class", "name": "org/hamcrest/text/IsEqualIgnoringWhiteSpace.class", "size": 2644, "crc": -1122316088}, {"key": "org/hamcrest/text/StringContainsInOrder.class", "name": "org/hamcrest/text/StringContainsInOrder.class", "size": 2491, "crc": 1521340968}, {"key": "org/hamcrest/xml/HasXPath$1.class", "name": "org/hamcrest/xml/HasXPath$1.class", "size": 1272, "crc": 580136647}, {"key": "org/hamcrest/xml/HasXPath.class", "name": "org/hamcrest/xml/HasXPath.class", "size": 6395, "crc": -430746126}]