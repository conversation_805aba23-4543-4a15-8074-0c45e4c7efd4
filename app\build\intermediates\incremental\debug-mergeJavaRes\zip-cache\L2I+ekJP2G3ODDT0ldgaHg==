[{"key": "com/airbnb/lottie/AsyncUpdates.class", "name": "com/airbnb/lottie/AsyncUpdates.class", "size": 1120, "crc": 1087116191}, {"key": "com/airbnb/lottie/Cancellable.class", "name": "com/airbnb/lottie/Cancellable.class", "size": 228, "crc": 338069003}, {"key": "com/airbnb/lottie/FontAssetDelegate.class", "name": "com/airbnb/lottie/FontAssetDelegate.class", "size": 961, "crc": 1836675076}, {"key": "com/airbnb/lottie/ImageAssetDelegate.class", "name": "com/airbnb/lottie/ImageAssetDelegate.class", "size": 298, "crc": 1232967806}, {"key": "com/airbnb/lottie/L.class", "name": "com/airbnb/lottie/L.class", "size": 5058, "crc": -1622185261}, {"key": "com/airbnb/lottie/Lottie.class", "name": "com/airbnb/lottie/Lottie.class", "size": 1192, "crc": 890366673}, {"key": "com/airbnb/lottie/LottieAnimationView$1.class", "name": "com/airbnb/lottie/LottieAnimationView$1.class", "size": 1355, "crc": -1864125020}, {"key": "com/airbnb/lottie/LottieAnimationView$SavedState$1.class", "name": "com/airbnb/lottie/LottieAnimationView$SavedState$1.class", "size": 1413, "crc": -1584439065}, {"key": "com/airbnb/lottie/LottieAnimationView$SavedState.class", "name": "com/airbnb/lottie/LottieAnimationView$SavedState.class", "size": 2238, "crc": 363626390}, {"key": "com/airbnb/lottie/LottieAnimationView$UserActionTaken.class", "name": "com/airbnb/lottie/LottieAnimationView$UserActionTaken.class", "size": 1558, "crc": -1164115567}, {"key": "com/airbnb/lottie/LottieAnimationView$WeakFailureListener.class", "name": "com/airbnb/lottie/LottieAnimationView$WeakFailureListener.class", "size": 1785, "crc": -595667956}, {"key": "com/airbnb/lottie/LottieAnimationView$WeakSuccessListener.class", "name": "com/airbnb/lottie/LottieAnimationView$WeakSuccessListener.class", "size": 1392, "crc": 29764640}, {"key": "com/airbnb/lottie/LottieAnimationView.class", "name": "com/airbnb/lottie/LottieAnimationView.class", "size": 30349, "crc": -1210645966}, {"key": "com/airbnb/lottie/LottieComposition$1.class", "name": "com/airbnb/lottie/LottieComposition$1.class", "size": 235, "crc": 674368103}, {"key": "com/airbnb/lottie/LottieComposition$Factory$ListenerAdapter.class", "name": "com/airbnb/lottie/LottieComposition$Factory$ListenerAdapter.class", "size": 1711, "crc": 1836804063}, {"key": "com/airbnb/lottie/LottieComposition$Factory.class", "name": "com/airbnb/lottie/LottieComposition$Factory.class", "size": 5194, "crc": -1655276621}, {"key": "com/airbnb/lottie/LottieComposition.class", "name": "com/airbnb/lottie/LottieComposition.class", "size": 8290, "crc": 1734187210}, {"key": "com/airbnb/lottie/LottieCompositionFactory.class", "name": "com/airbnb/lottie/LottieCompositionFactory.class", "size": 30212, "crc": -1959091251}, {"key": "com/airbnb/lottie/LottieConfig$1.class", "name": "com/airbnb/lottie/LottieConfig$1.class", "size": 220, "crc": 874298704}, {"key": "com/airbnb/lottie/LottieConfig$Builder$1.class", "name": "com/airbnb/lottie/LottieConfig$Builder$1.class", "size": 1214, "crc": -1141651499}, {"key": "com/airbnb/lottie/LottieConfig$Builder$2.class", "name": "com/airbnb/lottie/LottieConfig$Builder$2.class", "size": 1409, "crc": -1375429463}, {"key": "com/airbnb/lottie/LottieConfig$Builder.class", "name": "com/airbnb/lottie/LottieConfig$Builder.class", "size": 2823, "crc": -897348140}, {"key": "com/airbnb/lottie/LottieConfig.class", "name": "com/airbnb/lottie/LottieConfig.class", "size": 1452, "crc": 1647571854}, {"key": "com/airbnb/lottie/LottieDrawable$1.class", "name": "com/airbnb/lottie/LottieDrawable$1.class", "size": 1325, "crc": 595618937}, {"key": "com/airbnb/lottie/LottieDrawable$LazyCompositionTask.class", "name": "com/airbnb/lottie/LottieDrawable$LazyCompositionTask.class", "size": 295, "crc": 1380603421}, {"key": "com/airbnb/lottie/LottieDrawable$OnVisibleAction.class", "name": "com/airbnb/lottie/LottieDrawable$OnVisibleAction.class", "size": 1307, "crc": 1720261541}, {"key": "com/airbnb/lottie/LottieDrawable$RepeatMode.class", "name": "com/airbnb/lottie/LottieDrawable$RepeatMode.class", "size": 399, "crc": -380590320}, {"key": "com/airbnb/lottie/LottieDrawable.class", "name": "com/airbnb/lottie/LottieDrawable.class", "size": 40222, "crc": -1098557161}, {"key": "com/airbnb/lottie/LottieImageAsset.class", "name": "com/airbnb/lottie/LottieImageAsset.class", "size": 1947, "crc": 16039501}, {"key": "com/airbnb/lottie/LottieListener.class", "name": "com/airbnb/lottie/LottieListener.class", "size": 250, "crc": -32597042}, {"key": "com/airbnb/lottie/LottieLogger.class", "name": "com/airbnb/lottie/LottieLogger.class", "size": 258, "crc": 723869315}, {"key": "com/airbnb/lottie/LottieOnCompositionLoadedListener.class", "name": "com/airbnb/lottie/LottieOnCompositionLoadedListener.class", "size": 238, "crc": -635439313}, {"key": "com/airbnb/lottie/LottieProperty.class", "name": "com/airbnb/lottie/LottieProperty.class", "size": 3087, "crc": 1367016866}, {"key": "com/airbnb/lottie/LottieResult.class", "name": "com/airbnb/lottie/LottieResult.class", "size": 1768, "crc": 1260211526}, {"key": "com/airbnb/lottie/LottieTask$LottieFutureTask.class", "name": "com/airbnb/lottie/LottieTask$LottieFutureTask.class", "size": 1587, "crc": -1102793919}, {"key": "com/airbnb/lottie/LottieTask.class", "name": "com/airbnb/lottie/LottieTask.class", "size": 6638, "crc": 700619195}, {"key": "com/airbnb/lottie/LottieTaskIdleListener.class", "name": "com/airbnb/lottie/LottieTaskIdleListener.class", "size": 174, "crc": 418144817}, {"key": "com/airbnb/lottie/OnCompositionLoadedListener.class", "name": "com/airbnb/lottie/OnCompositionLoadedListener.class", "size": 395, "crc": -97239190}, {"key": "com/airbnb/lottie/PerformanceTracker$1.class", "name": "com/airbnb/lottie/PerformanceTracker$1.class", "size": 1509, "crc": -653797739}, {"key": "com/airbnb/lottie/PerformanceTracker$FrameListener.class", "name": "com/airbnb/lottie/PerformanceTracker$FrameListener.class", "size": 271, "crc": -648209930}, {"key": "com/airbnb/lottie/PerformanceTracker.class", "name": "com/airbnb/lottie/PerformanceTracker.class", "size": 4485, "crc": 1401091323}, {"key": "com/airbnb/lottie/RenderMode$1.class", "name": "com/airbnb/lottie/RenderMode$1.class", "size": 716, "crc": 751141987}, {"key": "com/airbnb/lottie/RenderMode.class", "name": "com/airbnb/lottie/RenderMode.class", "size": 1642, "crc": -1764780611}, {"key": "com/airbnb/lottie/SimpleColorFilter.class", "name": "com/airbnb/lottie/SimpleColorFilter.class", "size": 658, "crc": -2031364926}, {"key": "com/airbnb/lottie/TextDelegate.class", "name": "com/airbnb/lottie/TextDelegate.class", "size": 2903, "crc": -1621831060}, {"key": "com/airbnb/lottie/animation/LPaint.class", "name": "com/airbnb/lottie/animation/LPaint.class", "size": 1697, "crc": -187041524}, {"key": "com/airbnb/lottie/animation/content/BaseStrokeContent$1.class", "name": "com/airbnb/lottie/animation/content/BaseStrokeContent$1.class", "size": 271, "crc": -1523211878}, {"key": "com/airbnb/lottie/animation/content/BaseStrokeContent$PathGroup.class", "name": "com/airbnb/lottie/animation/content/BaseStrokeContent$PathGroup.class", "size": 1648, "crc": 441678823}, {"key": "com/airbnb/lottie/animation/content/BaseStrokeContent.class", "name": "com/airbnb/lottie/animation/content/BaseStrokeContent.class", "size": 16189, "crc": 342116708}, {"key": "com/airbnb/lottie/animation/content/CompoundTrimPathContent.class", "name": "com/airbnb/lottie/animation/content/CompoundTrimPathContent.class", "size": 1343, "crc": 98210185}, {"key": "com/airbnb/lottie/animation/content/Content.class", "name": "com/airbnb/lottie/animation/content/Content.class", "size": 384, "crc": -1611880855}, {"key": "com/airbnb/lottie/animation/content/ContentGroup.class", "name": "com/airbnb/lottie/animation/content/ContentGroup.class", "size": 11651, "crc": 95477630}, {"key": "com/airbnb/lottie/animation/content/DrawingContent.class", "name": "com/airbnb/lottie/animation/content/DrawingContent.class", "size": 344, "crc": 1475140994}, {"key": "com/airbnb/lottie/animation/content/EllipseContent.class", "name": "com/airbnb/lottie/animation/content/EllipseContent.class", "size": 6637, "crc": 520112786}, {"key": "com/airbnb/lottie/animation/content/FillContent.class", "name": "com/airbnb/lottie/animation/content/FillContent.class", "size": 9849, "crc": 890849367}, {"key": "com/airbnb/lottie/animation/content/GradientFillContent.class", "name": "com/airbnb/lottie/animation/content/GradientFillContent.class", "size": 14562, "crc": -1049842394}, {"key": "com/airbnb/lottie/animation/content/GradientStrokeContent.class", "name": "com/airbnb/lottie/animation/content/GradientStrokeContent.class", "size": 9208, "crc": -560523304}, {"key": "com/airbnb/lottie/animation/content/GreedyContent.class", "name": "com/airbnb/lottie/animation/content/GreedyContent.class", "size": 294, "crc": -946631972}, {"key": "com/airbnb/lottie/animation/content/KeyPathElementContent.class", "name": "com/airbnb/lottie/animation/content/KeyPathElementContent.class", "size": 256, "crc": -1714281522}, {"key": "com/airbnb/lottie/animation/content/MergePathsContent$1.class", "name": "com/airbnb/lottie/animation/content/MergePathsContent$1.class", "size": 1099, "crc": 196351939}, {"key": "com/airbnb/lottie/animation/content/MergePathsContent.class", "name": "com/airbnb/lottie/animation/content/MergePathsContent.class", "size": 5098, "crc": -1666383236}, {"key": "com/airbnb/lottie/animation/content/ModifierContent.class", "name": "com/airbnb/lottie/animation/content/ModifierContent.class", "size": 147, "crc": -1418773659}, {"key": "com/airbnb/lottie/animation/content/PathContent.class", "name": "com/airbnb/lottie/animation/content/PathContent.class", "size": 236, "crc": 1148995438}, {"key": "com/airbnb/lottie/animation/content/PolystarContent$1.class", "name": "com/airbnb/lottie/animation/content/PolystarContent$1.class", "size": 900, "crc": 1418468610}, {"key": "com/airbnb/lottie/animation/content/PolystarContent.class", "name": "com/airbnb/lottie/animation/content/PolystarContent.class", "size": 11290, "crc": -1874668992}, {"key": "com/airbnb/lottie/animation/content/RectangleContent.class", "name": "com/airbnb/lottie/animation/content/RectangleContent.class", "size": 7817, "crc": 2113463972}, {"key": "com/airbnb/lottie/animation/content/RepeaterContent.class", "name": "com/airbnb/lottie/animation/content/RepeaterContent.class", "size": 8239, "crc": 1374891265}, {"key": "com/airbnb/lottie/animation/content/RoundedCornersContent.class", "name": "com/airbnb/lottie/animation/content/RoundedCornersContent.class", "size": 7480, "crc": 649475519}, {"key": "com/airbnb/lottie/animation/content/ShapeContent.class", "name": "com/airbnb/lottie/animation/content/ShapeContent.class", "size": 4860, "crc": 776139455}, {"key": "com/airbnb/lottie/animation/content/ShapeModifierContent.class", "name": "com/airbnb/lottie/animation/content/ShapeModifierContent.class", "size": 321, "crc": -1598183073}, {"key": "com/airbnb/lottie/animation/content/StrokeContent.class", "name": "com/airbnb/lottie/animation/content/StrokeContent.class", "size": 5130, "crc": 302810483}, {"key": "com/airbnb/lottie/animation/content/TrimPathContent.class", "name": "com/airbnb/lottie/animation/content/TrimPathContent.class", "size": 3808, "crc": 997011152}, {"key": "com/airbnb/lottie/animation/content/package-info.class", "name": "com/airbnb/lottie/animation/content/package-info.class", "size": 404, "crc": -61164179}, {"key": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$1.class", "name": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$1.class", "size": 285, "crc": -2073916488}, {"key": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$AnimationListener.class", "name": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$AnimationListener.class", "size": 324, "crc": -1119187347}, {"key": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$EmptyKeyframeWrapper.class", "name": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$EmptyKeyframeWrapper.class", "size": 2036, "crc": 500800922}, {"key": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$KeyframesWrapper.class", "name": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$KeyframesWrapper.class", "size": 763, "crc": -1830410904}, {"key": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$KeyframesWrapperImpl.class", "name": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$KeyframesWrapperImpl.class", "size": 3020, "crc": -429214660}, {"key": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$SingleKeyframeWrapper.class", "name": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation$SingleKeyframeWrapper.class", "size": 2357, "crc": -2081334407}, {"key": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/BaseKeyframeAnimation.class", "size": 7516, "crc": -2143457092}, {"key": "com/airbnb/lottie/animation/keyframe/ColorKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/ColorKeyframeAnimation.class", "size": 2768, "crc": 1458856403}, {"key": "com/airbnb/lottie/animation/keyframe/DropShadowKeyframeAnimation$1.class", "name": "com/airbnb/lottie/animation/keyframe/DropShadowKeyframeAnimation$1.class", "size": 1850, "crc": 2007040354}, {"key": "com/airbnb/lottie/animation/keyframe/DropShadowKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/DropShadowKeyframeAnimation.class", "size": 4994, "crc": 84376284}, {"key": "com/airbnb/lottie/animation/keyframe/FloatKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/FloatKeyframeAnimation.class", "size": 2648, "crc": 726924747}, {"key": "com/airbnb/lottie/animation/keyframe/GradientColorKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/GradientColorKeyframeAnimation.class", "size": 2128, "crc": -1157341408}, {"key": "com/airbnb/lottie/animation/keyframe/IntegerKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/IntegerKeyframeAnimation.class", "size": 2738, "crc": -1479115151}, {"key": "com/airbnb/lottie/animation/keyframe/KeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/KeyframeAnimation.class", "size": 817, "crc": 1813138712}, {"key": "com/airbnb/lottie/animation/keyframe/MaskKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/MaskKeyframeAnimation.class", "size": 2568, "crc": 964419171}, {"key": "com/airbnb/lottie/animation/keyframe/PathKeyframe.class", "name": "com/airbnb/lottie/animation/keyframe/PathKeyframe.class", "size": 2164, "crc": -858845787}, {"key": "com/airbnb/lottie/animation/keyframe/PathKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/PathKeyframeAnimation.class", "size": 2796, "crc": -914572184}, {"key": "com/airbnb/lottie/animation/keyframe/PointKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/PointKeyframeAnimation.class", "size": 2876, "crc": -1362421744}, {"key": "com/airbnb/lottie/animation/keyframe/ScaleKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/ScaleKeyframeAnimation.class", "size": 2543, "crc": 1961391024}, {"key": "com/airbnb/lottie/animation/keyframe/ShapeKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/ShapeKeyframeAnimation.class", "size": 3028, "crc": -1663773894}, {"key": "com/airbnb/lottie/animation/keyframe/SplitDimensionPathKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/SplitDimensionPathKeyframeAnimation.class", "size": 5000, "crc": 595489942}, {"key": "com/airbnb/lottie/animation/keyframe/TextKeyframeAnimation$1.class", "name": "com/airbnb/lottie/animation/keyframe/TextKeyframeAnimation$1.class", "size": 3194, "crc": 1074422138}, {"key": "com/airbnb/lottie/animation/keyframe/TextKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/TextKeyframeAnimation.class", "size": 3172, "crc": -1364983644}, {"key": "com/airbnb/lottie/animation/keyframe/TransformKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/TransformKeyframeAnimation.class", "size": 10392, "crc": -899387409}, {"key": "com/airbnb/lottie/animation/keyframe/ValueCallbackKeyframeAnimation.class", "name": "com/airbnb/lottie/animation/keyframe/ValueCallbackKeyframeAnimation.class", "size": 2575, "crc": -2087397337}, {"key": "com/airbnb/lottie/animation/keyframe/package-info.class", "name": "com/airbnb/lottie/animation/keyframe/package-info.class", "size": 405, "crc": -1384942688}, {"key": "com/airbnb/lottie/animation/package-info.class", "name": "com/airbnb/lottie/animation/package-info.class", "size": 396, "crc": 565467385}, {"key": "com/airbnb/lottie/manager/FontAssetManager.class", "name": "com/airbnb/lottie/manager/FontAssetManager.class", "size": 4733, "crc": -1493789782}, {"key": "com/airbnb/lottie/manager/ImageAssetManager.class", "name": "com/airbnb/lottie/manager/ImageAssetManager.class", "size": 6355, "crc": 521868110}, {"key": "com/airbnb/lottie/manager/package-info.class", "name": "com/airbnb/lottie/manager/package-info.class", "size": 394, "crc": 1553431026}, {"key": "com/airbnb/lottie/model/CubicCurveData.class", "name": "com/airbnb/lottie/model/CubicCurveData.class", "size": 2362, "crc": -316274451}, {"key": "com/airbnb/lottie/model/DocumentData$Justification.class", "name": "com/airbnb/lottie/model/DocumentData$Justification.class", "size": 1332, "crc": 1218598135}, {"key": "com/airbnb/lottie/model/DocumentData.class", "name": "com/airbnb/lottie/model/DocumentData.class", "size": 2452, "crc": -806989447}, {"key": "com/airbnb/lottie/model/Font.class", "name": "com/airbnb/lottie/model/Font.class", "size": 1536, "crc": 2125122521}, {"key": "com/airbnb/lottie/model/FontCharacter.class", "name": "com/airbnb/lottie/model/FontCharacter.class", "size": 1779, "crc": 1933991585}, {"key": "com/airbnb/lottie/model/KeyPath.class", "name": "com/airbnb/lottie/model/KeyPath.class", "size": 4697, "crc": 1298872150}, {"key": "com/airbnb/lottie/model/KeyPathElement.class", "name": "com/airbnb/lottie/model/KeyPathElement.class", "size": 926, "crc": -1937291125}, {"key": "com/airbnb/lottie/model/LottieCompositionCache.class", "name": "com/airbnb/lottie/model/LottieCompositionCache.class", "size": 2028, "crc": 1056398390}, {"key": "com/airbnb/lottie/model/Marker.class", "name": "com/airbnb/lottie/model/Marker.class", "size": 1148, "crc": 283394382}, {"key": "com/airbnb/lottie/model/MutablePair.class", "name": "com/airbnb/lottie/model/MutablePair.class", "size": 2103, "crc": 1599142050}, {"key": "com/airbnb/lottie/model/animatable/AnimatableColorValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatableColorValue.class", "size": 1424, "crc": -1575859612}, {"key": "com/airbnb/lottie/model/animatable/AnimatableFloatValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatableFloatValue.class", "size": 1412, "crc": 534587656}, {"key": "com/airbnb/lottie/model/animatable/AnimatableGradientColorValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatableGradientColorValue.class", "size": 3898, "crc": -1416222929}, {"key": "com/airbnb/lottie/model/animatable/AnimatableIntegerValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatableIntegerValue.class", "size": 1432, "crc": 1920424677}, {"key": "com/airbnb/lottie/model/animatable/AnimatablePathValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatablePathValue.class", "size": 1801, "crc": 766252464}, {"key": "com/airbnb/lottie/model/animatable/AnimatablePointValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatablePointValue.class", "size": 1460, "crc": -1765655242}, {"key": "com/airbnb/lottie/model/animatable/AnimatableScaleValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatableScaleValue.class", "size": 1701, "crc": 1402636251}, {"key": "com/airbnb/lottie/model/animatable/AnimatableShapeValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatableShapeValue.class", "size": 1524, "crc": -2128568580}, {"key": "com/airbnb/lottie/model/animatable/AnimatableSplitDimensionPathValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatableSplitDimensionPathValue.class", "size": 1957, "crc": 755258680}, {"key": "com/airbnb/lottie/model/animatable/AnimatableTextFrame.class", "name": "com/airbnb/lottie/model/animatable/AnimatableTextFrame.class", "size": 1519, "crc": 1459014004}, {"key": "com/airbnb/lottie/model/animatable/AnimatableTextProperties.class", "name": "com/airbnb/lottie/model/animatable/AnimatableTextProperties.class", "size": 1097, "crc": 135091986}, {"key": "com/airbnb/lottie/model/animatable/AnimatableTransform.class", "name": "com/airbnb/lottie/model/animatable/AnimatableTransform.class", "size": 4748, "crc": 1801734135}, {"key": "com/airbnb/lottie/model/animatable/AnimatableValue.class", "name": "com/airbnb/lottie/model/animatable/AnimatableValue.class", "size": 540, "crc": -1416313463}, {"key": "com/airbnb/lottie/model/animatable/BaseAnimatableValue.class", "name": "com/airbnb/lottie/model/animatable/BaseAnimatableValue.class", "size": 2179, "crc": -336431501}, {"key": "com/airbnb/lottie/model/animatable/package-info.class", "name": "com/airbnb/lottie/model/animatable/package-info.class", "size": 403, "crc": -2062879293}, {"key": "com/airbnb/lottie/model/content/BlurEffect.class", "name": "com/airbnb/lottie/model/content/BlurEffect.class", "size": 635, "crc": 358793970}, {"key": "com/airbnb/lottie/model/content/CircleShape.class", "name": "com/airbnb/lottie/model/content/CircleShape.class", "size": 2484, "crc": -651159559}, {"key": "com/airbnb/lottie/model/content/ContentModel.class", "name": "com/airbnb/lottie/model/content/ContentModel.class", "size": 394, "crc": 1851564013}, {"key": "com/airbnb/lottie/model/content/GradientColor.class", "name": "com/airbnb/lottie/model/content/GradientColor.class", "size": 2355, "crc": -1816502310}, {"key": "com/airbnb/lottie/model/content/GradientFill.class", "name": "com/airbnb/lottie/model/content/GradientFill.class", "size": 3468, "crc": -705891600}, {"key": "com/airbnb/lottie/model/content/GradientStroke.class", "name": "com/airbnb/lottie/model/content/GradientStroke.class", "size": 5411, "crc": -1061312597}, {"key": "com/airbnb/lottie/model/content/GradientType.class", "name": "com/airbnb/lottie/model/content/GradientType.class", "size": 1145, "crc": 641176133}, {"key": "com/airbnb/lottie/model/content/Mask$MaskMode.class", "name": "com/airbnb/lottie/model/content/Mask$MaskMode.class", "size": 1373, "crc": -1737138818}, {"key": "com/airbnb/lottie/model/content/Mask.class", "name": "com/airbnb/lottie/model/content/Mask.class", "size": 1418, "crc": 2129690726}, {"key": "com/airbnb/lottie/model/content/MergePaths$MergePathsMode.class", "name": "com/airbnb/lottie/model/content/MergePaths$MergePathsMode.class", "size": 1744, "crc": -427763308}, {"key": "com/airbnb/lottie/model/content/MergePaths.class", "name": "com/airbnb/lottie/model/content/MergePaths.class", "size": 2316, "crc": -694149504}, {"key": "com/airbnb/lottie/model/content/PolystarShape$Type.class", "name": "com/airbnb/lottie/model/content/PolystarShape$Type.class", "size": 1606, "crc": -1483246308}, {"key": "com/airbnb/lottie/model/content/PolystarShape.class", "name": "com/airbnb/lottie/model/content/PolystarShape.class", "size": 4157, "crc": -1928944042}, {"key": "com/airbnb/lottie/model/content/RectangleShape.class", "name": "com/airbnb/lottie/model/content/RectangleShape.class", "size": 3058, "crc": 122232567}, {"key": "com/airbnb/lottie/model/content/Repeater.class", "name": "com/airbnb/lottie/model/content/Repeater.class", "size": 2148, "crc": -1759313553}, {"key": "com/airbnb/lottie/model/content/RoundedCorners.class", "name": "com/airbnb/lottie/model/content/RoundedCorners.class", "size": 1942, "crc": -287304009}, {"key": "com/airbnb/lottie/model/content/ShapeData.class", "name": "com/airbnb/lottie/model/content/ShapeData.class", "size": 4134, "crc": 329096970}, {"key": "com/airbnb/lottie/model/content/ShapeFill.class", "name": "com/airbnb/lottie/model/content/ShapeFill.class", "size": 2786, "crc": 1360426436}, {"key": "com/airbnb/lottie/model/content/ShapeGroup.class", "name": "com/airbnb/lottie/model/content/ShapeGroup.class", "size": 2260, "crc": -165485831}, {"key": "com/airbnb/lottie/model/content/ShapePath.class", "name": "com/airbnb/lottie/model/content/ShapePath.class", "size": 1987, "crc": 1745507512}, {"key": "com/airbnb/lottie/model/content/ShapeStroke$1.class", "name": "com/airbnb/lottie/model/content/ShapeStroke$1.class", "size": 1358, "crc": -2104576032}, {"key": "com/airbnb/lottie/model/content/ShapeStroke$LineCapType.class", "name": "com/airbnb/lottie/model/content/ShapeStroke$LineCapType.class", "size": 1857, "crc": -105841173}, {"key": "com/airbnb/lottie/model/content/ShapeStroke$LineJoinType.class", "name": "com/airbnb/lottie/model/content/ShapeStroke$LineJoinType.class", "size": 1867, "crc": -1179354622}, {"key": "com/airbnb/lottie/model/content/ShapeStroke.class", "name": "com/airbnb/lottie/model/content/ShapeStroke.class", "size": 4405, "crc": -2001815552}, {"key": "com/airbnb/lottie/model/content/ShapeTrimPath$Type.class", "name": "com/airbnb/lottie/model/content/ShapeTrimPath$Type.class", "size": 1812, "crc": -1595981924}, {"key": "com/airbnb/lottie/model/content/ShapeTrimPath.class", "name": "com/airbnb/lottie/model/content/ShapeTrimPath.class", "size": 2648, "crc": 1152792167}, {"key": "com/airbnb/lottie/model/content/package-info.class", "name": "com/airbnb/lottie/model/content/package-info.class", "size": 400, "crc": -442062059}, {"key": "com/airbnb/lottie/model/layer/BaseLayer$1.class", "name": "com/airbnb/lottie/model/layer/BaseLayer$1.class", "size": 1627, "crc": 1791950136}, {"key": "com/airbnb/lottie/model/layer/BaseLayer.class", "name": "com/airbnb/lottie/model/layer/BaseLayer.class", "size": 23982, "crc": -302915341}, {"key": "com/airbnb/lottie/model/layer/CompositionLayer$1.class", "name": "com/airbnb/lottie/model/layer/CompositionLayer$1.class", "size": 864, "crc": -2115414879}, {"key": "com/airbnb/lottie/model/layer/CompositionLayer.class", "name": "com/airbnb/lottie/model/layer/CompositionLayer.class", "size": 10198, "crc": -359286456}, {"key": "com/airbnb/lottie/model/layer/ImageLayer.class", "name": "com/airbnb/lottie/model/layer/ImageLayer.class", "size": 4960, "crc": -660888692}, {"key": "com/airbnb/lottie/model/layer/Layer$LayerType.class", "name": "com/airbnb/lottie/model/layer/Layer$LayerType.class", "size": 1492, "crc": 1359372564}, {"key": "com/airbnb/lottie/model/layer/Layer$MatteType.class", "name": "com/airbnb/lottie/model/layer/Layer$MatteType.class", "size": 1442, "crc": 570840570}, {"key": "com/airbnb/lottie/model/layer/Layer.class", "name": "com/airbnb/lottie/model/layer/Layer.class", "size": 8405, "crc": 1823816179}, {"key": "com/airbnb/lottie/model/layer/NullLayer.class", "name": "com/airbnb/lottie/model/layer/NullLayer.class", "size": 1093, "crc": 977462203}, {"key": "com/airbnb/lottie/model/layer/ShapeLayer.class", "name": "com/airbnb/lottie/model/layer/ShapeLayer.class", "size": 3643, "crc": -1918595299}, {"key": "com/airbnb/lottie/model/layer/SolidLayer.class", "name": "com/airbnb/lottie/model/layer/SolidLayer.class", "size": 4525, "crc": -2081840047}, {"key": "com/airbnb/lottie/model/layer/TextLayer$1.class", "name": "com/airbnb/lottie/model/layer/TextLayer$1.class", "size": 755, "crc": -1197276712}, {"key": "com/airbnb/lottie/model/layer/TextLayer$2.class", "name": "com/airbnb/lottie/model/layer/TextLayer$2.class", "size": 757, "crc": 499492560}, {"key": "com/airbnb/lottie/model/layer/TextLayer$3.class", "name": "com/airbnb/lottie/model/layer/TextLayer$3.class", "size": 927, "crc": -282608703}, {"key": "com/airbnb/lottie/model/layer/TextLayer$TextSubLine.class", "name": "com/airbnb/lottie/model/layer/TextLayer$TextSubLine.class", "size": 1167, "crc": -822181817}, {"key": "com/airbnb/lottie/model/layer/TextLayer.class", "name": "com/airbnb/lottie/model/layer/TextLayer.class", "size": 21696, "crc": -929913568}, {"key": "com/airbnb/lottie/model/layer/package-info.class", "name": "com/airbnb/lottie/model/layer/package-info.class", "size": 398, "crc": 6922322}, {"key": "com/airbnb/lottie/network/DefaultLottieFetchResult.class", "name": "com/airbnb/lottie/network/DefaultLottieFetchResult.class", "size": 3125, "crc": -474293493}, {"key": "com/airbnb/lottie/network/DefaultLottieNetworkFetcher.class", "name": "com/airbnb/lottie/network/DefaultLottieNetworkFetcher.class", "size": 1390, "crc": -960433911}, {"key": "com/airbnb/lottie/network/FileExtension.class", "name": "com/airbnb/lottie/network/FileExtension.class", "size": 1841, "crc": -581011405}, {"key": "com/airbnb/lottie/network/LottieFetchResult.class", "name": "com/airbnb/lottie/network/LottieFetchResult.class", "size": 486, "crc": 31790636}, {"key": "com/airbnb/lottie/network/LottieNetworkCacheProvider.class", "name": "com/airbnb/lottie/network/LottieNetworkCacheProvider.class", "size": 274, "crc": 154611356}, {"key": "com/airbnb/lottie/network/LottieNetworkFetcher.class", "name": "com/airbnb/lottie/network/LottieNetworkFetcher.class", "size": 450, "crc": -**********}, {"key": "com/airbnb/lottie/network/NetworkCache.class", "name": "com/airbnb/lottie/network/NetworkCache.class", "size": 6499, "crc": **********}, {"key": "com/airbnb/lottie/network/NetworkFetcher.class", "name": "com/airbnb/lottie/network/NetworkFetcher.class", "size": 8068, "crc": -**********}, {"key": "com/airbnb/lottie/parser/AnimatablePathValueParser.class", "name": "com/airbnb/lottie/parser/AnimatablePathValueParser.class", "size": 4282, "crc": -**********}, {"key": "com/airbnb/lottie/parser/AnimatableTextPropertiesParser.class", "name": "com/airbnb/lottie/parser/AnimatableTextPropertiesParser.class", "size": 3125, "crc": -442110340}, {"key": "com/airbnb/lottie/parser/AnimatableTransformParser.class", "name": "com/airbnb/lottie/parser/AnimatableTransformParser.class", "size": 7206, "crc": -768769800}, {"key": "com/airbnb/lottie/parser/AnimatableValueParser.class", "name": "com/airbnb/lottie/parser/AnimatableValueParser.class", "size": 5803, "crc": 266707230}, {"key": "com/airbnb/lottie/parser/BlurEffectParser.class", "name": "com/airbnb/lottie/parser/BlurEffectParser.class", "size": 2468, "crc": -304234844}, {"key": "com/airbnb/lottie/parser/CircleShapeParser.class", "name": "com/airbnb/lottie/parser/CircleShapeParser.class", "size": 2825, "crc": 172864239}, {"key": "com/airbnb/lottie/parser/ColorParser.class", "name": "com/airbnb/lottie/parser/ColorParser.class", "size": 1829, "crc": 1932593032}, {"key": "com/airbnb/lottie/parser/ContentModelParser.class", "name": "com/airbnb/lottie/parser/ContentModelParser.class", "size": 5854, "crc": 1183493639}, {"key": "com/airbnb/lottie/parser/DocumentDataParser.class", "name": "com/airbnb/lottie/parser/DocumentDataParser.class", "size": 3567, "crc": 942883547}, {"key": "com/airbnb/lottie/parser/DropShadowEffect.class", "name": "com/airbnb/lottie/parser/DropShadowEffect.class", "size": 1469, "crc": 1730714323}, {"key": "com/airbnb/lottie/parser/DropShadowEffectParser.class", "name": "com/airbnb/lottie/parser/DropShadowEffectParser.class", "size": 3775, "crc": 633720541}, {"key": "com/airbnb/lottie/parser/FloatParser.class", "name": "com/airbnb/lottie/parser/FloatParser.class", "size": 1154, "crc": -1364923234}, {"key": "com/airbnb/lottie/parser/FontCharacterParser.class", "name": "com/airbnb/lottie/parser/FontCharacterParser.class", "size": 2761, "crc": 1060143937}, {"key": "com/airbnb/lottie/parser/FontParser.class", "name": "com/airbnb/lottie/parser/FontParser.class", "size": 1721, "crc": 894176795}, {"key": "com/airbnb/lottie/parser/GradientColorParser.class", "name": "com/airbnb/lottie/parser/GradientColorParser.class", "size": 6499, "crc": -1077098159}, {"key": "com/airbnb/lottie/parser/GradientFillParser.class", "name": "com/airbnb/lottie/parser/GradientFillParser.class", "size": 4413, "crc": -701316569}, {"key": "com/airbnb/lottie/parser/GradientStrokeParser.class", "name": "com/airbnb/lottie/parser/GradientStrokeParser.class", "size": 6263, "crc": 138751444}, {"key": "com/airbnb/lottie/parser/IntegerParser.class", "name": "com/airbnb/lottie/parser/IntegerParser.class", "size": 1216, "crc": 1685648647}, {"key": "com/airbnb/lottie/parser/JsonUtils$1.class", "name": "com/airbnb/lottie/parser/JsonUtils$1.class", "size": 904, "crc": -1007108605}, {"key": "com/airbnb/lottie/parser/JsonUtils.class", "name": "com/airbnb/lottie/parser/JsonUtils.class", "size": 4222, "crc": 34368470}, {"key": "com/airbnb/lottie/parser/KeyframeParser.class", "name": "com/airbnb/lottie/parser/KeyframeParser.class", "size": 10116, "crc": 917755608}, {"key": "com/airbnb/lottie/parser/KeyframesParser.class", "name": "com/airbnb/lottie/parser/KeyframesParser.class", "size": 3991, "crc": 1277647602}, {"key": "com/airbnb/lottie/parser/LayerParser$1.class", "name": "com/airbnb/lottie/parser/LayerParser$1.class", "size": 847, "crc": 731745619}, {"key": "com/airbnb/lottie/parser/LayerParser.class", "name": "com/airbnb/lottie/parser/LayerParser.class", "size": 10335, "crc": -13434086}, {"key": "com/airbnb/lottie/parser/LottieCompositionMoshiParser.class", "name": "com/airbnb/lottie/parser/LottieCompositionMoshiParser.class", "size": 9850, "crc": -771042276}, {"key": "com/airbnb/lottie/parser/MaskParser.class", "name": "com/airbnb/lottie/parser/MaskParser.class", "size": 3351, "crc": 714023398}, {"key": "com/airbnb/lottie/parser/MergePathsParser.class", "name": "com/airbnb/lottie/parser/MergePathsParser.class", "size": 1906, "crc": -31414281}, {"key": "com/airbnb/lottie/parser/PathKeyframeParser.class", "name": "com/airbnb/lottie/parser/PathKeyframeParser.class", "size": 1780, "crc": -2135920226}, {"key": "com/airbnb/lottie/parser/PathParser.class", "name": "com/airbnb/lottie/parser/PathParser.class", "size": 1046, "crc": -1813514550}, {"key": "com/airbnb/lottie/parser/PointFParser.class", "name": "com/airbnb/lottie/parser/PointFParser.class", "size": 2077, "crc": 173863560}, {"key": "com/airbnb/lottie/parser/PolystarShapeParser.class", "name": "com/airbnb/lottie/parser/PolystarShapeParser.class", "size": 4028, "crc": 1697461908}, {"key": "com/airbnb/lottie/parser/RectangleShapeParser.class", "name": "com/airbnb/lottie/parser/RectangleShapeParser.class", "size": 3026, "crc": -369654834}, {"key": "com/airbnb/lottie/parser/RepeaterParser.class", "name": "com/airbnb/lottie/parser/RepeaterParser.class", "size": 2673, "crc": 1305950492}, {"key": "com/airbnb/lottie/parser/RoundedCornersParser.class", "name": "com/airbnb/lottie/parser/RoundedCornersParser.class", "size": 2289, "crc": 2481567}, {"key": "com/airbnb/lottie/parser/ScaleXYParser.class", "name": "com/airbnb/lottie/parser/ScaleXYParser.class", "size": 1678, "crc": 1892950314}, {"key": "com/airbnb/lottie/parser/ShapeDataParser.class", "name": "com/airbnb/lottie/parser/ShapeDataParser.class", "size": 4155, "crc": -1146236320}, {"key": "com/airbnb/lottie/parser/ShapeFillParser.class", "name": "com/airbnb/lottie/parser/ShapeFillParser.class", "size": 3257, "crc": -1713602466}, {"key": "com/airbnb/lottie/parser/ShapeGroupParser.class", "name": "com/airbnb/lottie/parser/ShapeGroupParser.class", "size": 2263, "crc": 687307282}, {"key": "com/airbnb/lottie/parser/ShapePathParser.class", "name": "com/airbnb/lottie/parser/ShapePathParser.class", "size": 2194, "crc": -1753370981}, {"key": "com/airbnb/lottie/parser/ShapeStrokeParser.class", "name": "com/airbnb/lottie/parser/ShapeStrokeParser.class", "size": 5358, "crc": 466657254}, {"key": "com/airbnb/lottie/parser/ShapeTrimPathParser.class", "name": "com/airbnb/lottie/parser/ShapeTrimPathParser.class", "size": 2714, "crc": 1253144640}, {"key": "com/airbnb/lottie/parser/ValueParser.class", "name": "com/airbnb/lottie/parser/ValueParser.class", "size": 382, "crc": 253520152}, {"key": "com/airbnb/lottie/parser/moshi/JsonDataException.class", "name": "com/airbnb/lottie/parser/moshi/JsonDataException.class", "size": 497, "crc": 652449975}, {"key": "com/airbnb/lottie/parser/moshi/JsonEncodingException.class", "name": "com/airbnb/lottie/parser/moshi/JsonEncodingException.class", "size": 502, "crc": 1218502885}, {"key": "com/airbnb/lottie/parser/moshi/JsonReader$Options.class", "name": "com/airbnb/lottie/parser/moshi/JsonReader$Options.class", "size": 1440, "crc": -1252079404}, {"key": "com/airbnb/lottie/parser/moshi/JsonReader$Token.class", "name": "com/airbnb/lottie/parser/moshi/JsonReader$Token.class", "size": 1701, "crc": 1202146837}, {"key": "com/airbnb/lottie/parser/moshi/JsonReader.class", "name": "com/airbnb/lottie/parser/moshi/JsonReader.class", "size": 4191, "crc": 517824095}, {"key": "com/airbnb/lottie/parser/moshi/JsonScope.class", "name": "com/airbnb/lottie/parser/moshi/JsonScope.class", "size": 1344, "crc": -907059849}, {"key": "com/airbnb/lottie/parser/moshi/JsonUtf8Reader.class", "name": "com/airbnb/lottie/parser/moshi/JsonUtf8Reader.class", "size": 17892, "crc": -685523795}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$1.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$1.class", "size": 940, "crc": 497971735}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$AvlBuilder.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$AvlBuilder.class", "size": 2507, "crc": 1564775902}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$AvlIterator.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$AvlIterator.class", "size": 1739, "crc": 1156627005}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$EntrySet$1.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$EntrySet$1.class", "size": 1635, "crc": -1588292587}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$EntrySet.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$EntrySet.class", "size": 2189, "crc": 1082812071}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$KeySet$1.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$KeySet$1.class", "size": 1436, "crc": 537749518}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$KeySet.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$KeySet.class", "size": 1782, "crc": 909422679}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$LinkedTreeMapIterator.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$LinkedTreeMapIterator.class", "size": 2151, "crc": 425563235}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$Node.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap$Node.class", "size": 3529, "crc": -545372573}, {"key": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap.class", "name": "com/airbnb/lottie/parser/moshi/LinkedHashTreeMap.class", "size": 14202, "crc": 73145417}, {"key": "com/airbnb/lottie/parser/moshi/package-info.class", "name": "com/airbnb/lottie/parser/moshi/package-info.class", "size": 399, "crc": -238145462}, {"key": "com/airbnb/lottie/parser/package-info.class", "name": "com/airbnb/lottie/parser/package-info.class", "size": 393, "crc": -1197554235}, {"key": "com/airbnb/lottie/utils/BaseLottieAnimator.class", "name": "com/airbnb/lottie/utils/BaseLottieAnimator.class", "size": 5104, "crc": 147322766}, {"key": "com/airbnb/lottie/utils/GammaEvaluator.class", "name": "com/airbnb/lottie/utils/GammaEvaluator.class", "size": 1447, "crc": -1215016424}, {"key": "com/airbnb/lottie/utils/LogcatLogger.class", "name": "com/airbnb/lottie/utils/LogcatLogger.class", "size": 1497, "crc": -1826618004}, {"key": "com/airbnb/lottie/utils/Logger.class", "name": "com/airbnb/lottie/utils/Logger.class", "size": 1221, "crc": 1591170956}, {"key": "com/airbnb/lottie/utils/LottieThreadFactory.class", "name": "com/airbnb/lottie/utils/LottieThreadFactory.class", "size": 1805, "crc": 118261499}, {"key": "com/airbnb/lottie/utils/LottieTrace.class", "name": "com/airbnb/lottie/utils/LottieTrace.class", "size": 1550, "crc": -1471973197}, {"key": "com/airbnb/lottie/utils/LottieValueAnimator.class", "name": "com/airbnb/lottie/utils/LottieValueAnimator.class", "size": 7847, "crc": -336930846}, {"key": "com/airbnb/lottie/utils/MeanCalculator.class", "name": "com/airbnb/lottie/utils/MeanCalculator.class", "size": 689, "crc": 750006138}, {"key": "com/airbnb/lottie/utils/MiscUtils.class", "name": "com/airbnb/lottie/utils/MiscUtils.class", "size": 4662, "crc": 613117138}, {"key": "com/airbnb/lottie/utils/Utils$1.class", "name": "com/airbnb/lottie/utils/Utils$1.class", "size": 716, "crc": -1671367022}, {"key": "com/airbnb/lottie/utils/Utils$2.class", "name": "com/airbnb/lottie/utils/Utils$2.class", "size": 695, "crc": -462935407}, {"key": "com/airbnb/lottie/utils/Utils$3.class", "name": "com/airbnb/lottie/utils/Utils$3.class", "size": 695, "crc": 292491642}, {"key": "com/airbnb/lottie/utils/Utils$4.class", "name": "com/airbnb/lottie/utils/Utils$4.class", "size": 617, "crc": -1979763406}, {"key": "com/airbnb/lottie/utils/Utils.class", "name": "com/airbnb/lottie/utils/Utils.class", "size": 9164, "crc": -402901932}, {"key": "com/airbnb/lottie/utils/package-info.class", "name": "com/airbnb/lottie/utils/package-info.class", "size": 392, "crc": -1303850518}, {"key": "com/airbnb/lottie/value/Keyframe.class", "name": "com/airbnb/lottie/value/Keyframe.class", "size": 6508, "crc": 1082257642}, {"key": "com/airbnb/lottie/value/LottieFrameInfo.class", "name": "com/airbnb/lottie/value/LottieFrameInfo.class", "size": 2176, "crc": -135381892}, {"key": "com/airbnb/lottie/value/LottieInterpolatedFloatValue.class", "name": "com/airbnb/lottie/value/LottieInterpolatedFloatValue.class", "size": 1582, "crc": 664564274}, {"key": "com/airbnb/lottie/value/LottieInterpolatedIntegerValue.class", "name": "com/airbnb/lottie/value/LottieInterpolatedIntegerValue.class", "size": 1608, "crc": 1077030583}, {"key": "com/airbnb/lottie/value/LottieInterpolatedPointValue.class", "name": "com/airbnb/lottie/value/LottieInterpolatedPointValue.class", "size": 1750, "crc": 333042854}, {"key": "com/airbnb/lottie/value/LottieInterpolatedValue.class", "name": "com/airbnb/lottie/value/LottieInterpolatedValue.class", "size": 1885, "crc": 320408295}, {"key": "com/airbnb/lottie/value/LottieRelativeFloatValueCallback.class", "name": "com/airbnb/lottie/value/LottieRelativeFloatValueCallback.class", "size": 2083, "crc": -382941440}, {"key": "com/airbnb/lottie/value/LottieRelativeIntegerValueCallback.class", "name": "com/airbnb/lottie/value/LottieRelativeIntegerValueCallback.class", "size": 1854, "crc": -50013755}, {"key": "com/airbnb/lottie/value/LottieRelativePointValueCallback.class", "name": "com/airbnb/lottie/value/LottieRelativePointValueCallback.class", "size": 2245, "crc": -348937579}, {"key": "com/airbnb/lottie/value/LottieValueCallback.class", "name": "com/airbnb/lottie/value/LottieValueCallback.class", "size": 2851, "crc": 553794114}, {"key": "com/airbnb/lottie/value/ScaleXY.class", "name": "com/airbnb/lottie/value/ScaleXY.class", "size": 1185, "crc": -1501876265}, {"key": "com/airbnb/lottie/value/SimpleLottieValueCallback.class", "name": "com/airbnb/lottie/value/SimpleLottieValueCallback.class", "size": 363, "crc": -1723697582}]