[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 65, "crc": -1271577704}, {"key": "okhttp3/logging/HttpLoggingInterceptor$Logger$Companion.class", "name": "okhttp3/logging/HttpLoggingInterceptor$Logger$Companion.class", "size": 1027, "crc": 1980620574}, {"key": "okhttp3/logging/HttpLoggingInterceptor$Logger.class", "name": "okhttp3/logging/HttpLoggingInterceptor$Logger.class", "size": 1198, "crc": -190145060}, {"key": "okhttp3/logging/Utf8Kt.class", "name": "okhttp3/logging/Utf8Kt.class", "size": 1411, "crc": -1293603456}, {"key": "okhttp3/logging/HttpLoggingInterceptor$Level.class", "name": "okhttp3/logging/HttpLoggingInterceptor$Level.class", "size": 1642, "crc": 1071852874}, {"key": "okhttp3/logging/LoggingEventListener.class", "name": "okhttp3/logging/LoggingEventListener.class", "size": 9361, "crc": -204028436}, {"key": "okhttp3/logging/HttpLoggingInterceptor.class", "name": "okhttp3/logging/HttpLoggingInterceptor.class", "size": 11786, "crc": -1023898799}, {"key": "okhttp3/logging/HttpLoggingInterceptor$Logger$Companion$DefaultLogger.class", "name": "okhttp3/logging/HttpLoggingInterceptor$Logger$Companion$DefaultLogger.class", "size": 1640, "crc": 1076790706}, {"key": "okhttp3/logging/LoggingEventListener$Factory.class", "name": "okhttp3/logging/LoggingEventListener$Factory.class", "size": 2008, "crc": -1986125625}, {"key": "META-INF/okhttp-logging-interceptor.kotlin_module", "name": "META-INF/okhttp-logging-interceptor.kotlin_module", "size": 51, "crc": 1966310142}]