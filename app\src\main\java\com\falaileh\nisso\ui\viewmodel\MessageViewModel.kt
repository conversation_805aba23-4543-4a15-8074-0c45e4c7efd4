package com.falaileh.nisso.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.falaileh.nisso.data.model.LoveMessage
import com.falaileh.nisso.data.model.LoveMessageUiState
import com.falaileh.nisso.data.repository.MessageRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch

/**
 * ViewModel for managing love messages UI state and business logic
 */
class MessageViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository = MessageRepository(application)
    
    private val _uiState = MutableStateFlow(LoveMessageUiState())
    val uiState: StateFlow<LoveMessageUiState> = _uiState.asStateFlow()
    
    init {
        loadMessages()
    }
    
    /**
     * Load messages from repository
     */
    private fun loadMessages() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            repository.getMessages()
                .catch { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Unknown error occurred"
                    )
                }
                .collect { result ->
                    result.fold(
                        onSuccess = { messages ->
                            _uiState.value = _uiState.value.copy(
                                messages = messages,
                                isLoading = false,
                                error = null,
                                isOffline = false,
                                currentMessageIndex = if (messages.isNotEmpty()) 0 else 0
                            )
                        },
                        onFailure = { exception ->
                            _uiState.value = _uiState.value.copy(
                                messages = emptyList(), // Clear any old messages
                                isLoading = false,
                                error = exception.message ?: "Failed to load messages",
                                isOffline = true
                            )
                        }
                    )
                }
        }
    }
    
    /**
     * Refresh messages from API
     */
    fun refreshMessages() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isRefreshing = true, error = null)
            
            val result = repository.refreshMessages()
            result.fold(
                onSuccess = { messages ->
                    _uiState.value = _uiState.value.copy(
                        messages = messages,
                        isRefreshing = false,
                        error = null,
                        isOffline = false,
                        currentMessageIndex = if (messages.isNotEmpty()) 0 else 0
                    )
                },
                onFailure = { exception ->
                    _uiState.value = _uiState.value.copy(
                        messages = emptyList(), // Clear any old messages on refresh failure
                        isRefreshing = false,
                        error = exception.message ?: "Failed to refresh messages",
                        isOffline = true
                    )
                }
            )
        }
    }
    
    /**
     * Navigate to next message
     */
    fun nextMessage() {
        val currentState = _uiState.value
        if (currentState.hasMessages) {
            val nextIndex = (currentState.currentMessageIndex + 1) % currentState.messageCount
            _uiState.value = currentState.copy(currentMessageIndex = nextIndex)
        }
    }
    
    /**
     * Navigate to previous message
     */
    fun previousMessage() {
        val currentState = _uiState.value
        if (currentState.hasMessages) {
            val prevIndex = if (currentState.currentMessageIndex == 0) {
                currentState.messageCount - 1
            } else {
                currentState.currentMessageIndex - 1
            }
            _uiState.value = currentState.copy(currentMessageIndex = prevIndex)
        }
    }
    
    /**
     * Navigate to specific message index
     */
    fun goToMessage(index: Int) {
        val currentState = _uiState.value
        if (currentState.hasMessages && index in 0 until currentState.messageCount) {
            _uiState.value = currentState.copy(currentMessageIndex = index)
        }
    }
    
    /**
     * Clear error state
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    // Removed cache cleaning - app now only uses live API data
    
    /**
     * Get current message
     */
    fun getCurrentMessage(): LoveMessage? = _uiState.value.currentMessage
    
    /**
     * Check if there are messages available
     */
    fun hasMessages(): Boolean = _uiState.value.hasMessages
    
    /**
     * Get message count
     */
    fun getMessageCount(): Int = _uiState.value.messageCount
    
    /**
     * Check if currently loading
     */
    fun isLoading(): Boolean = _uiState.value.isLoading
    
    /**
     * Check if currently refreshing
     */
    fun isRefreshing(): Boolean = _uiState.value.isRefreshing
}
