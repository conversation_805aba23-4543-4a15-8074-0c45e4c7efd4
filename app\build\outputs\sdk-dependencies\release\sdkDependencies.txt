# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.1"
  }
  digests {
    sha256: "\031\272P\320\224\3076\216\336\033L\317\021\225\316\270>5\227\a6Y?\202>Z\367\026\370\320]p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.0"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.1"
  }
  digests {
    sha256: ",\'\336\031\2255gP\005U0fYzK \372\036\352|\"\212\264\357k2\265\3769\312\037Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\317{rd|y\225\a\025\210\376\207\004P\377\234\217\022\177%=-HQ\341a\270\000\366z\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\347a\377oO\a\t5X\267\3157\t(\363\035S\264n\251\024A\365-\3255\332\226\324p\214+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.3"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.8.3"
  }
  digests {
    sha256: "\375\277pb\vHU\n\262?\317\004\331\356Y\022;\370\303\035\262`\026\312t\3233*yDJ\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.3"
  }
  digests {
    sha256: "\001w\226\224\261\350\"\322\356\271\336\316\310o@\334)\203\200k\230_\264\216\341\026\310`SNz\305"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.8.3"
  }
  digests {
    sha256: "\326\276\336\302X\343\277\310m,\272\'\376\244\300=\202\207 W\277\312*CBF\036m\357\262\323^"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.8.3"
  }
  digests {
    sha256: ".\325G.\362.\235X\233\270\222\324m]:\326\214\272i\234\000\237\304\367\252{\n\0173\vSd"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.3"
  }
  digests {
    sha256: "\323\372\"W\315L\365\022q}\300\214\0233\210r\2616\271}-Q\017\3167;\234\303\221\352\031\271"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\312\316\377\320v\271\207Ou\352\307 \256H\313.\213\372Uc\023\225\271\177\260\366\202\322y4*\025"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.7.0"
  }
  digests {
    sha256: "E\334cRLpZ\266\032\265g\223\236\265\v\233\204\005\234P]\ak+\337-\312&hJ\000\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.7.0"
  }
  digests {
    sha256: "-ggP\3726\371\360$7\005\035=^\345\341`\266q\302\232\251l1\njK\215\307\251\326\226"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\323\365\321B;\306\334\fsC\247#\211B[\344\266\357\203.\364a\372\361T+\274bK\3339\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.3"
  }
  digests {
    sha256: "C\322\212lm\251\301\313r\277\265\314\033Q\032y7\262\333\213y\325/7\320\267\361Ly\260\220\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\273\365M\3140\f\201\352\221\365fz\300\270Y4\262\313\312\377\300\200m\203\211\337\024l\302\024\212\\"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.8.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose-android"
    version: "2.8.3"
  }
  digests {
    sha256: "\331\b\017D5\177\301\311\241\a\241\377\240\006\031\257\'\361\304t\001\251\230\346\264(/\341\236\3238\343"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\341\377\306_\202S\2552Es\367~:T\321\'ZF\351\177\031\372*\ap\212l\264\265\314N\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\277\356\022\301\310\214?t\225O\277ngf\274\0309V\363tx\267\300$\372\347\365\263\204\223\327\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.3"
  }
  digests {
    sha256: "\203\235\321$S\304\340Q\026s(1\377I\362\020\211<\311\233p\221\273\232\022I\210\221\034Sw4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.3"
  }
  digests {
    sha256: "\341{\f\324\000\201\250l\243\255\313\213\023^\350/=\330B\333B\a.\262\220\034d\242w:mu"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.8.0"
  }
  digests {
    sha256: ";F+\307`\353\241\200\225eC#,\244g\307\266 \n\t\034[\372\024o\220l\372\274\205c\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\351\265Vc3\214A\247P\003\215\314\317G)\035\226\027\251}-\240\203\377\200@1\331\256G\331I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.7.0"
  }
  digests {
    sha256: "#w\245\257\262\345\237\206It\355\224\336\371\305\252\204\022\350\bBi\274[\247\027\364\262b\251I\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\023!\245\a\310`_\202\005\r\345\353\256\216\r\a*\271\267\302z\2355^\016>>\2222q\203T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\213!Q{1\022\265\267\2178\322\034X\034\230\033U*\b!\206_\355\234\257s6\ro\217q\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\017\3544z\021o\003\016z\272\213\035A\b8S2\023@\234\222\271 $\253\363_\205V\200M\371"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\360\244v0\264\340\f\033\231{?\347o\331n \253\354\211\372\325F\321\221c\332\030\324\240\210\241S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.7.0"
  }
  digests {
    sha256: "c\022v\330\b\303>5\253\362\371\027\300r\372\034\321z\225\222\233\210<t\311B\250\322\2072\233\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.7.0"
  }
  digests {
    sha256: "C\234\233\310\335\225B\352| \205\031\a{\215G\304\306\215>i\237\216\226\037\270\v\302\225[\273\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.7.0"
  }
  digests {
    sha256: "-\302#Y\336\203\355\002Y\250\267\203O\026\331\207\330*\220\307\031~\253\264Mr\313\332\306{\370,"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\323\340\371\200\376\231\216\314\333\360\351x\354\346\324)\345\346\255\211\225z2\303\360r8)\356\355\206$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.09.00"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\222]$c\310\vG\263\360L\253oI8\025>\017]\225\217\020\255\371B(\327\036\340n\037\304\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tv"
    artifactId: "tv-foundation"
    version: "1.0.0-alpha07"
  }
  digests {
    sha256: "\312*7\324\001\247h4\260\255T2@\253\234\240\350Q/\325\231\342vT\003\027O\025\177k3\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.0.0-beta03"
  }
  digests {
    sha256: "\262f\210\343\314\311i\201\243\246\212{+\035\366\324u\200,\360C\226W\236\364^\315\260\223\305D\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.0.0-beta03"
  }
  digests {
    sha256: "\303>\355`\332m\232\375\204\275\354\313\177\027xB\336@x\202B\262n\230\336@\\\234\333\034\241\346"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.0.1-android"
  }
  digests {
    sha256: "\224%\244#\244\313\235\235\2605c\000r-\233\330\3464\317S\237)\331{\270OE|\314\321n\270"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.0.0-beta03"
  }
  digests {
    sha256: "d\202(\237\252\037\3559vb\243\b-\244@p\225S\211x\266\240G6O\346\227{?\252dp"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.0.0-beta03"
  }
  digests {
    sha256: "\322\330\251\'U\b\212\211\347\317\362\365\336xG\312A\356\202ob\222\031\003\227\221\025\342\235\241Or"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.0.0-beta03"
  }
  digests {
    sha256: "\345;\364\003>}\320\037{\375\217N<XFG\035O\232(t\353\v\307\325~b\345c\313+%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.0.0-beta03"
  }
  digests {
    sha256: "V\232~\254\"@\274\016\336\272l\221~\246\355\341\026/Q\353\266\003\037\302\210\276`\330\226\267{\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tv"
    artifactId: "tv-material"
    version: "1.0.0-alpha07"
  }
  digests {
    sha256: "\315\312R\023`Rk\255\276\232\217\vM\267\023\v>\3101\n\334\271\261\032\005bA\0250>\212\271"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie-compose"
    version: "6.1.0"
  }
  digests {
    sha256: "N$\315\'\001\367\r\203\374.+\371\343\r\202w\345\340v\3010\214\356\322\274PS\020\257\327\256\271"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie"
    version: "6.1.0"
  }
  digests {
    sha256: "Di\322\024.\n\017\353\246\321\224R\206\030\202L\346\302}C\236\031!+{\336\250\354\303\005\217\237"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 80
  library_dep_index: 0
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 18
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 1
  library_dep_index: 24
  library_dep_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 25
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 3
}
library_dependencies {
  library_index: 26
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 20
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 27
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 28
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 29
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 30
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 31
  library_dep_index: 6
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 6
}
library_dependencies {
  library_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
  library_dep_index: 35
  library_dep_index: 16
  library_dep_index: 39
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
  library_dep_index: 28
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 35
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 43
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 35
  library_dep_index: 45
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 49
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 52
  library_dep_index: 49
}
library_dependencies {
  library_index: 45
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 47
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 55
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 64
  library_dep_index: 62
  library_dep_index: 58
  library_dep_index: 8
  library_dep_index: 71
  library_dep_index: 66
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 53
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 62
  library_dep_index: 58
  library_dep_index: 72
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
  library_dep_index: 5
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 54
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 41
  library_dep_index: 49
  library_dep_index: 53
  library_dep_index: 50
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 54
  library_dep_index: 47
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 27
  library_dep_index: 41
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 52
}
library_dependencies {
  library_index: 50
  library_dep_index: 6
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 50
}
library_dependencies {
  library_index: 52
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 49
}
library_dependencies {
  library_index: 53
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 14
}
library_dependencies {
  library_index: 54
  library_dep_index: 47
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 45
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 47
}
library_dependencies {
  library_index: 55
  library_dep_index: 8
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 6
  library_dep_index: 35
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 45
  library_dep_index: 60
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 62
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 59
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 45
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 62
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 35
  library_dep_index: 62
  library_dep_index: 58
  library_dep_index: 8
  library_dep_index: 70
  library_dep_index: 2
  library_dep_index: 45
  library_dep_index: 56
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 62
  library_dep_index: 58
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 35
  library_dep_index: 56
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 45
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 58
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 58
  library_dep_index: 8
  library_dep_index: 66
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 22
  library_dep_index: 45
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 68
  library_dep_index: 62
  library_dep_index: 58
}
library_dependencies {
  library_index: 66
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 66
  library_dep_index: 66
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
  library_dep_index: 35
  library_dep_index: 2
  library_dep_index: 45
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 64
  library_dep_index: 62
  library_dep_index: 58
}
library_dependencies {
  library_index: 70
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 71
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 74
  library_dep_index: 78
  library_dep_index: 35
  library_dep_index: 45
  library_dep_index: 64
  library_dep_index: 58
  library_dep_index: 8
  library_dep_index: 66
  library_dep_index: 2
  library_dep_index: 78
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 75
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 35
  library_dep_index: 45
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 58
  library_dep_index: 2
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 35
  library_dep_index: 45
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 22
  library_dep_index: 74
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
}
library_dependencies {
  library_index: 79
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 76
  library_dep_index: 35
  library_dep_index: 45
  library_dep_index: 62
  library_dep_index: 58
  library_dep_index: 8
  library_dep_index: 2
  library_dep_index: 72
}
library_dependencies {
  library_index: 80
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 81
  library_dep_index: 48
  library_dep_index: 6
  library_dep_index: 82
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 66
  library_dep_index: 67
  library_dep_index: 88
  library_dep_index: 16
  library_dep_index: 41
  library_dep_index: 91
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 82
}
library_dependencies {
  library_index: 82
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 81
}
library_dependencies {
  library_index: 83
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 84
  library_dep_index: 83
  library_dep_index: 15
  library_dep_index: 10
}
library_dependencies {
  library_index: 85
  library_dep_index: 6
}
library_dependencies {
  library_index: 86
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 87
}
library_dependencies {
  library_index: 87
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 88
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 48
  library_dep_index: 27
  library_dep_index: 41
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 9
}
library_dependencies {
  library_index: 89
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 87
}
library_dependencies {
  library_index: 90
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 26
  library_dep_index: 41
}
library_dependencies {
  library_index: 91
  library_dep_index: 6
}
library_dependencies {
  library_index: 92
  library_dep_index: 45
  library_dep_index: 60
  library_dep_index: 68
  library_dep_index: 74
  library_dep_index: 72
  library_dep_index: 78
  library_dep_index: 35
  library_dep_index: 64
  library_dep_index: 58
  library_dep_index: 93
  library_dep_index: 37
  library_dep_index: 46
  library_dep_index: 61
  library_dep_index: 69
  library_dep_index: 75
  library_dep_index: 73
  library_dep_index: 79
  library_dep_index: 36
  library_dep_index: 65
  library_dep_index: 59
  library_dep_index: 94
  library_dep_index: 38
  library_dep_index: 56
  library_dep_index: 62
  library_dep_index: 76
  library_dep_index: 57
  library_dep_index: 63
  library_dep_index: 77
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
}
library_dependencies {
  library_index: 94
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 95
  library_dep_index: 6
  library_dep_index: 74
  library_dep_index: 72
  library_dep_index: 78
  library_dep_index: 35
  library_dep_index: 45
  library_dep_index: 60
  library_dep_index: 64
  library_dep_index: 58
  library_dep_index: 96
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 104
}
library_dependencies {
  library_index: 96
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 97
  library_dep_index: 101
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 100
}
library_dependencies {
  library_index: 97
  library_dep_index: 6
  library_dep_index: 98
  library_dep_index: 9
  library_dep_index: 100
  library_dep_index: 101
  library_dep_index: 102
  library_dep_index: 96
  library_dep_index: 103
}
library_dependencies {
  library_index: 98
  library_dep_index: 99
  library_dep_index: 14
}
library_dependencies {
  library_index: 100
  library_dep_index: 97
  library_dep_index: 6
}
library_dependencies {
  library_index: 101
  library_dep_index: 97
  library_dep_index: 100
  library_dep_index: 6
}
library_dependencies {
  library_index: 102
  library_dep_index: 97
  library_dep_index: 6
}
library_dependencies {
  library_index: 103
  library_dep_index: 6
  library_dep_index: 97
  library_dep_index: 102
}
library_dependencies {
  library_index: 104
  library_dep_index: 74
  library_dep_index: 93
  library_dep_index: 53
  library_dep_index: 95
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 95
}
library_dependencies {
  library_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 106
  library_dep_index: 107
  library_dep_index: 3
}
library_dependencies {
  library_index: 107
  library_dep_index: 108
}
library_dependencies {
  library_index: 108
  library_dep_index: 3
  library_dep_index: 2
}
library_dependencies {
  library_index: 109
  library_dep_index: 105
  library_dep_index: 110
}
library_dependencies {
  library_index: 111
  library_dep_index: 106
  library_dep_index: 3
}
library_dependencies {
  library_index: 112
  library_dep_index: 9
  library_dep_index: 19
  library_dep_index: 113
  library_dep_index: 115
  library_dep_index: 116
  library_dep_index: 113
  library_dep_index: 114
}
library_dependencies {
  library_index: 113
  library_dep_index: 6
  library_dep_index: 3
  library_dep_index: 114
  library_dep_index: 112
}
library_dependencies {
  library_index: 114
  library_dep_index: 113
  library_dep_index: 112
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 113
  library_dep_index: 112
}
library_dependencies {
  library_index: 115
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 116
}
library_dependencies {
  library_index: 116
  library_dep_index: 6
  library_dep_index: 115
  library_dep_index: 0
  library_dep_index: 115
}
library_dependencies {
  library_index: 117
  library_dep_index: 92
  library_dep_index: 72
  library_dep_index: 45
  library_dep_index: 118
  library_dep_index: 3
}
library_dependencies {
  library_index: 118
  library_dep_index: 81
  library_dep_index: 107
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 81
  dependency_index: 92
  dependency_index: 45
  dependency_index: 60
  dependency_index: 68
  dependency_index: 95
  dependency_index: 104
  dependency_index: 39
  dependency_index: 54
  dependency_index: 105
  dependency_index: 109
  dependency_index: 106
  dependency_index: 111
  dependency_index: 110
  dependency_index: 112
  dependency_index: 114
  dependency_index: 43
  dependency_index: 29
  dependency_index: 117
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
