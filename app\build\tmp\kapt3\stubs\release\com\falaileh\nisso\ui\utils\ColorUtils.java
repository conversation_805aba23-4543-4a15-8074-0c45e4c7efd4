package com.falaileh.nisso.ui.utils;

/**
 * Utility object for color parsing and manipulation
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0010\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u0005H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u000b\u0010\fJ\u001e\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u000e\u001a\u00020\u0005\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u0018\u0010\u0011\u001a\u00020\u00052\u0006\u0010\u0012\u001a\u00020\u0005\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0013\u0010\u0014J\u0013\u0010\u0015\u001a\u00020\u0005\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0016\u0010\u0017J\u001b\u0010\u0018\u001a\u00020\u00052\u0006\u0010\u0019\u001a\u00020\u001a\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001b\u0010\u001cR\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006\u001d"}, d2 = {"Lcom/falaileh/nisso/ui/utils/ColorUtils;", "", "()V", "romanticColors", "", "Landroidx/compose/ui/graphics/Color;", "getRomanticColors", "()Ljava/util/List;", "calculateLuminance", "", "color", "calculateLuminance-8_81llA", "(J)F", "createRomanticGradient", "baseColor", "createRomanticGradient-8_81llA", "(J)Ljava/util/List;", "getContrastingTextColor", "backgroundColor", "getContrastingTextColor-l2rxGTc", "(J)J", "getRandomRomanticColor", "getRandomRomanticColor-0d7_KjU", "()J", "parseColor", "colorString", "", "parseColor-vNxB06k", "(Ljava/lang/String;)J", "app_release"})
public final class ColorUtils {
    
    /**
     * Predefined romantic colors for fallback
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> romanticColors = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.falaileh.nisso.ui.utils.ColorUtils INSTANCE = null;
    
    private ColorUtils() {
        super();
    }
    
    /**
     * Predefined romantic colors for fallback
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<androidx.compose.ui.graphics.Color> getRomanticColors() {
        return null;
    }
}