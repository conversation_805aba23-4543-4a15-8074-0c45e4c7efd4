package com.falaileh.nisso.ui.animations;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0010\u0007\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016R+\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u00048F@BX\u0086\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\t\u0010\n\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\bR+\u0010\f\u001a\u00020\u000b2\u0006\u0010\u0003\u001a\u00020\u000b8F@BX\u0086\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\u0011\u0010\n\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010\u00a8\u0006\u0017"}, d2 = {"Lcom/falaileh/nisso/ui/animations/MessageTransitionState;", "", "()V", "<set-?>", "", "isTransitioning", "()Z", "setTransitioning", "(Z)V", "isTransitioning$delegate", "Landroidx/compose/runtime/MutableState;", "", "transitionProgress", "getTransitionProgress", "()F", "setTransitionProgress", "(F)V", "transitionProgress$delegate", "startTransition", "", "durationMs", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class MessageTransitionState {
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.runtime.MutableState isTransitioning$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.runtime.MutableState transitionProgress$delegate = null;
    
    public MessageTransitionState() {
        super();
    }
    
    public final boolean isTransitioning() {
        return false;
    }
    
    private final void setTransitioning(boolean p0) {
    }
    
    public final float getTransitionProgress() {
        return 0.0F;
    }
    
    private final void setTransitionProgress(float p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object startTransition(long durationMs, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}