[{"key": "androidx/compose/foundation/AbstractClickableNode$TraverseKey.class", "name": "androidx/compose/foundation/AbstractClickableNode$TraverseKey.class", "size": 862, "crc": -298932202}, {"key": "androidx/compose/foundation/AbstractClickableNode$applySemantics$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$applySemantics$1.class", "size": 1498, "crc": 158833719}, {"key": "androidx/compose/foundation/AbstractClickableNode$emitHoverEnter$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$emitHoverEnter$1$1.class", "size": 4141, "crc": -1941625265}, {"key": "androidx/compose/foundation/AbstractClickableNode$emitHoverExit$1$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$emitHoverExit$1$1$1.class", "size": 4139, "crc": -1530813071}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1$delayJob$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1$delayJob$1.class", "size": 5093, "crc": -317276002}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1.class", "size": 7566, "crc": -1496997139}, {"key": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$1.class", "size": 4273, "crc": -1547898135}, {"key": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$2$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$2$1.class", "size": 4457, "crc": -1648105530}, {"key": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$1.class", "size": 3560, "crc": -1114790827}, {"key": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$2.class", "name": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$2.class", "size": 3559, "crc": 307293296}, {"key": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$3.class", "name": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$3.class", "size": 3757, "crc": -1851655358}, {"key": "androidx/compose/foundation/AbstractClickableNode.class", "name": "androidx/compose/foundation/AbstractClickableNode.class", "size": 23436, "crc": -1181098100}, {"key": "androidx/compose/foundation/ActualJvm_jvmKt.class", "name": "androidx/compose/foundation/ActualJvm_jvmKt.class", "size": 671, "crc": -37985904}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$applyToFling$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$applyToFling$1.class", "size": 1981, "crc": -1567570367}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$effectModifier$1$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$effectModifier$1$1.class", "size": 8861, "crc": -1684486944}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$effectModifier$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$effectModifier$1.class", "size": 4107, "crc": -633953318}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$special$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$special$$inlined$debugInspectorInfo$1.class", "size": 2910, "crc": -521859661}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$special$$inlined$debugInspectorInfo$2.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$special$$inlined$debugInspectorInfo$2.class", "size": 2910, "crc": 1294845947}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect.class", "size": 20161, "crc": -2029492473}, {"key": "androidx/compose/foundation/AndroidEmbeddedExternalSurfaceState.class", "name": "androidx/compose/foundation/AndroidEmbeddedExternalSurfaceState.class", "size": 3786, "crc": 1611471967}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceScope.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceScope.class", "size": 1276, "crc": -660810491}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceState.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceState.class", "size": 2694, "crc": -40251785}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceZOrder$Companion.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceZOrder$Companion.class", "size": 1491, "crc": 524373564}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceZOrder.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceZOrder.class", "size": 2956, "crc": -950089192}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$1$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$1$1.class", "size": 2866, "crc": -1117173048}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$2.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$2.class", "size": 1658, "crc": -493199266}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$3$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$3$1.class", "size": 3950, "crc": 345673842}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$4.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$4.class", "size": 2488, "crc": 184620733}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$1$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$1$1.class", "size": 2669, "crc": 1526999142}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$2.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$2.class", "size": 1634, "crc": 1845103671}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$3$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$3$1.class", "size": 2914, "crc": 1337166964}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$4.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$4.class", "size": 2506, "crc": 2143200317}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt.class", "size": 17962, "crc": 1988916074}, {"key": "androidx/compose/foundation/AndroidOverscroll_androidKt.class", "name": "androidx/compose/foundation/AndroidOverscroll_androidKt.class", "size": 4962, "crc": -1980332554}, {"key": "androidx/compose/foundation/Api31Impl.class", "name": "androidx/compose/foundation/Api31Impl.class", "size": 2185, "crc": 1791244307}, {"key": "androidx/compose/foundation/BackgroundElement.class", "name": "androidx/compose/foundation/BackgroundElement.class", "size": 5360, "crc": -1411582009}, {"key": "androidx/compose/foundation/BackgroundKt$background$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/BackgroundKt$background$$inlined$debugInspectorInfo$1.class", "size": 3207, "crc": 511886342}, {"key": "androidx/compose/foundation/BackgroundKt$background-bw27NRU$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/BackgroundKt$background-bw27NRU$$inlined$debugInspectorInfo$1.class", "size": 3172, "crc": 1604789309}, {"key": "androidx/compose/foundation/BackgroundKt.class", "name": "androidx/compose/foundation/BackgroundKt.class", "size": 4212, "crc": 1858006405}, {"key": "androidx/compose/foundation/BackgroundNode$getOutline$1.class", "name": "androidx/compose/foundation/BackgroundNode$getOutline$1.class", "size": 2487, "crc": 1684720428}, {"key": "androidx/compose/foundation/BackgroundNode.class", "name": "androidx/compose/foundation/BackgroundNode.class", "size": 8052, "crc": 1346256746}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1$receiver$1.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1$receiver$1.class", "size": 2962, "crc": 57543580}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1.class", "size": 5130, "crc": 808103324}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState.class", "size": 6161, "crc": -631620396}, {"key": "androidx/compose/foundation/BasicMarqueeKt.class", "name": "androidx/compose/foundation/BasicMarqueeKt.class", "size": 6693, "crc": -330310723}, {"key": "androidx/compose/foundation/BasicTooltipDefaults.class", "name": "androidx/compose/foundation/BasicTooltipDefaults.class", "size": 1332, "crc": -2041012928}, {"key": "androidx/compose/foundation/BasicTooltipKt.class", "name": "androidx/compose/foundation/BasicTooltipKt.class", "size": 4532, "crc": 519467510}, {"key": "androidx/compose/foundation/BasicTooltipState.class", "name": "androidx/compose/foundation/BasicTooltipState.class", "size": 1806, "crc": -1756084476}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$2$1.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$2$1.class", "size": 3559, "crc": -144924195}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$2.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$2.class", "size": 4076, "crc": -1130171016}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$cancellableShow$1.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$cancellableShow$1.class", "size": 5252, "crc": -349807576}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl.class", "size": 5596, "crc": -592612415}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "size": 2207, "crc": 1554571231}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$2$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$2$1.class", "size": 3069, "crc": -1609585812}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$3.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$3.class", "size": 3053, "crc": 776253563}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$1$1$1.class", "size": 3441, "crc": 364352122}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$1$1.class", "size": 2154, "crc": -965804066}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$2$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$2$1$1.class", "size": 2175, "crc": -1098822942}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$2.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$2.class", "size": 10561, "crc": -52592111}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$3.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$3.class", "size": 2751, "crc": -527860862}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$WrappedAnchor$2.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$WrappedAnchor$2.class", "size": 2536, "crc": -2029302659}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1$1$1.class", "size": 3801, "crc": -2012617400}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1$1.class", "size": 2035, "crc": 863423654}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1.class", "size": 2458, "crc": 417688036}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1$1.class", "size": 4233, "crc": 977006554}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1$2.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1$2.class", "size": 3904, "crc": 1355069524}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1.class", "size": 6914, "crc": 67331013}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1.class", "size": 4184, "crc": 1617888817}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1.class", "size": 4092, "crc": 290201015}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1$1$1.class", "size": 3904, "crc": 1361073214}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1$1.class", "size": 5961, "crc": -716321717}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1.class", "size": 4137, "crc": -393592871}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2.class", "size": 4092, "crc": -1133949413}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt.class", "size": 24274, "crc": -1286013539}, {"key": "androidx/compose/foundation/BorderCache.class", "name": "androidx/compose/foundation/BorderCache.class", "size": 12443, "crc": 480150144}, {"key": "androidx/compose/foundation/BorderKt$drawContentWithoutBorder$1.class", "name": "androidx/compose/foundation/BorderKt$drawContentWithoutBorder$1.class", "size": 1679, "crc": -541562974}, {"key": "androidx/compose/foundation/BorderKt$drawRectBorder$1.class", "name": "androidx/compose/foundation/BorderKt$drawRectBorder$1.class", "size": 2341, "crc": 1968402651}, {"key": "androidx/compose/foundation/BorderKt.class", "name": "androidx/compose/foundation/BorderKt.class", "size": 8695, "crc": -1333627241}, {"key": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$1.class", "name": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$1.class", "size": 2488, "crc": 2061419500}, {"key": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$3.class", "name": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$3.class", "size": 4764, "crc": 1983087535}, {"key": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$1.class", "name": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$1.class", "size": 6061, "crc": 608609811}, {"key": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$2.class", "name": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$2.class", "size": 2427, "crc": -655723162}, {"key": "androidx/compose/foundation/BorderModifierNode$drawWithCacheModifierNode$1.class", "name": "androidx/compose/foundation/BorderModifierNode$drawWithCacheModifierNode$1.class", "size": 4666, "crc": 1901858518}, {"key": "androidx/compose/foundation/BorderModifierNode.class", "name": "androidx/compose/foundation/BorderModifierNode.class", "size": 21573, "crc": 1558040794}, {"key": "androidx/compose/foundation/BorderModifierNodeElement.class", "name": "androidx/compose/foundation/BorderModifierNodeElement.class", "size": 6412, "crc": -532579354}, {"key": "androidx/compose/foundation/BorderStroke.class", "name": "androidx/compose/foundation/BorderStroke.class", "size": 3297, "crc": -1015812754}, {"key": "androidx/compose/foundation/BorderStrokeKt.class", "name": "androidx/compose/foundation/BorderStrokeKt.class", "size": 1197, "crc": -680781903}, {"key": "androidx/compose/foundation/CanvasKt$Canvas$1.class", "name": "androidx/compose/foundation/CanvasKt$Canvas$1.class", "size": 2066, "crc": 550992917}, {"key": "androidx/compose/foundation/CanvasKt$Canvas$2$1.class", "name": "androidx/compose/foundation/CanvasKt$Canvas$2$1.class", "size": 1824, "crc": 631026012}, {"key": "androidx/compose/foundation/CanvasKt$Canvas$3.class", "name": "androidx/compose/foundation/CanvasKt$Canvas$3.class", "size": 2201, "crc": -1104273044}, {"key": "androidx/compose/foundation/CanvasKt.class", "name": "androidx/compose/foundation/CanvasKt.class", "size": 6237, "crc": 964127853}, {"key": "androidx/compose/foundation/CheckScrollableContainerConstraintsKt.class", "name": "androidx/compose/foundation/CheckScrollableContainerConstraintsKt.class", "size": 2910, "crc": 2033075093}, {"key": "androidx/compose/foundation/ClickableElement.class", "name": "androidx/compose/foundation/ClickableElement.class", "size": 5671, "crc": -527269271}, {"key": "androidx/compose/foundation/ClickableKt$clickable$2.class", "name": "androidx/compose/foundation/ClickableKt$clickable$2.class", "size": 6378, "crc": 421953439}, {"key": "androidx/compose/foundation/ClickableKt$clickable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$clickable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6802, "crc": 701236475}, {"key": "androidx/compose/foundation/ClickableKt$clickable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$clickable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3378, "crc": 979788475}, {"key": "androidx/compose/foundation/ClickableKt$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$clickableWithIndicationIfNeeded$1.class", "size": 5968, "crc": -941392229}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable$2.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable$2.class", "size": 7012, "crc": -1490438245}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-XVZzFYc$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-XVZzFYc$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 7262, "crc": -424899558}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-cJG_KMw$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-cJG_KMw$$inlined$debugInspectorInfo$1.class", "size": 3847, "crc": -2094563635}, {"key": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1$1.class", "name": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1$1.class", "size": 4366, "crc": 59264765}, {"key": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1$2$1.class", "name": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1$2$1.class", "size": 4546, "crc": 1267394548}, {"key": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1.class", "name": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1.class", "size": 5369, "crc": 1571688873}, {"key": "androidx/compose/foundation/ClickableKt$hasScrollableContainer$1.class", "name": "androidx/compose/foundation/ClickableKt$hasScrollableContainer$1.class", "size": 2206, "crc": 1769273786}, {"key": "androidx/compose/foundation/ClickableKt.class", "name": "androidx/compose/foundation/ClickableKt.class", "size": 21905, "crc": 1026644492}, {"key": "androidx/compose/foundation/ClickableNode$clickPointerInput$2.class", "name": "androidx/compose/foundation/ClickableNode$clickPointerInput$2.class", "size": 3792, "crc": 354086612}, {"key": "androidx/compose/foundation/ClickableNode$clickPointerInput$3.class", "name": "androidx/compose/foundation/ClickableNode$clickPointerInput$3.class", "size": 1837, "crc": 736737484}, {"key": "androidx/compose/foundation/ClickableNode.class", "name": "androidx/compose/foundation/ClickableNode.class", "size": 4684, "crc": -1061909912}, {"key": "androidx/compose/foundation/ClickableSemanticsElement.class", "name": "androidx/compose/foundation/ClickableSemanticsElement.class", "size": 5095, "crc": -1003388088}, {"key": "androidx/compose/foundation/ClickableSemanticsNode$applySemantics$1.class", "name": "androidx/compose/foundation/ClickableSemanticsNode$applySemantics$1.class", "size": 1564, "crc": 1867169812}, {"key": "androidx/compose/foundation/ClickableSemanticsNode$applySemantics$2.class", "name": "androidx/compose/foundation/ClickableSemanticsNode$applySemantics$2.class", "size": 1605, "crc": 1250920676}, {"key": "androidx/compose/foundation/ClickableSemanticsNode.class", "name": "androidx/compose/foundation/ClickableSemanticsNode.class", "size": 4472, "crc": -2142554814}, {"key": "androidx/compose/foundation/Clickable_androidKt.class", "name": "androidx/compose/foundation/Clickable_androidKt.class", "size": 2961, "crc": 802602503}, {"key": "androidx/compose/foundation/ClipScrollableContainerKt$HorizontalScrollableClipModifier$1.class", "name": "androidx/compose/foundation/ClipScrollableContainerKt$HorizontalScrollableClipModifier$1.class", "size": 2846, "crc": 633531353}, {"key": "androidx/compose/foundation/ClipScrollableContainerKt$VerticalScrollableClipModifier$1.class", "name": "androidx/compose/foundation/ClipScrollableContainerKt$VerticalScrollableClipModifier$1.class", "size": 2836, "crc": -765856742}, {"key": "androidx/compose/foundation/ClipScrollableContainerKt.class", "name": "androidx/compose/foundation/ClipScrollableContainerKt.class", "size": 3164, "crc": 1540481593}, {"key": "androidx/compose/foundation/CombinedClickableElement.class", "name": "androidx/compose/foundation/CombinedClickableElement.class", "size": 7410, "crc": -1937221059}, {"key": "androidx/compose/foundation/CombinedClickableNode.class", "name": "androidx/compose/foundation/CombinedClickableNode.class", "size": 2109, "crc": -1224184566}, {"key": "androidx/compose/foundation/CombinedClickableNodeImpl$applyAdditionalSemantics$1.class", "name": "androidx/compose/foundation/CombinedClickableNodeImpl$applyAdditionalSemantics$1.class", "size": 1653, "crc": -984142607}, {"key": "androidx/compose/foundation/CombinedClickableNodeImpl$clickPointerInput$2.class", "name": "androidx/compose/foundation/CombinedClickableNodeImpl$clickPointerInput$2.class", "size": 1885, "crc": -1656837835}, {"key": "androidx/compose/foundation/CombinedClickableNodeImpl$clickPointerInput$3.class", "name": "androidx/compose/foundation/CombinedClickableNodeImpl$clickPointerInput$3.class", "size": 1883, "crc": -142303153}, {"key": "androidx/compose/foundation/CombinedClickableNodeImpl$clickPointerInput$4.class", "name": "androidx/compose/foundation/CombinedClickableNodeImpl$clickPointerInput$4.class", "size": 3833, "crc": 1080018246}, {"key": "androidx/compose/foundation/CombinedClickableNodeImpl$clickPointerInput$5.class", "name": "androidx/compose/foundation/CombinedClickableNodeImpl$clickPointerInput$5.class", "size": 1850, "crc": 926569833}, {"key": "androidx/compose/foundation/CombinedClickableNodeImpl.class", "name": "androidx/compose/foundation/CombinedClickableNodeImpl.class", "size": 7895, "crc": 692960323}, {"key": "androidx/compose/foundation/DarkThemeKt.class", "name": "androidx/compose/foundation/DarkThemeKt.class", "size": 1453, "crc": -2031846601}, {"key": "androidx/compose/foundation/DarkTheme_androidKt.class", "name": "androidx/compose/foundation/DarkTheme_androidKt.class", "size": 2798, "crc": -343554070}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1$1.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1$1.class", "size": 4395, "crc": -1460784704}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1.class", "size": 4845, "crc": 265328349}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance.class", "size": 4199, "crc": -630004908}, {"key": "androidx/compose/foundation/DefaultDebugIndication.class", "name": "androidx/compose/foundation/DefaultDebugIndication.class", "size": 1898, "crc": 326848635}, {"key": "androidx/compose/foundation/DrawGlowOverscrollModifier.class", "name": "androidx/compose/foundation/DrawGlowOverscrollModifier.class", "size": 7487, "crc": 814990687}, {"key": "androidx/compose/foundation/DrawStretchOverscrollModifier.class", "name": "androidx/compose/foundation/DrawStretchOverscrollModifier.class", "size": 13658, "crc": 1157144117}, {"key": "androidx/compose/foundation/EdgeEffectCompat.class", "name": "androidx/compose/foundation/EdgeEffectCompat.class", "size": 2942, "crc": 944633320}, {"key": "androidx/compose/foundation/EdgeEffectWrapper.class", "name": "androidx/compose/foundation/EdgeEffectWrapper.class", "size": 8373, "crc": 1180392539}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureElement.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureElement.class", "size": 3920, "crc": 228482977}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureKt.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureKt.class", "size": 1784, "crc": -1864381013}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureNode.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureNode.class", "size": 3674, "crc": 1178044001}, {"key": "androidx/compose/foundation/ExperimentalFoundationApi.class", "name": "androidx/compose/foundation/ExperimentalFoundationApi.class", "size": 833, "crc": 1841402606}, {"key": "androidx/compose/foundation/FixedMotionDurationScale.class", "name": "androidx/compose/foundation/FixedMotionDurationScale.class", "size": 3079, "crc": 1107774048}, {"key": "androidx/compose/foundation/FocusableElement.class", "name": "androidx/compose/foundation/FocusableElement.class", "size": 3287, "crc": 234406873}, {"key": "androidx/compose/foundation/FocusableInNonTouchMode.class", "name": "androidx/compose/foundation/FocusableInNonTouchMode.class", "size": 2987, "crc": -1421608760}, {"key": "androidx/compose/foundation/FocusableInteractionNode$emitWithFallback$1.class", "name": "androidx/compose/foundation/FocusableInteractionNode$emitWithFallback$1.class", "size": 4310, "crc": 506193263}, {"key": "androidx/compose/foundation/FocusableInteractionNode$emitWithFallback$handler$1.class", "name": "androidx/compose/foundation/FocusableInteractionNode$emitWithFallback$handler$1.class", "size": 1906, "crc": -1683775919}, {"key": "androidx/compose/foundation/FocusableInteractionNode.class", "name": "androidx/compose/foundation/FocusableInteractionNode.class", "size": 5261, "crc": -684140191}, {"key": "androidx/compose/foundation/FocusableKt$FocusableInNonTouchModeElement$1.class", "name": "androidx/compose/foundation/FocusableKt$FocusableInNonTouchModeElement$1.class", "size": 2671, "crc": -1419199086}, {"key": "androidx/compose/foundation/FocusableKt$focusGroup$1.class", "name": "androidx/compose/foundation/FocusableKt$focusGroup$1.class", "size": 1577, "crc": -1952775266}, {"key": "androidx/compose/foundation/FocusableKt$special$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/FocusableKt$special$$inlined$debugInspectorInfo$1.class", "size": 2464, "crc": 1089160422}, {"key": "androidx/compose/foundation/FocusableKt.class", "name": "androidx/compose/foundation/FocusableKt.class", "size": 4374, "crc": -950187293}, {"key": "androidx/compose/foundation/FocusableNode$applySemantics$1.class", "name": "androidx/compose/foundation/FocusableNode$applySemantics$1.class", "size": 1587, "crc": 1284500926}, {"key": "androidx/compose/foundation/FocusableNode$onFocusEvent$1.class", "name": "androidx/compose/foundation/FocusableNode$onFocusEvent$1.class", "size": 3695, "crc": 1065433012}, {"key": "androidx/compose/foundation/FocusableNode.class", "name": "androidx/compose/foundation/FocusableNode.class", "size": 5503, "crc": -402902863}, {"key": "androidx/compose/foundation/FocusablePinnableContainerNode$retrievePinnableContainer$1.class", "name": "androidx/compose/foundation/FocusablePinnableContainerNode$retrievePinnableContainer$1.class", "size": 2369, "crc": 1442900953}, {"key": "androidx/compose/foundation/FocusablePinnableContainerNode.class", "name": "androidx/compose/foundation/FocusablePinnableContainerNode.class", "size": 3156, "crc": 1340580294}, {"key": "androidx/compose/foundation/FocusedBoundsKt.class", "name": "androidx/compose/foundation/FocusedBoundsKt.class", "size": 1448, "crc": -374341504}, {"key": "androidx/compose/foundation/FocusedBoundsNode$TraverseKey.class", "name": "androidx/compose/foundation/FocusedBoundsNode$TraverseKey.class", "size": 854, "crc": -2049629523}, {"key": "androidx/compose/foundation/FocusedBoundsNode.class", "name": "androidx/compose/foundation/FocusedBoundsNode.class", "size": 3651, "crc": 442617858}, {"key": "androidx/compose/foundation/FocusedBoundsObserverElement.class", "name": "androidx/compose/foundation/FocusedBoundsObserverElement.class", "size": 3638, "crc": 1923038386}, {"key": "androidx/compose/foundation/FocusedBoundsObserverNode$TraverseKey.class", "name": "androidx/compose/foundation/FocusedBoundsObserverNode$TraverseKey.class", "size": 878, "crc": -1135838379}, {"key": "androidx/compose/foundation/FocusedBoundsObserverNode.class", "name": "androidx/compose/foundation/FocusedBoundsObserverNode.class", "size": 3053, "crc": 597835536}, {"key": "androidx/compose/foundation/GlowEdgeEffectCompat.class", "name": "androidx/compose/foundation/GlowEdgeEffectCompat.class", "size": 3180, "crc": -1362922198}, {"key": "androidx/compose/foundation/HoverableElement.class", "name": "androidx/compose/foundation/HoverableElement.class", "size": 3215, "crc": 976938686}, {"key": "androidx/compose/foundation/HoverableKt.class", "name": "androidx/compose/foundation/HoverableKt.class", "size": 1782, "crc": 2040078212}, {"key": "androidx/compose/foundation/HoverableNode$emitEnter$1.class", "name": "androidx/compose/foundation/HoverableNode$emitEnter$1.class", "size": 1854, "crc": -721089065}, {"key": "androidx/compose/foundation/HoverableNode$emitExit$1.class", "name": "androidx/compose/foundation/HoverableNode$emitExit$1.class", "size": 1812, "crc": -748714494}, {"key": "androidx/compose/foundation/HoverableNode$onPointerEvent$1.class", "name": "androidx/compose/foundation/HoverableNode$onPointerEvent$1.class", "size": 3606, "crc": 1071340969}, {"key": "androidx/compose/foundation/HoverableNode$onPointerEvent$2.class", "name": "androidx/compose/foundation/HoverableNode$onPointerEvent$2.class", "size": 3605, "crc": 1253921698}, {"key": "androidx/compose/foundation/HoverableNode.class", "name": "androidx/compose/foundation/HoverableNode.class", "size": 6739, "crc": -545336090}, {"key": "androidx/compose/foundation/ImageKt$Image$1$1.class", "name": "androidx/compose/foundation/ImageKt$Image$1$1.class", "size": 1676, "crc": -1151459110}, {"key": "androidx/compose/foundation/ImageKt$Image$1.class", "name": "androidx/compose/foundation/ImageKt$Image$1.class", "size": 2322, "crc": 576216377}, {"key": "androidx/compose/foundation/ImageKt$Image$2.class", "name": "androidx/compose/foundation/ImageKt$Image$2.class", "size": 2582, "crc": 1872540779}, {"key": "androidx/compose/foundation/ImageKt$Image$semantics$1$1.class", "name": "androidx/compose/foundation/ImageKt$Image$semantics$1$1.class", "size": 2281, "crc": -1169720008}, {"key": "androidx/compose/foundation/ImageKt.class", "name": "androidx/compose/foundation/ImageKt.class", "size": 16429, "crc": 478750059}, {"key": "androidx/compose/foundation/Indication.class", "name": "androidx/compose/foundation/Indication.class", "size": 2453, "crc": 1390411313}, {"key": "androidx/compose/foundation/IndicationInstance.class", "name": "androidx/compose/foundation/IndicationInstance.class", "size": 1099, "crc": -948591678}, {"key": "androidx/compose/foundation/IndicationKt$LocalIndication$1.class", "name": "androidx/compose/foundation/IndicationKt$LocalIndication$1.class", "size": 1409, "crc": -1435815848}, {"key": "androidx/compose/foundation/IndicationKt$indication$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/IndicationKt$indication$$inlined$debugInspectorInfo$1.class", "size": 3167, "crc": -1570359981}, {"key": "androidx/compose/foundation/IndicationKt$indication$2.class", "name": "androidx/compose/foundation/IndicationKt$indication$2.class", "size": 4948, "crc": -1384912578}, {"key": "androidx/compose/foundation/IndicationKt.class", "name": "androidx/compose/foundation/IndicationKt.class", "size": 4932, "crc": -547171302}, {"key": "androidx/compose/foundation/IndicationModifier.class", "name": "androidx/compose/foundation/IndicationModifier.class", "size": 1584, "crc": 511456309}, {"key": "androidx/compose/foundation/IndicationModifierElement.class", "name": "androidx/compose/foundation/IndicationModifierElement.class", "size": 3663, "crc": 1577287226}, {"key": "androidx/compose/foundation/IndicationModifierNode.class", "name": "androidx/compose/foundation/IndicationModifierNode.class", "size": 1244, "crc": 736262854}, {"key": "androidx/compose/foundation/IndicationNodeFactory.class", "name": "androidx/compose/foundation/IndicationNodeFactory.class", "size": 1176, "crc": -359338172}, {"key": "androidx/compose/foundation/InternalFoundationApi.class", "name": "androidx/compose/foundation/InternalFoundationApi.class", "size": 1046, "crc": -1667127780}, {"key": "androidx/compose/foundation/MagnifierElement.class", "name": "androidx/compose/foundation/MagnifierElement.class", "size": 7872, "crc": 696120679}, {"key": "androidx/compose/foundation/MagnifierNode$anchorPositionInRoot$1.class", "name": "androidx/compose/foundation/MagnifierNode$anchorPositionInRoot$1.class", "size": 1894, "crc": -170757789}, {"key": "androidx/compose/foundation/MagnifierNode$applySemantics$1.class", "name": "androidx/compose/foundation/MagnifierNode$applySemantics$1.class", "size": 1492, "crc": -1547561591}, {"key": "androidx/compose/foundation/MagnifierNode$onAttach$1$1.class", "name": "androidx/compose/foundation/MagnifierNode$onAttach$1$1.class", "size": 1306, "crc": -653482603}, {"key": "androidx/compose/foundation/MagnifierNode$onAttach$1.class", "name": "androidx/compose/foundation/MagnifierNode$onAttach$1.class", "size": 4258, "crc": 1190935327}, {"key": "androidx/compose/foundation/MagnifierNode$onObservedReadsChanged$1.class", "name": "androidx/compose/foundation/MagnifierNode$onObservedReadsChanged$1.class", "size": 1265, "crc": 2028515087}, {"key": "androidx/compose/foundation/MagnifierNode.class", "name": "androidx/compose/foundation/MagnifierNode.class", "size": 20299, "crc": -787686076}, {"key": "androidx/compose/foundation/Magnifier_androidKt.class", "name": "androidx/compose/foundation/Magnifier_androidKt.class", "size": 7216, "crc": -1122332236}, {"key": "androidx/compose/foundation/MarqueeAnimationMode$Companion.class", "name": "androidx/compose/foundation/MarqueeAnimationMode$Companion.class", "size": 1301, "crc": -541335373}, {"key": "androidx/compose/foundation/MarqueeAnimationMode.class", "name": "androidx/compose/foundation/MarqueeAnimationMode.class", "size": 2905, "crc": -760034272}, {"key": "androidx/compose/foundation/MarqueeDefaults.class", "name": "androidx/compose/foundation/MarqueeDefaults.class", "size": 2889, "crc": 136077664}, {"key": "androidx/compose/foundation/MarqueeModifierElement.class", "name": "androidx/compose/foundation/MarqueeModifierElement.class", "size": 6730, "crc": -469539584}, {"key": "androidx/compose/foundation/MarqueeModifierNode$WhenMappings.class", "name": "androidx/compose/foundation/MarqueeModifierNode$WhenMappings.class", "size": 819, "crc": -898092723}, {"key": "androidx/compose/foundation/MarqueeModifierNode$measure$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$measure$1.class", "size": 2575, "crc": -537866019}, {"key": "androidx/compose/foundation/MarqueeModifierNode$restartAnimation$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$restartAnimation$1.class", "size": 3862, "crc": 1544603090}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$1.class", "size": 2123, "crc": -723405180}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$2.class", "size": 5486, "crc": -1801721394}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2.class", "size": 4071, "crc": -121033600}, {"key": "androidx/compose/foundation/MarqueeModifierNode$spacingPx$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$spacingPx$2.class", "size": 2105, "crc": -680988436}, {"key": "androidx/compose/foundation/MarqueeModifierNode.class", "name": "androidx/compose/foundation/MarqueeModifierNode.class", "size": 19927, "crc": 2100054130}, {"key": "androidx/compose/foundation/MarqueeSpacing$Companion.class", "name": "androidx/compose/foundation/MarqueeSpacing$Companion.class", "size": 1764, "crc": 1939657812}, {"key": "androidx/compose/foundation/MarqueeSpacing.class", "name": "androidx/compose/foundation/MarqueeSpacing.class", "size": 1051, "crc": -193681226}, {"key": "androidx/compose/foundation/MutatePriority.class", "name": "androidx/compose/foundation/MutatePriority.class", "size": 1487, "crc": -1686970598}, {"key": "androidx/compose/foundation/MutationInterruptedException.class", "name": "androidx/compose/foundation/MutationInterruptedException.class", "size": 1828, "crc": -715809018}, {"key": "androidx/compose/foundation/MutatorMutex$Mutator.class", "name": "androidx/compose/foundation/MutatorMutex$Mutator.class", "size": 2022, "crc": -775743984}, {"key": "androidx/compose/foundation/MutatorMutex$mutate$2.class", "name": "androidx/compose/foundation/MutatorMutex$mutate$2.class", "size": 7172, "crc": 159053670}, {"key": "androidx/compose/foundation/MutatorMutex$mutateWith$2.class", "name": "androidx/compose/foundation/MutatorMutex$mutateWith$2.class", "size": 7346, "crc": 1364222332}, {"key": "androidx/compose/foundation/MutatorMutex.class", "name": "androidx/compose/foundation/MutatorMutex.class", "size": 7010, "crc": -604549572}, {"key": "androidx/compose/foundation/NoIndicationInstance.class", "name": "androidx/compose/foundation/NoIndicationInstance.class", "size": 1227, "crc": 1387545319}, {"key": "androidx/compose/foundation/NoOpOverscrollEffect.class", "name": "androidx/compose/foundation/NoOpOverscrollEffect.class", "size": 3580, "crc": 2130624397}, {"key": "androidx/compose/foundation/OverscrollConfiguration.class", "name": "androidx/compose/foundation/OverscrollConfiguration.class", "size": 3673, "crc": -505875324}, {"key": "androidx/compose/foundation/OverscrollConfiguration_androidKt$LocalOverscrollConfiguration$1.class", "name": "androidx/compose/foundation/OverscrollConfiguration_androidKt$LocalOverscrollConfiguration$1.class", "size": 1572, "crc": 1833299898}, {"key": "androidx/compose/foundation/OverscrollConfiguration_androidKt.class", "name": "androidx/compose/foundation/OverscrollConfiguration_androidKt.class", "size": 1859, "crc": -1815643871}, {"key": "androidx/compose/foundation/OverscrollEffect.class", "name": "androidx/compose/foundation/OverscrollEffect.class", "size": 2060, "crc": -766034288}, {"key": "androidx/compose/foundation/OverscrollKt.class", "name": "androidx/compose/foundation/OverscrollKt.class", "size": 1161, "crc": 1689337302}, {"key": "androidx/compose/foundation/PlatformMagnifier.class", "name": "androidx/compose/foundation/PlatformMagnifier.class", "size": 856, "crc": 201609145}, {"key": "androidx/compose/foundation/PlatformMagnifierFactory$Companion.class", "name": "androidx/compose/foundation/PlatformMagnifierFactory$Companion.class", "size": 1878, "crc": -1832853889}, {"key": "androidx/compose/foundation/PlatformMagnifierFactory.class", "name": "androidx/compose/foundation/PlatformMagnifierFactory.class", "size": 1634, "crc": -556149441}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl$PlatformMagnifierImpl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl$PlatformMagnifierImpl.class", "size": 2493, "crc": -704032866}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl.class", "size": 2716, "crc": -1214052802}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl$PlatformMagnifierImpl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl$PlatformMagnifierImpl.class", "size": 2236, "crc": 1110139081}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl.class", "size": 5051, "crc": 1381124051}, {"key": "androidx/compose/foundation/PreferKeepClearElement.class", "name": "androidx/compose/foundation/PreferKeepClearElement.class", "size": 3812, "crc": 1227148372}, {"key": "androidx/compose/foundation/PreferKeepClearNode.class", "name": "androidx/compose/foundation/PreferKeepClearNode.class", "size": 3557, "crc": -332929720}, {"key": "androidx/compose/foundation/PreferKeepClear_androidKt.class", "name": "androidx/compose/foundation/PreferKeepClear_androidKt.class", "size": 2971, "crc": -1982531190}, {"key": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$1.class", "name": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$1.class", "size": 2584, "crc": -689570618}, {"key": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$2.class", "name": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$2.class", "size": 2167, "crc": -2115936275}, {"key": "androidx/compose/foundation/ProgressSemanticsKt.class", "name": "androidx/compose/foundation/ProgressSemanticsKt.class", "size": 2335, "crc": 468284899}, {"key": "androidx/compose/foundation/RectListNode.class", "name": "androidx/compose/foundation/RectListNode.class", "size": 6883, "crc": 1704593987}, {"key": "androidx/compose/foundation/ScrollKt$rememberScrollState$1$1.class", "name": "androidx/compose/foundation/ScrollKt$rememberScrollState$1$1.class", "size": 1364, "crc": -1498406702}, {"key": "androidx/compose/foundation/ScrollKt$scroll$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ScrollKt$scroll$$inlined$debugInspectorInfo$1.class", "size": 3434, "crc": 277841022}, {"key": "androidx/compose/foundation/ScrollKt$scroll$2.class", "name": "androidx/compose/foundation/ScrollKt$scroll$2.class", "size": 4339, "crc": 836387686}, {"key": "androidx/compose/foundation/ScrollKt.class", "name": "androidx/compose/foundation/ScrollKt.class", "size": 6618, "crc": 1353620175}, {"key": "androidx/compose/foundation/ScrollSemanticsElement.class", "name": "androidx/compose/foundation/ScrollSemanticsElement.class", "size": 6065, "crc": 1074057028}, {"key": "androidx/compose/foundation/ScrollSemanticsModifierNode$applySemantics$accessibilityScrollState$1.class", "name": "androidx/compose/foundation/ScrollSemanticsModifierNode$applySemantics$accessibilityScrollState$1.class", "size": 1633, "crc": -768532353}, {"key": "androidx/compose/foundation/ScrollSemanticsModifierNode$applySemantics$accessibilityScrollState$2.class", "name": "androidx/compose/foundation/ScrollSemanticsModifierNode$applySemantics$accessibilityScrollState$2.class", "size": 1636, "crc": -1258253415}, {"key": "androidx/compose/foundation/ScrollSemanticsModifierNode.class", "name": "androidx/compose/foundation/ScrollSemanticsModifierNode.class", "size": 4117, "crc": -1979869385}, {"key": "androidx/compose/foundation/ScrollState$Companion$Saver$1.class", "name": "androidx/compose/foundation/ScrollState$Companion$Saver$1.class", "size": 1839, "crc": -1263688559}, {"key": "androidx/compose/foundation/ScrollState$Companion$Saver$2.class", "name": "androidx/compose/foundation/ScrollState$Companion$Saver$2.class", "size": 1423, "crc": 759340777}, {"key": "androidx/compose/foundation/ScrollState$Companion.class", "name": "androidx/compose/foundation/ScrollState$Companion.class", "size": 1315, "crc": -1215568099}, {"key": "androidx/compose/foundation/ScrollState$canScrollBackward$2.class", "name": "androidx/compose/foundation/ScrollState$canScrollBackward$2.class", "size": 1351, "crc": 1502589884}, {"key": "androidx/compose/foundation/ScrollState$canScrollForward$2.class", "name": "androidx/compose/foundation/ScrollState$canScrollForward$2.class", "size": 1380, "crc": 543442781}, {"key": "androidx/compose/foundation/ScrollState$scrollableState$1.class", "name": "androidx/compose/foundation/ScrollState$scrollableState$1.class", "size": 2933, "crc": 1046076843}, {"key": "androidx/compose/foundation/ScrollState.class", "name": "androidx/compose/foundation/ScrollState.class", "size": 12366, "crc": 2028237084}, {"key": "androidx/compose/foundation/ScrollingContainerKt.class", "name": "androidx/compose/foundation/ScrollingContainerKt.class", "size": 5745, "crc": 718377173}, {"key": "androidx/compose/foundation/ScrollingLayoutElement.class", "name": "androidx/compose/foundation/ScrollingLayoutElement.class", "size": 4090, "crc": -1858912563}, {"key": "androidx/compose/foundation/ScrollingLayoutNode$measure$1$1.class", "name": "androidx/compose/foundation/ScrollingLayoutNode$measure$1$1.class", "size": 1982, "crc": 955591684}, {"key": "androidx/compose/foundation/ScrollingLayoutNode$measure$1.class", "name": "androidx/compose/foundation/ScrollingLayoutNode$measure$1.class", "size": 2682, "crc": -893780375}, {"key": "androidx/compose/foundation/ScrollingLayoutNode.class", "name": "androidx/compose/foundation/ScrollingLayoutNode.class", "size": 5971, "crc": -900689653}, {"key": "androidx/compose/foundation/SurfaceCoroutineScope.class", "name": "androidx/compose/foundation/SurfaceCoroutineScope.class", "size": 615, "crc": 1085527667}, {"key": "androidx/compose/foundation/SurfaceScope.class", "name": "androidx/compose/foundation/SurfaceScope.class", "size": 1306, "crc": 353967741}, {"key": "androidx/compose/foundation/SystemGestureExclusionKt.class", "name": "androidx/compose/foundation/SystemGestureExclusionKt.class", "size": 3114, "crc": 148415522}, {"key": "androidx/compose/foundation/content/MediaType$Companion.class", "name": "androidx/compose/foundation/content/MediaType$Companion.class", "size": 1793, "crc": 2084894729}, {"key": "androidx/compose/foundation/content/MediaType.class", "name": "androidx/compose/foundation/content/MediaType.class", "size": 2944, "crc": 1044577425}, {"key": "androidx/compose/foundation/content/PlatformTransferableContent.class", "name": "androidx/compose/foundation/content/PlatformTransferableContent.class", "size": 2569, "crc": 386488750}, {"key": "androidx/compose/foundation/content/ReceiveContentElement.class", "name": "androidx/compose/foundation/content/ReceiveContentElement.class", "size": 4297, "crc": -255956328}, {"key": "androidx/compose/foundation/content/ReceiveContentKt.class", "name": "androidx/compose/foundation/content/ReceiveContentKt.class", "size": 1279, "crc": -1901635901}, {"key": "androidx/compose/foundation/content/ReceiveContentListener.class", "name": "androidx/compose/foundation/content/ReceiveContentListener.class", "size": 1352, "crc": -66627569}, {"key": "androidx/compose/foundation/content/ReceiveContentNode$1.class", "name": "androidx/compose/foundation/content/ReceiveContentNode$1.class", "size": 1952, "crc": 93834333}, {"key": "androidx/compose/foundation/content/ReceiveContentNode.class", "name": "androidx/compose/foundation/content/ReceiveContentNode.class", "size": 3688, "crc": 1169171825}, {"key": "androidx/compose/foundation/content/TransferableContent$Source$Companion.class", "name": "androidx/compose/foundation/content/TransferableContent$Source$Companion.class", "size": 1597, "crc": 1532824813}, {"key": "androidx/compose/foundation/content/TransferableContent$Source.class", "name": "androidx/compose/foundation/content/TransferableContent$Source.class", "size": 3265, "crc": -701209399}, {"key": "androidx/compose/foundation/content/TransferableContent.class", "name": "androidx/compose/foundation/content/TransferableContent.class", "size": 3107, "crc": 1784901271}, {"key": "androidx/compose/foundation/content/TransferableContent_androidKt.class", "name": "androidx/compose/foundation/content/TransferableContent_androidKt.class", "size": 6756, "crc": 1358143956}, {"key": "androidx/compose/foundation/content/internal/DragAndDropRequestPermission_androidKt.class", "name": "androidx/compose/foundation/content/internal/DragAndDropRequestPermission_androidKt.class", "size": 3327, "crc": 410320251}, {"key": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration$receiveContentListener$1.class", "name": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration$receiveContentListener$1.class", "size": 3220, "crc": 917795809}, {"key": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration.class", "name": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration.class", "size": 2841, "crc": 1112181084}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration$Companion.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration$Companion.class", "size": 1682, "crc": 1311766814}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration.class", "size": 2179, "crc": 1034310078}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationImpl.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationImpl.class", "size": 2905, "crc": -686330031}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt$ModifierLocalReceiveContent$1.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt$ModifierLocalReceiveContent$1.class", "size": 1487, "crc": 669781260}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt.class", "size": 2699, "crc": 1762546766}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$1.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$1.class", "size": 1979, "crc": 2146854978}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$2.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$2.class", "size": 3816, "crc": -512117339}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt.class", "size": 4155, "crc": 1562979712}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenu$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenu$1.class", "size": 2808, "crc": -1338372623}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenu$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenu$2.class", "size": 2808, "crc": -1733528800}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenuArea$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenuArea$2.class", "size": 3239, "crc": 1396356947}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt.class", "size": 15051, "crc": 1865129269}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuColors.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuColors.class", "size": 3503, "crc": 1229451158}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$awaitFirstRightClickDown$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$awaitFirstRightClickDown$1.class", "size": 1828, "crc": -1719934530}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$1$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$1$1.class", "size": 2038, "crc": -1591789710}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$1.class", "size": 4261, "crc": -1460815973}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$onRightClickDown$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$onRightClickDown$2.class", "size": 4851, "crc": -**********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt.class", "size": 7140, "crc": -389557925}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuKey.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuKey.class", "size": 741, "crc": 224022162}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider.class", "size": 2669, "crc": **********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider_androidKt.class", "size": 2245, "crc": 729172663}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuScope$Content$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuScope$Content$2.class", "size": 2020, "crc": **********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuScope$item$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuScope$item$1.class", "size": 5621, "crc": **********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuScope.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuScope.class", "size": 7280, "crc": -215779498}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuSpec.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuSpec.class", "size": 7090, "crc": -**********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Closed.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Closed.class", "size": 1300, "crc": 1370294177}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Open.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Open.class", "size": 3506, "crc": -2055657749}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState$Status.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState$Status.class", "size": 1420, "crc": 746046121}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState.class", "size": 4450, "crc": 906701025}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState_androidKt.class", "size": 1482, "crc": -709803469}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuColumn$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuColumn$1.class", "size": 2642, "crc": -2011243638}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuItem$1$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuItem$1$1.class", "size": 1680, "crc": -1063864458}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuItem$3.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuItem$3.class", "size": 3047, "crc": 428485298}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$1.class", "size": 2799, "crc": -2047256406}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2$1.class", "size": 5725, "crc": -571237103}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2.class", "size": 4000, "crc": 1024206470}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$3.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$3.class", "size": 3086, "crc": -1216523502}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt.class", "size": 34581, "crc": 1900582552}, {"key": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt.class", "name": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt.class", "size": 1729, "crc": 927163310}, {"key": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback$cachePicture$1$1.class", "name": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback$cachePicture$1$1.class", "size": 6302, "crc": -2103330172}, {"key": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback.class", "name": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback.class", "size": 4379, "crc": -1605549188}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceElement.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceElement.class", "size": 6969, "crc": 1527214902}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt.class", "size": 2041, "crc": -1408378110}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$1$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$1$1.class", "size": 7040, "crc": -1108337304}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$1.class", "size": 4327, "crc": 603333593}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode.class", "size": 5221, "crc": -58935308}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceScope.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceScope.class", "size": 959, "crc": 306374842}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceWithDefaultShadowElement.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceWithDefaultShadowElement.class", "size": 4468, "crc": 1029825293}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetKt.class", "size": 1829, "crc": 1802241320}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode$createAndAttachDragAndDropModifierNode$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode$createAndAttachDragAndDropModifierNode$1.class", "size": 1975, "crc": -1881712864}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode.class", "size": 4263, "crc": 350558382}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$1.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$1.class", "size": 1882, "crc": -1181981984}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$2.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$2.class", "size": 3899, "crc": 613003361}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "size": 1894, "crc": -121621532}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter.class", "size": 3721, "crc": 823101806}, {"key": "androidx/compose/foundation/draganddrop/DropTargetElement.class", "name": "androidx/compose/foundation/draganddrop/DropTargetElement.class", "size": 4471, "crc": -700122945}, {"key": "androidx/compose/foundation/gestures/AnchoredDragFinishedSignal.class", "name": "androidx/compose/foundation/gestures/AnchoredDragFinishedSignal.class", "size": 1863, "crc": 1372328851}, {"key": "androidx/compose/foundation/gestures/AnchoredDragScope.class", "name": "androidx/compose/foundation/gestures/AnchoredDragScope.class", "size": 1062, "crc": 1567393364}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableElement.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableElement.class", "size": 6014, "crc": 1207712977}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AlwaysDrag$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AlwaysDrag$1.class", "size": 1648, "crc": 1017764833}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$2$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$2$2.class", "size": 2108, "crc": 364770484}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$4.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$4.class", "size": 4550, "crc": 1116945388}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$1.class", "size": 1821, "crc": 1976251142}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2$3.class", "size": 3108, "crc": 1299553544}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2.class", "size": 8122, "crc": 429373639}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$1.class", "size": 1721, "crc": 723106637}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$2.class", "size": 4310, "crc": -181308886}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$emit$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$emit$1.class", "size": 2190, "crc": -816646121}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1.class", "size": 4599, "crc": 1177188788}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2.class", "size": 4722, "crc": -752127632}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$snapTo$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$snapTo$2.class", "size": 4055, "crc": -444977604}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt.class", "size": 19491, "crc": 1521241804}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2$1$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2$1$1.class", "size": 3109, "crc": 273208109}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2$1.class", "size": 3818, "crc": -311411578}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2.class", "size": 4873, "crc": -969167228}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1$1.class", "size": 4971, "crc": 374978143}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1.class", "size": 5149, "crc": 1887624967}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode.class", "size": 10232, "crc": -334030470}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$1.class", "size": 1688, "crc": 189995453}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$2.class", "size": 1743, "crc": 2089972673}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$1.class", "size": 1969, "crc": -1729688693}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$2.class", "size": 2420, "crc": -967654924}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$3.class", "size": 3585, "crc": 741927870}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion.class", "size": 4071, "crc": -826103596}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$1.class", "size": 1873, "crc": 1633121972}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$2.class", "size": 4843, "crc": 1173917022}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2.class", "size": 5598, "crc": 1681204205}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$3.class", "size": 2038, "crc": -1471156958}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$1.class", "size": 2091, "crc": -1048046855}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$2.class", "size": 5056, "crc": -1493136354}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4.class", "size": 5804, "crc": -1820012499}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDragScope$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDragScope$1.class", "size": 4868, "crc": 283427975}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$progress$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$progress$2.class", "size": 2376, "crc": 1703516134}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$targetValue$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$targetValue$2.class", "size": 2228, "crc": -2074623822}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState.class", "size": 25642, "crc": -194897237}, {"key": "androidx/compose/foundation/gestures/AndroidConfig.class", "name": "androidx/compose/foundation/gestures/AndroidConfig.class", "size": 4109, "crc": -1811128336}, {"key": "androidx/compose/foundation/gestures/AndroidScrollable_androidKt.class", "name": "androidx/compose/foundation/gestures/AndroidScrollable_androidKt.class", "size": 1106, "crc": -699221717}, {"key": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue$enqueue$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue$enqueue$1.class", "size": 2282, "crc": 1409664706}, {"key": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue.class", "name": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue.class", "size": 9621, "crc": -215323827}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion$DefaultBringIntoViewSpec$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion$DefaultBringIntoViewSpec$1.class", "size": 896, "crc": 995394308}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion.class", "size": 2551, "crc": -337008244}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec.class", "size": 1646, "crc": -109293655}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$LocalBringIntoViewSpec$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$LocalBringIntoViewSpec$1.class", "size": 2821, "crc": 1388424773}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$PivotBringIntoViewSpec$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$PivotBringIntoViewSpec$1.class", "size": 2889, "crc": 1089315979}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt.class", "size": 2292, "crc": -860983586}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$Request.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$Request.class", "size": 4362, "crc": 638957807}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$WhenMappings.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$WhenMappings.class", "size": 874, "crc": 1923690120}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$1.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$1.class", "size": 3928, "crc": 996964198}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$2.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$2.class", "size": 5732, "crc": 845257211}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1.class", "size": 5452, "crc": -175978360}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2.class", "size": 6139, "crc": 1766559162}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode.class", "size": 18330, "crc": 1569871427}, {"key": "androidx/compose/foundation/gestures/ContentInViewNodeKt.class", "name": "androidx/compose/foundation/gestures/ContentInViewNodeKt.class", "size": 675, "crc": 1624562014}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag$2.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag$2.class", "size": 4749, "crc": 953461650}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag2DScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag2DScope$1.class", "size": 1560, "crc": -890175173}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState.class", "size": 4494, "crc": 1215464204}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState$drag$2.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState$drag$2.class", "size": 4719, "crc": -596204315}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState$dragScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState$dragScope$1.class", "size": 1410, "crc": 452715744}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState.class", "size": 4256, "crc": 765017284}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2$1.class", "size": 3050, "crc": -1087008544}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2.class", "size": 5695, "crc": 2119691918}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior.class", "size": 3983, "crc": -1343032889}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2$1.class", "size": 4562, "crc": -1118083129}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2.class", "size": 4995, "crc": -529357382}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scrollScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scrollScope$1.class", "size": 2024, "crc": 12143407}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState.class", "size": 5987, "crc": 242664265}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2$1.class", "size": 4634, "crc": -765469724}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2.class", "size": 5070, "crc": 2095679008}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transformScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transformScope$1.class", "size": 1808, "crc": -153171153}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState.class", "size": 5444, "crc": 693112793}, {"key": "androidx/compose/foundation/gestures/Drag2DScope.class", "name": "androidx/compose/foundation/gestures/Drag2DScope.class", "size": 676, "crc": 448036047}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragCancelled.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragCancelled.class", "size": 1025, "crc": 1193839192}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragDelta.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragDelta.class", "size": 1369, "crc": 1997853296}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragStarted.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragStarted.class", "size": 1385, "crc": 1480879395}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragStopped.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragStopped.class", "size": 1379, "crc": 362222963}, {"key": "androidx/compose/foundation/gestures/DragEvent.class", "name": "androidx/compose/foundation/gestures/DragEvent.class", "size": 1582, "crc": -706222043}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitDragOrCancellation$1.class", "size": 1771, "crc": -759550191}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalDragOrCancellation$1.class", "size": 1811, "crc": -1069892283}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalPointerSlopOrCancellation$1.class", "size": 2116, "crc": -805171750}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalTouchSlopOrCancellation$1.class", "size": 2104, "crc": 1075893231}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$1.class", "size": 1768, "crc": 1059624744}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$2.class", "size": 10993, "crc": -788822385}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitTouchSlopOrCancellation$1.class", "size": 2064, "crc": -868832451}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalDragOrCancellation$1.class", "size": 1803, "crc": 80144448}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalPointerSlopOrCancellation$1.class", "size": 2106, "crc": -100800289}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalTouchSlopOrCancellation$1.class", "size": 2096, "crc": -963809796}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$2.class", "size": 1728, "crc": -1169904557}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$3.class", "size": 1450, "crc": 385952459}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$4.class", "size": 1450, "crc": -655350463}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$5.class", "size": 2533, "crc": 824129049}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$6.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$6.class", "size": 2071, "crc": 841054014}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$7.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$7.class", "size": 1565, "crc": 1598854928}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$9.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$9.class", "size": 19817, "crc": -990062443}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$2.class", "size": 1770, "crc": -1179682411}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$3.class", "size": 1492, "crc": -1405981099}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$4.class", "size": 1492, "crc": 88400963}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5$1.class", "size": 2414, "crc": 432221370}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5.class", "size": 8573, "crc": 878240690}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$2.class", "size": 1758, "crc": 1332611934}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$3.class", "size": 1480, "crc": -2051778081}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$4.class", "size": 1480, "crc": 2008823692}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$1.class", "size": 2404, "crc": 1941501794}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$drag$1.class", "size": 2058, "crc": -1239138736}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5.class", "size": 7096, "crc": -1841468777}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$2.class", "size": 1752, "crc": 1210746361}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$3.class", "size": 1474, "crc": -1892805679}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$4.class", "size": 1474, "crc": 1120204625}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$1.class", "size": 2396, "crc": -557806722}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$drag$1.class", "size": 2052, "crc": -684500234}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5.class", "size": 7064, "crc": -367301759}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$1.class", "size": 1712, "crc": 1345007517}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$2.class", "size": 2387, "crc": -60105700}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$horizontalDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$horizontalDrag$1.class", "size": 1896, "crc": -1119303868}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$verticalDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$verticalDrag$1.class", "size": 1888, "crc": 167269109}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt.class", "size": 72526, "crc": 2074607189}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$_canDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$_canDrag$1.class", "size": 1899, "crc": 81669914}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$1.class", "size": 6908, "crc": -1998686437}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDrag$1.class", "size": 3071, "crc": -247409618}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragCancel$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragCancel$1.class", "size": 2039, "crc": 678450928}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragEnd$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragEnd$1.class", "size": 3819, "crc": -1192237901}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragStart$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragStart$1.class", "size": 3885, "crc": 672977192}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$shouldAwaitTouchSlop$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$shouldAwaitTouchSlop$1.class", "size": 1652, "crc": 627889585}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1.class", "size": 5715, "crc": 1205589576}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$processDragCancel$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$processDragCancel$1.class", "size": 1956, "crc": 1769748677}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$processDragStart$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$processDragStart$1.class", "size": 2304, "crc": 943697873}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$processDragStop$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$processDragStop$1.class", "size": 2236, "crc": -2056352184}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1$1.class", "size": 5266, "crc": 747860931}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1.class", "size": 6792, "crc": -1365243878}, {"key": "androidx/compose/foundation/gestures/DragGestureNode.class", "name": "androidx/compose/foundation/gestures/DragGestureNode.class", "size": 17047, "crc": -1678481269}, {"key": "androidx/compose/foundation/gestures/DragScope.class", "name": "androidx/compose/foundation/gestures/DragScope.class", "size": 486, "crc": -433894205}, {"key": "androidx/compose/foundation/gestures/Draggable2DCompatElement$Companion$CanDrag$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DCompatElement$Companion$CanDrag$1.class", "size": 1671, "crc": -1877936275}, {"key": "androidx/compose/foundation/gestures/Draggable2DCompatElement$Companion.class", "name": "androidx/compose/foundation/gestures/Draggable2DCompatElement$Companion.class", "size": 1404, "crc": 407643368}, {"key": "androidx/compose/foundation/gestures/Draggable2DCompatElement.class", "name": "androidx/compose/foundation/gestures/Draggable2DCompatElement.class", "size": 7885, "crc": 1899700731}, {"key": "androidx/compose/foundation/gestures/Draggable2DElement$Companion$CanDrag$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DElement$Companion$CanDrag$1.class", "size": 1653, "crc": -963901842}, {"key": "androidx/compose/foundation/gestures/Draggable2DElement$Companion.class", "name": "androidx/compose/foundation/gestures/Draggable2DElement$Companion.class", "size": 1386, "crc": -450140356}, {"key": "androidx/compose/foundation/gestures/Draggable2DElement.class", "name": "androidx/compose/foundation/gestures/Draggable2DElement.class", "size": 7221, "crc": 769094303}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStart$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStart$1.class", "size": 1401, "crc": 918599999}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStarted$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStarted$1.class", "size": 3044, "crc": 1432865684}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStop$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStop$1.class", "size": 1393, "crc": 734659628}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStopped$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStopped$1.class", "size": 3038, "crc": 1846098308}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$rememberDraggable2DState$1$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$rememberDraggable2DState$1$1.class", "size": 2062, "crc": 1084242286}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt.class", "size": 10772, "crc": 1205768198}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2$1.class", "size": 2203, "crc": 926764399}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2.class", "size": 4525, "crc": -1074938026}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode$onDragStarted$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode$onDragStarted$1.class", "size": 3959, "crc": 1388421471}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode$onDragStopped$1.class", "size": 4059, "crc": -1826237870}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode.class", "size": 12207, "crc": 655596773}, {"key": "androidx/compose/foundation/gestures/Draggable2DState.class", "name": "androidx/compose/foundation/gestures/Draggable2DState.class", "size": 2232, "crc": -760261387}, {"key": "androidx/compose/foundation/gestures/DraggableAnchors.class", "name": "androidx/compose/foundation/gestures/DraggableAnchors.class", "size": 1668, "crc": 1393577949}, {"key": "androidx/compose/foundation/gestures/DraggableAnchorsConfig.class", "name": "androidx/compose/foundation/gestures/DraggableAnchorsConfig.class", "size": 1752, "crc": 1692078817}, {"key": "androidx/compose/foundation/gestures/DraggableElement$Companion$CanDrag$1.class", "name": "androidx/compose/foundation/gestures/DraggableElement$Companion$CanDrag$1.class", "size": 1645, "crc": 820710257}, {"key": "androidx/compose/foundation/gestures/DraggableElement$Companion.class", "name": "androidx/compose/foundation/gestures/DraggableElement$Companion.class", "size": 1378, "crc": 852878046}, {"key": "androidx/compose/foundation/gestures/DraggableElement.class", "name": "androidx/compose/foundation/gestures/DraggableElement.class", "size": 7686, "crc": 146310833}, {"key": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStarted$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStarted$1.class", "size": 3032, "crc": -1190106668}, {"key": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStopped$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStopped$1.class", "size": 2935, "crc": 28649048}, {"key": "androidx/compose/foundation/gestures/DraggableKt$rememberDraggableState$1$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$rememberDraggableState$1$1.class", "size": 1904, "crc": 147595118}, {"key": "androidx/compose/foundation/gestures/DraggableKt.class", "name": "androidx/compose/foundation/gestures/DraggableKt.class", "size": 9942, "crc": -1407207336}, {"key": "androidx/compose/foundation/gestures/DraggableNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/DraggableNode$drag$2$1.class", "size": 2472, "crc": 1397324601}, {"key": "androidx/compose/foundation/gestures/DraggableNode$drag$2.class", "name": "androidx/compose/foundation/gestures/DraggableNode$drag$2.class", "size": 4491, "crc": -35969385}, {"key": "androidx/compose/foundation/gestures/DraggableNode$onDragStarted$1.class", "name": "androidx/compose/foundation/gestures/DraggableNode$onDragStarted$1.class", "size": 3939, "crc": 529360826}, {"key": "androidx/compose/foundation/gestures/DraggableNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/DraggableNode$onDragStopped$1.class", "size": 4322, "crc": 1441194589}, {"key": "androidx/compose/foundation/gestures/DraggableNode.class", "name": "androidx/compose/foundation/gestures/DraggableNode.class", "size": 9320, "crc": 755373036}, {"key": "androidx/compose/foundation/gestures/DraggableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/DraggableState$DefaultImpls.class", "size": 685, "crc": 1399862667}, {"key": "androidx/compose/foundation/gestures/DraggableState.class", "name": "androidx/compose/foundation/gestures/DraggableState.class", "size": 2173, "crc": 134850973}, {"key": "androidx/compose/foundation/gestures/FlingBehavior.class", "name": "androidx/compose/foundation/gestures/FlingBehavior.class", "size": 1051, "crc": -1167153858}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$2.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$2.class", "size": 3590, "crc": 1541647111}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$3.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$3.class", "size": 1666, "crc": 1022330374}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitEachGesture$2.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitEachGesture$2.class", "size": 4962, "crc": 2141287905}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$forEachGesture$1.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$forEachGesture$1.class", "size": 1815, "crc": 374180803}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt.class", "size": 9537, "crc": -25241836}, {"key": "androidx/compose/foundation/gestures/GestureCancellationException.class", "name": "androidx/compose/foundation/gestures/GestureCancellationException.class", "size": 1297, "crc": 1982569541}, {"key": "androidx/compose/foundation/gestures/MapDraggableAnchors.class", "name": "androidx/compose/foundation/gestures/MapDraggableAnchors.class", "size": 9413, "crc": -1884021849}, {"key": "androidx/compose/foundation/gestures/NestedScrollScope.class", "name": "androidx/compose/foundation/gestures/NestedScrollScope.class", "size": 786, "crc": -1176590293}, {"key": "androidx/compose/foundation/gestures/Orientation.class", "name": "androidx/compose/foundation/gestures/Orientation.class", "size": 1454, "crc": 366668660}, {"key": "androidx/compose/foundation/gestures/PressGestureScope$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/PressGestureScope$DefaultImpls.class", "size": 3551, "crc": 2063149455}, {"key": "androidx/compose/foundation/gestures/PressGestureScope.class", "name": "androidx/compose/foundation/gestures/PressGestureScope.class", "size": 3573, "crc": 58449426}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$awaitRelease$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$awaitRelease$1.class", "size": 1829, "crc": 1820260649}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$reset$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$reset$1.class", "size": 1824, "crc": 927675584}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$tryAwaitRelease$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$tryAwaitRelease$1.class", "size": 1864, "crc": -1830334123}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl.class", "size": 7517, "crc": 1748277917}, {"key": "androidx/compose/foundation/gestures/ScrollConfig.class", "name": "androidx/compose/foundation/gestures/ScrollConfig.class", "size": 960, "crc": -1007127235}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$1.class", "size": 1701, "crc": 608429192}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2$1.class", "size": 1939, "crc": -1674227060}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2.class", "size": 4597, "crc": 203709669}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$1.class", "size": 1620, "crc": 1734718849}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$2.class", "size": 3739, "crc": -957754157}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$2.class", "size": 3378, "crc": -293877511}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt.class", "size": 5920, "crc": -912955328}, {"key": "androidx/compose/foundation/gestures/ScrollScope.class", "name": "androidx/compose/foundation/gestures/ScrollScope.class", "size": 489, "crc": -1294474486}, {"key": "androidx/compose/foundation/gestures/ScrollableContainerNode$TraverseKey.class", "name": "androidx/compose/foundation/gestures/ScrollableContainerNode$TraverseKey.class", "size": 896, "crc": -1160054177}, {"key": "androidx/compose/foundation/gestures/ScrollableContainerNode.class", "name": "androidx/compose/foundation/gestures/ScrollableContainerNode.class", "size": 1885, "crc": 691026988}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaults.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaults.class", "size": 6348, "crc": -1704495568}, {"key": "androidx/compose/foundation/gestures/ScrollableElement.class", "name": "androidx/compose/foundation/gestures/ScrollableElement.class", "size": 7034, "crc": 542645871}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$CanDragCalculation$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$CanDragCalculation$1.class", "size": 1985, "crc": -732467066}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$DefaultScrollMotionDurationScale$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$DefaultScrollMotionDurationScale$1.class", "size": 3125, "crc": -600151405}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$NoOpScrollScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$NoOpScrollScope$1.class", "size": 931, "crc": -125581204}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$UnityDensity$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$UnityDensity$1.class", "size": 1005, "crc": 197045812}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$1.class", "size": 1735, "crc": 778374705}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2$1.class", "size": 2679, "crc": 1035600793}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2.class", "size": 4664, "crc": -366768181}, {"key": "androidx/compose/foundation/gestures/ScrollableKt.class", "name": "androidx/compose/foundation/gestures/ScrollableKt.class", "size": 9010, "crc": 309763727}, {"key": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection$onPostFling$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection$onPostFling$1.class", "size": 1956, "crc": -427306658}, {"key": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection.class", "name": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection.class", "size": 4158, "crc": -28002736}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$1.class", "size": 2161, "crc": 1163581013}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1$1.class", "size": 2472, "crc": 1141195686}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1.class", "size": 4566, "crc": 1951509917}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onDragStopped$1.class", "size": 3815, "crc": 55311724}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1.class", "size": 3710, "crc": 873139046}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1.class", "size": 4146, "crc": -416133097}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$processMouseWheelEvent$2$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$processMouseWheelEvent$2$1$1.class", "size": 3780, "crc": 525827223}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$processMouseWheelEvent$2$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$processMouseWheelEvent$2$1.class", "size": 4254, "crc": -2079907992}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1$1.class", "size": 4135, "crc": 1299111183}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1.class", "size": 2206, "crc": -1433688099}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$2.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$2.class", "size": 4032, "crc": -377980686}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$updateDefaultFlingBehavior$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$updateDefaultFlingBehavior$1.class", "size": 2370, "crc": 1161736174}, {"key": "androidx/compose/foundation/gestures/ScrollableNode.class", "name": "androidx/compose/foundation/gestures/ScrollableNode.class", "size": 23084, "crc": 2110627876}, {"key": "androidx/compose/foundation/gestures/ScrollableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/ScrollableState$DefaultImpls.class", "size": 1597, "crc": -211196989}, {"key": "androidx/compose/foundation/gestures/ScrollableState.class", "name": "androidx/compose/foundation/gestures/ScrollableState.class", "size": 3317, "crc": -445541322}, {"key": "androidx/compose/foundation/gestures/ScrollableStateKt$rememberScrollableState$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableStateKt$rememberScrollableState$1$1.class", "size": 1957, "crc": 116679536}, {"key": "androidx/compose/foundation/gestures/ScrollableStateKt.class", "name": "androidx/compose/foundation/gestures/ScrollableStateKt.class", "size": 4450, "crc": 1911557766}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$1.class", "size": 1814, "crc": 1940877766}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$reverseScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$reverseScope$1.class", "size": 2051, "crc": -1210384021}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2.class", "size": 5719, "crc": 1268951422}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$nestedScrollScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$nestedScrollScope$1.class", "size": 3378, "crc": 1962829716}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$onDragStopped$performFling$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$onDragStopped$performFling$1.class", "size": 4794, "crc": 1378351943}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$performScrollForOverscroll$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$performScrollForOverscroll$1.class", "size": 2435, "crc": 1464456802}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$scroll$2.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$scroll$2.class", "size": 4550, "crc": 247518778}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic.class", "size": 15533, "crc": -1166465546}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$NoPressGesture$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$NoPressGesture$1.class", "size": 3179, "crc": -777316503}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitFirstDown$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitFirstDown$2.class", "size": 1802, "crc": 488381669}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitSecondDown$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitSecondDown$2.class", "size": 4697, "crc": -552532910}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$consumeUntilUp$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$consumeUntilUp$1.class", "size": 1699, "crc": -556834551}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$1.class", "size": 3761, "crc": -1860561541}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$2.class", "size": 4688, "crc": -1570790772}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$3.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$3.class", "size": 3643, "crc": 875903059}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$4.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$4.class", "size": 3644, "crc": -1435202014}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1.class", "size": 7716, "crc": -1889236860}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2.class", "size": 5357, "crc": 414728506}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$1.class", "size": 3761, "crc": -1874218879}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$10.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$10.class", "size": 3648, "crc": 790113240}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$2.class", "size": 4688, "crc": -1889776436}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$3.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$3.class", "size": 4020, "crc": 803760244}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$4.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$4.class", "size": 3643, "crc": -1987469371}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$5.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$5.class", "size": 3644, "crc": 703489138}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$6.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$6.class", "size": 3644, "crc": 5698670}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$7.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$7.class", "size": 3761, "crc": -1865118222}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$8.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$8.class", "size": 4694, "crc": -774504881}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9$1.class", "size": 3747, "crc": -1272124644}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9$2.class", "size": 3746, "crc": -1322190499}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9.class", "size": 6290, "crc": 1332392298}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1.class", "size": 10766, "crc": -601858681}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2.class", "size": 5873, "crc": 802288505}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForUpOrCancellation$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForUpOrCancellation$2.class", "size": 1824, "crc": 851772179}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt.class", "size": 18225, "crc": 323363483}, {"key": "androidx/compose/foundation/gestures/TargetedFlingBehavior.class", "name": "androidx/compose/foundation/gestures/TargetedFlingBehavior.class", "size": 2560, "crc": -1725625076}, {"key": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt$NoOnReport$1.class", "name": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt$NoOnReport$1.class", "size": 1329, "crc": -427014807}, {"key": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt.class", "name": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt.class", "size": 1098, "crc": -1953826791}, {"key": "androidx/compose/foundation/gestures/TouchSlopDetector.class", "name": "androidx/compose/foundation/gestures/TouchSlopDetector.class", "size": 4024, "crc": 485962510}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformDelta.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformDelta.class", "size": 1810, "crc": 213064140}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformStarted.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformStarted.class", "size": 1058, "crc": 372971666}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformStopped.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformStopped.class", "size": 1058, "crc": -1153256848}, {"key": "androidx/compose/foundation/gestures/TransformEvent.class", "name": "androidx/compose/foundation/gestures/TransformEvent.class", "size": 1328, "crc": 546933377}, {"key": "androidx/compose/foundation/gestures/TransformGestureDetectorKt$detectTransformGestures$2.class", "name": "androidx/compose/foundation/gestures/TransformGestureDetectorKt$detectTransformGestures$2.class", "size": 12037, "crc": 1188519758}, {"key": "androidx/compose/foundation/gestures/TransformGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/TransformGestureDetectorKt.class", "size": 9476, "crc": -1987691504}, {"key": "androidx/compose/foundation/gestures/TransformScope$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/TransformScope$DefaultImpls.class", "size": 588, "crc": 1665136784}, {"key": "androidx/compose/foundation/gestures/TransformScope.class", "name": "androidx/compose/foundation/gestures/TransformScope.class", "size": 1477, "crc": -373435282}, {"key": "androidx/compose/foundation/gestures/TransformableElement.class", "name": "androidx/compose/foundation/gestures/TransformableElement.class", "size": 4258, "crc": 1810756269}, {"key": "androidx/compose/foundation/gestures/TransformableKt$detectZoom$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$detectZoom$1.class", "size": 2306, "crc": 1821738948}, {"key": "androidx/compose/foundation/gestures/TransformableKt$transformable$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$transformable$1.class", "size": 1680, "crc": -854331944}, {"key": "androidx/compose/foundation/gestures/TransformableKt.class", "name": "androidx/compose/foundation/gestures/TransformableKt.class", "size": 15350, "crc": -1418495159}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1$1.class", "size": 5465, "crc": -1994122285}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1.class", "size": 5565, "crc": 446016535}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$2.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$2.class", "size": 5443, "crc": 1087571471}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1.class", "size": 4860, "crc": -634348075}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1.class", "size": 4313, "crc": 1572158104}, {"key": "androidx/compose/foundation/gestures/TransformableNode$updatedCanPan$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$updatedCanPan$1.class", "size": 1893, "crc": 692119046}, {"key": "androidx/compose/foundation/gestures/TransformableNode.class", "name": "androidx/compose/foundation/gestures/TransformableNode.class", "size": 4895, "crc": -1191545521}, {"key": "androidx/compose/foundation/gestures/TransformableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/TransformableState$DefaultImpls.class", "size": 711, "crc": -775838245}, {"key": "androidx/compose/foundation/gestures/TransformableState.class", "name": "androidx/compose/foundation/gestures/TransformableState.class", "size": 2228, "crc": 320793560}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2$1.class", "size": 2660, "crc": 1760516858}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2.class", "size": 5486, "crc": 1359415728}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2$1.class", "size": 2538, "crc": 383830624}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2.class", "size": 5028, "crc": -1481081016}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3$1.class", "size": 2593, "crc": -1967825283}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3.class", "size": 5019, "crc": -1572747165}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$panBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$panBy$2.class", "size": 3525, "crc": 493947770}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$rememberTransformableState$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$rememberTransformableState$1$1.class", "size": 2470, "crc": -182979771}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$rotateBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$rotateBy$2.class", "size": 3744, "crc": 1966445364}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$stopTransformation$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$stopTransformation$2.class", "size": 3448, "crc": -505836622}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$zoomBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$zoomBy$2.class", "size": 3737, "crc": 1694497556}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt.class", "size": 12332, "crc": -1416644116}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$Companion.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$Companion.class", "size": 1540, "crc": -997892259}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$1.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$1.class", "size": 2085, "crc": -1690823926}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$4.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$4.class", "size": 4336, "crc": -1964749964}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$5.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$5.class", "size": 2104, "crc": 1266870831}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState.class", "size": 8918, "crc": -1972225889}, {"key": "androidx/compose/foundation/gestures/snapping/AnimationResult.class", "name": "androidx/compose/foundation/gestures/snapping/AnimationResult.class", "size": 2027, "crc": 1208049125}, {"key": "androidx/compose/foundation/gestures/snapping/ApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/ApproachAnimation.class", "size": 1685, "crc": -1882358589}, {"key": "androidx/compose/foundation/gestures/snapping/DecayApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/DecayApproachAnimation.class", "size": 4212, "crc": -946421958}, {"key": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem$Companion.class", "name": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem$Companion.class", "size": 1543, "crc": 544387418}, {"key": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem.class", "name": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem.class", "size": 2920, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 6630, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt.class", "size": 7283, "crc": 943345990}, {"key": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 6245, "crc": 57055950}, {"key": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt.class", "size": 7352, "crc": 202400358}, {"key": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 9780, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt.class", "size": 7297, "crc": 14225348}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$1.class", "size": 2204, "crc": 653898181}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$4.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$4.class", "size": 1987, "crc": 49048239}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$animationState$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$animationState$1.class", "size": 2017, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1.class", "size": 9186, "crc": -733981168}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$1.class", "size": 1954, "crc": 1196297172}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$tryApproach$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$tryApproach$1.class", "size": 2182, "crc": 1713110248}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior.class", "size": 14157, "crc": -41399990}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$1.class", "size": 1932, "crc": -416292014}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$2.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$2.class", "size": 3500, "crc": -1299774493}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$1.class", "size": 1997, "crc": 167307712}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$2.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$2.class", "size": 3458, "crc": -406151592}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt.class", "size": 18986, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapLayoutInfoProvider.class", "name": "androidx/compose/foundation/gestures/snapping/SnapLayoutInfoProvider.class", "size": 764, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition$Center.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition$Center.class", "size": 1531, "crc": 929636079}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition$End.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition$End.class", "size": 1518, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition$Start.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition$Start.class", "size": 1476, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition.class", "size": 1021, "crc": -508027325}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPositionKt.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPositionKt.class", "size": 1437, "crc": -42259420}, {"key": "androidx/compose/foundation/gestures/snapping/TargetApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/TargetApproachAnimation.class", "size": 4328, "crc": -432245282}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Cancel.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Cancel.class", "size": 1475, "crc": 124627009}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Start.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Start.class", "size": 911, "crc": 1273615433}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Stop.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Stop.class", "size": 1469, "crc": 487504463}, {"key": "androidx/compose/foundation/interaction/DragInteraction.class", "name": "androidx/compose/foundation/interaction/DragInteraction.class", "size": 831, "crc": 544097825}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1$1.class", "size": 3409, "crc": -1263747935}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1.class", "size": 4752, "crc": 333396828}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt.class", "size": 4893, "crc": -1480346065}, {"key": "androidx/compose/foundation/interaction/FocusInteraction$Focus.class", "name": "androidx/compose/foundation/interaction/FocusInteraction$Focus.class", "size": 916, "crc": 69191497}, {"key": "androidx/compose/foundation/interaction/FocusInteraction$Unfocus.class", "name": "androidx/compose/foundation/interaction/FocusInteraction$Unfocus.class", "size": 1487, "crc": 1743268778}, {"key": "androidx/compose/foundation/interaction/FocusInteraction.class", "name": "androidx/compose/foundation/interaction/FocusInteraction.class", "size": 753, "crc": 503779535}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1$1.class", "size": 3304, "crc": -902746663}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1.class", "size": 4762, "crc": -2122649114}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt.class", "size": 4907, "crc": 188202730}, {"key": "androidx/compose/foundation/interaction/HoverInteraction$Enter.class", "name": "androidx/compose/foundation/interaction/HoverInteraction$Enter.class", "size": 916, "crc": -1681335327}, {"key": "androidx/compose/foundation/interaction/HoverInteraction$Exit.class", "name": "androidx/compose/foundation/interaction/HoverInteraction$Exit.class", "size": 1478, "crc": -274023303}, {"key": "androidx/compose/foundation/interaction/HoverInteraction.class", "name": "androidx/compose/foundation/interaction/HoverInteraction.class", "size": 747, "crc": -1207777466}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1$1.class", "size": 3298, "crc": 1087413719}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1.class", "size": 4762, "crc": 195535250}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt.class", "size": 4907, "crc": 695230918}, {"key": "androidx/compose/foundation/interaction/Interaction.class", "name": "androidx/compose/foundation/interaction/Interaction.class", "size": 421, "crc": 1008777738}, {"key": "androidx/compose/foundation/interaction/InteractionSource.class", "name": "androidx/compose/foundation/interaction/InteractionSource.class", "size": 897, "crc": 669095953}, {"key": "androidx/compose/foundation/interaction/InteractionSourceKt.class", "name": "androidx/compose/foundation/interaction/InteractionSourceKt.class", "size": 850, "crc": -773399264}, {"key": "androidx/compose/foundation/interaction/MutableInteractionSource.class", "name": "androidx/compose/foundation/interaction/MutableInteractionSource.class", "size": 1318, "crc": -54852513}, {"key": "androidx/compose/foundation/interaction/MutableInteractionSourceImpl.class", "name": "androidx/compose/foundation/interaction/MutableInteractionSourceImpl.class", "size": 3025, "crc": 1425643249}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Cancel.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Cancel.class", "size": 1484, "crc": -1050996633}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Press.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Press.class", "size": 1391, "crc": 2049772587}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Release.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Release.class", "size": 1487, "crc": -599226296}, {"key": "androidx/compose/foundation/interaction/PressInteraction.class", "name": "androidx/compose/foundation/interaction/PressInteraction.class", "size": 843, "crc": -418253205}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1$1.class", "size": 3427, "crc": -2065083571}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1.class", "size": 4762, "crc": -1059950307}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt.class", "size": 4915, "crc": -1844159214}, {"key": "androidx/compose/foundation/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/foundation/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 566, "crc": -2034863898}, {"key": "androidx/compose/foundation/lazy/DefaultLazyListPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/DefaultLazyListPrefetchStrategy.class", "size": 4785, "crc": 1273385697}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$1.class", "size": 3796, "crc": 918473189}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$2.class", "size": 3734, "crc": -1063965043}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$1.class", "size": 3787, "crc": 1306188420}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$2.class", "size": 3725, "crc": -1252155378}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$1.class", "size": 1746, "crc": 615753844}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$2.class", "size": 2199, "crc": -1573009169}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$3.class", "size": 2210, "crc": 129975780}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$4.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$4.class", "size": 3964, "crc": -1839961030}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$5.class", "size": 1749, "crc": -1213409572}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$6.class", "size": 2133, "crc": 159412091}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$7.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$7.class", "size": 2144, "crc": -207185766}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$8.class", "size": 3875, "crc": -2037304280}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$1.class", "size": 2423, "crc": -2133117072}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$2.class", "size": 2395, "crc": 413328271}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$1.class", "size": 1931, "crc": 1661662187}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$2.class", "size": 2440, "crc": -431524955}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$3.class", "size": 2451, "crc": 79896662}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$4.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$4.class", "size": 4204, "crc": 403538979}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$5.class", "size": 1934, "crc": -458386665}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$6.class", "size": 2374, "crc": 77077853}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$7.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$7.class", "size": 2385, "crc": -1253727796}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$8.class", "size": 4115, "crc": 1898140844}, {"key": "androidx/compose/foundation/lazy/LazyDslKt.class", "name": "androidx/compose/foundation/lazy/LazyDslKt.class", "size": 33283, "crc": -1982051457}, {"key": "androidx/compose/foundation/lazy/LazyItemScope$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyItemScope$DefaultImpls.class", "size": 3651, "crc": -1121964546}, {"key": "androidx/compose/foundation/lazy/LazyItemScope.class", "name": "androidx/compose/foundation/lazy/LazyItemScope.class", "size": 6410, "crc": -342058521}, {"key": "androidx/compose/foundation/lazy/LazyItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/LazyItemScopeImpl.class", "size": 4217, "crc": -1952616960}, {"key": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "name": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "size": 4040, "crc": 48126928}, {"key": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt.class", "name": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt.class", "size": 1229, "crc": -446974404}, {"key": "androidx/compose/foundation/lazy/LazyListAnimateScrollScope.class", "name": "androidx/compose/foundation/lazy/LazyListAnimateScrollScope.class", "size": 7180, "crc": -385587730}, {"key": "androidx/compose/foundation/lazy/LazyListBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/LazyListBeyondBoundsModifierKt.class", "size": 3932, "crc": -1222145454}, {"key": "androidx/compose/foundation/lazy/LazyListBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/LazyListBeyondBoundsState.class", "size": 3106, "crc": 1068924529}, {"key": "androidx/compose/foundation/lazy/LazyListHeadersKt.class", "name": "androidx/compose/foundation/lazy/LazyListHeadersKt.class", "size": 4745, "crc": 1906387835}, {"key": "androidx/compose/foundation/lazy/LazyListInterval.class", "name": "androidx/compose/foundation/lazy/LazyListInterval.class", "size": 3327, "crc": 1525696912}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$1.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$1.class", "size": 1432, "crc": -852152874}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$2.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$2.class", "size": 1436, "crc": 155946549}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$3.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$3.class", "size": 3451, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent.class", "size": 6542, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemInfo.class", "name": "androidx/compose/foundation/lazy/LazyListItemInfo.class", "size": 990, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProvider.class", "name": "androidx/compose/foundation/lazy/LazyListItemProvider.class", "size": 1329, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$1.class", "size": 4897, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$2.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$2.class", "size": 1898, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl.class", "size": 6451, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$1.class", "size": 1320, "crc": -64421505}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$intervalContentState$1.class", "size": 2163, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$itemProviderState$1.class", "size": 3373, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt.class", "size": 5533, "crc": -275409157}, {"key": "androidx/compose/foundation/lazy/LazyListKt$LazyList$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$LazyList$1.class", "size": 4619, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measureResult$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measureResult$1.class", "size": 3253, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measuredItemProvider$1.class", "size": 4473, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1.class", "size": 15021, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListKt.class", "name": "androidx/compose/foundation/lazy/LazyListKt.class", "size": 20421, "crc": 215795261}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfo$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfo$DefaultImpls.class", "size": 1887, "crc": 926350357}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfo.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfo.class", "size": 3406, "crc": 842021838}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$3.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$3.class", "size": 2439, "crc": -1885766317}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$8.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$8.class", "size": 4780, "crc": -1548073776}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt.class", "size": 29547, "crc": 1023210931}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureResult.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureResult.class", "size": 10770, "crc": 436040497}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItem.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItem.class", "size": 16562, "crc": 404137675}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItemKt.class", "size": 422, "crc": 432183704}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItemProvider.class", "size": 5393, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchScope.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchScope.class", "size": 1039, "crc": -658825160}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchStrategy.class", "size": 1806, "crc": -737079669}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchStrategyKt.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchStrategyKt.class", "size": 1207, "crc": -309802709}, {"key": "androidx/compose/foundation/lazy/LazyListScope$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyListScope$DefaultImpls.class", "size": 3666, "crc": 716920502}, {"key": "androidx/compose/foundation/lazy/LazyListScope$items$1.class", "name": "androidx/compose/foundation/lazy/LazyListScope$items$1.class", "size": 1390, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListScope$items$2.class", "name": "androidx/compose/foundation/lazy/LazyListScope$items$2.class", "size": 1408, "crc": -927814385}, {"key": "androidx/compose/foundation/lazy/LazyListScope.class", "name": "androidx/compose/foundation/lazy/LazyListScope.class", "size": 6305, "crc": -567974880}, {"key": "androidx/compose/foundation/lazy/LazyListScrollPosition.class", "name": "androidx/compose/foundation/lazy/LazyListScrollPosition.class", "size": 7067, "crc": 1830732395}, {"key": "androidx/compose/foundation/lazy/LazyListScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/LazyListScrollPositionKt.class", "size": 523, "crc": -1333513848}, {"key": "androidx/compose/foundation/lazy/LazyListSemanticsKt.class", "name": "androidx/compose/foundation/lazy/LazyListSemanticsKt.class", "size": 3791, "crc": -1695265596}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$1.class", "size": 2197, "crc": 798030996}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$2.class", "size": 1829, "crc": 1178524852}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$saver$3.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$saver$3.class", "size": 2416, "crc": -241702887}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$saver$4.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$saver$4.class", "size": 2202, "crc": 1964950559}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion.class", "size": 2589, "crc": -770164378}, {"key": "androidx/compose/foundation/lazy/LazyListState$prefetchScope$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$prefetchScope$1.class", "size": 4315, "crc": 1562305079}, {"key": "androidx/compose/foundation/lazy/LazyListState$prefetchState$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$prefetchState$1.class", "size": 4371, "crc": 485654833}, {"key": "androidx/compose/foundation/lazy/LazyListState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$remeasurementModifier$1.class", "size": 1517, "crc": -1554782349}, {"key": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1$1.class", "size": 3233, "crc": 302397934}, {"key": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1.class", "size": 3879, "crc": 1705817611}, {"key": "androidx/compose/foundation/lazy/LazyListState$scroll$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$scroll$1.class", "size": 1884, "crc": 1731552814}, {"key": "androidx/compose/foundation/lazy/LazyListState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$scrollToItem$2.class", "size": 3637, "crc": 1478096202}, {"key": "androidx/compose/foundation/lazy/LazyListState$scrollableState$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$scrollableState$1.class", "size": 1578, "crc": -1382999921}, {"key": "androidx/compose/foundation/lazy/LazyListState$updateScrollDeltaForPostLookahead$2$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$updateScrollDeltaForPostLookahead$2$1.class", "size": 4404, "crc": 532169249}, {"key": "androidx/compose/foundation/lazy/LazyListState$updateScrollDeltaForPostLookahead$2$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$updateScrollDeltaForPostLookahead$2$2.class", "size": 4404, "crc": -1284758772}, {"key": "androidx/compose/foundation/lazy/LazyListState.class", "name": "androidx/compose/foundation/lazy/LazyListState.class", "size": 30865, "crc": -862409718}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt$EmptyLazyListMeasureResult$1.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt$EmptyLazyListMeasureResult$1.class", "size": 1883, "crc": 1488406152}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$1$1.class", "size": 1571, "crc": -1712360952}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$3$1.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$3$1.class", "size": 1812, "crc": 1450742255}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt.class", "size": 9441, "crc": 866484395}, {"key": "androidx/compose/foundation/lazy/LazyScopeMarker.class", "name": "androidx/compose/foundation/lazy/LazyScopeMarker.class", "size": 611, "crc": -401847391}, {"key": "androidx/compose/foundation/lazy/ParentSizeElement.class", "name": "androidx/compose/foundation/lazy/ParentSizeElement.class", "size": 4801, "crc": 1245164174}, {"key": "androidx/compose/foundation/lazy/ParentSizeNode$measure$1.class", "name": "androidx/compose/foundation/lazy/ParentSizeNode$measure$1.class", "size": 1942, "crc": 1925372398}, {"key": "androidx/compose/foundation/lazy/ParentSizeNode.class", "name": "androidx/compose/foundation/lazy/ParentSizeNode.class", "size": 6017, "crc": -417402186}, {"key": "androidx/compose/foundation/lazy/grid/DefaultLazyGridPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/grid/DefaultLazyGridPrefetchStrategy.class", "size": 9392, "crc": -1173470458}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$Adaptive.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$Adaptive.class", "size": 4035, "crc": 377781550}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$Fixed.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$Fixed.class", "size": 2944, "crc": -1326075377}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$FixedSize.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$FixedSize.class", "size": 4410, "crc": 1022193254}, {"key": "androidx/compose/foundation/lazy/grid/GridCells.class", "name": "androidx/compose/foundation/lazy/grid/GridCells.class", "size": 1199, "crc": -811209345}, {"key": "androidx/compose/foundation/lazy/grid/GridItemSpan.class", "name": "androidx/compose/foundation/lazy/grid/GridItemSpan.class", "size": 2339, "crc": 1931504447}, {"key": "androidx/compose/foundation/lazy/grid/GridSlotCache.class", "name": "androidx/compose/foundation/lazy/grid/GridSlotCache.class", "size": 3107, "crc": -542052624}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridAnimateScrollScope$calculateLineAverageMainAxisSize$lineOf$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridAnimateScrollScope$calculateLineAverageMainAxisSize$lineOf$1.class", "size": 2066, "crc": -1177048906}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridAnimateScrollScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridAnimateScrollScope.class", "size": 8285, "crc": -2137285255}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsModifierKt.class", "size": 3833, "crc": -1836151872}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsState.class", "size": 2855, "crc": -829744732}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyHorizontalGrid$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyHorizontalGrid$1.class", "size": 4178, "crc": -52184082}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyVerticalGrid$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyVerticalGrid$1.class", "size": 4175, "crc": 1901137562}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$1.class", "size": 1848, "crc": -695926484}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$10.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$10.class", "size": 4048, "crc": -1125247789}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$2.class", "size": 2301, "crc": 434260360}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$3.class", "size": 3147, "crc": 1437711684}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$4.class", "size": 2312, "crc": 832026808}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$5.class", "size": 4134, "crc": -934265451}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$6.class", "size": 1851, "crc": -758048834}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$7.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$7.class", "size": 2235, "crc": -231849480}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$8.class", "size": 3057, "crc": -2074968116}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$9.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$9.class", "size": 2246, "crc": 457160176}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$1.class", "size": 2033, "crc": -260696828}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$10.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$10.class", "size": 4288, "crc": -1835303990}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$2.class", "size": 2542, "crc": 716481986}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$3.class", "size": 3406, "crc": -70502761}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$4.class", "size": 2553, "crc": 348990936}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$5.class", "size": 4374, "crc": 383832851}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$6.class", "size": 2036, "crc": 373207503}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$7.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$7.class", "size": 2476, "crc": -1051517997}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$8.class", "size": 3316, "crc": 787704517}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$9.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$9.class", "size": 2487, "crc": 1882227036}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberColumnWidthSums$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberColumnWidthSums$1$1.class", "size": 5519, "crc": -1220437207}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberRowHeightSums$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberRowHeightSums$1$1.class", "size": 5254, "crc": -903260001}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt.class", "size": 30199, "crc": -1363928127}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridInterval.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridInterval.class", "size": 4337, "crc": 1477063597}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion$DefaultSpan$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion$DefaultSpan$1.class", "size": 2073, "crc": -71063406}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion.class", "size": 1608, "crc": 492696748}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$1$1.class", "size": 1476, "crc": 1406304703}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$2$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$2$1.class", "size": 2540, "crc": 805155295}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$3.class", "size": 1483, "crc": 1357066185}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$4.class", "size": 3557, "crc": 1082574033}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent.class", "size": 8930, "crc": 846595293}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo$Companion.class", "size": 916, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo.class", "size": 1618, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProvider.class", "size": 1189, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$1.class", "size": 5017, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$2.class", "size": 1923, "crc": -82015998}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl.class", "size": 6272, "crc": 573368442}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$1.class", "size": 1340, "crc": -600139782}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$intervalContentState$1.class", "size": 2213, "crc": 278673061}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$itemProviderState$1.class", "size": 3202, "crc": -313534840}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt.class", "size": 5479, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemScope.class", "size": 4389, "crc": -897544978}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemScopeImpl.class", "size": 2513, "crc": -695103998}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemSpanScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemSpanScope.class", "size": 782, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$LazyGrid$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$LazyGrid$1.class", "size": 4297, "crc": 529547241}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measureResult$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measureResult$1.class", "size": 3273, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredItemProvider$1.class", "size": 3965, "crc": -864352838}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredLineProvider$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredLineProvider$1.class", "size": 3512, "crc": -236066314}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$prefetchInfoRetriever$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$prefetchInfoRetriever$1.class", "size": 5152, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1.class", "size": 15584, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt.class", "size": 19952, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfo.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfo.class", "size": 1834, "crc": 60877100}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$3.class", "size": 2493, "crc": -308087816}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$6.class", "size": 4503, "crc": 2023102454}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt.class", "size": 27736, "crc": 645860007}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureResult.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureResult.class", "size": 11408, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItem.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItem.class", "size": 15311, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemKt.class", "size": 427, "crc": 645791865}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemProvider.class", "size": 5447, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLine.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLine.class", "size": 5738, "crc": 891292188}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLineProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLineProvider.class", "size": 6471, "crc": -59952146}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchScope.class", "size": 1130, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategy.class", "size": 1846, "crc": -4449416}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategyKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategyKt.class", "size": 1237, "crc": 373134908}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScope$items$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScope$items$1.class", "size": 1446, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScope.class", "size": 3952, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScopeMarker.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScopeMarker.class", "size": 633, "crc": 35944344}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollPosition.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollPosition.class", "size": 7345, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollPositionKt.class", "size": 528, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSlots.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSlots.class", "size": 1237, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSlotsProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSlotsProvider.class", "size": 952, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanKt.class", "size": 1574, "crc": 790201991}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$Bucket.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$Bucket.class", "size": 1258, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LazyGridItemSpanScopeImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LazyGridItemSpanScopeImpl.class", "size": 1610, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LineConfiguration.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LineConfiguration.class", "size": 1802, "crc": 61144763}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$getLineIndexOfItem$lowerBoundBucket$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$getLineIndexOfItem$lowerBoundBucket$1.class", "size": 1858, "crc": -611515845}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider.class", "size": 10729, "crc": 472747613}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$1.class", "size": 2232, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$2.class", "size": 1864, "crc": 802733533}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$saver$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$saver$3.class", "size": 2461, "crc": 629586788}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$saver$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$saver$4.class", "size": 2262, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion.class", "size": 2654, "crc": -1003514208}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchScope$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchScope$1.class", "size": 5601, "crc": -1553070267}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchState$1.class", "size": 4441, "crc": -938254460}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$remeasurementModifier$1.class", "size": 1552, "crc": 9201371}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$requestScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$requestScrollToItem$1.class", "size": 3809, "crc": -1299380585}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scroll$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scroll$1.class", "size": 1924, "crc": -1435880162}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollToItem$2.class", "size": 3677, "crc": 114289513}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollableState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollableState$1.class", "size": 1608, "crc": -78609418}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState.class", "size": 26010, "crc": 823995042}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$1.class", "size": 1892, "crc": 807346843}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$2.class", "size": 1730, "crc": -549046349}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$1$1.class", "size": 1611, "crc": -344622204}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$3$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$3$1.class", "size": 1867, "crc": 1271881803}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt.class", "size": 9077, "crc": -801653772}, {"key": "androidx/compose/foundation/lazy/grid/LazySemanticsKt$rememberLazyGridSemanticState$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazySemanticsKt$rememberLazyGridSemanticState$1$1.class", "size": 4012, "crc": -142019887}, {"key": "androidx/compose/foundation/lazy/grid/LazySemanticsKt.class", "name": "androidx/compose/foundation/lazy/grid/LazySemanticsKt.class", "size": 3757, "crc": -38664877}, {"key": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$Companion.class", "name": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$Companion.class", "size": 1892, "crc": -1934751272}, {"key": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$PrefetchRequestScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$PrefetchRequestScopeImpl.class", "size": 1389, "crc": -1657051616}, {"key": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler.class", "name": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler.class", "size": 6779, "crc": -832417663}, {"key": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier$waitForFirstLayout$1.class", "name": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier$waitForFirstLayout$1.class", "size": 1971, "crc": -61276475}, {"key": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier.class", "name": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier.class", "size": 4265, "crc": -1028501851}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion$CREATOR$1.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion$CREATOR$1.class", "size": 1934, "crc": 2095606017}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion.class", "size": 1105, "crc": 2049243120}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey.class", "size": 3239, "crc": -457458608}, {"key": "androidx/compose/foundation/lazy/layout/DummyHandle.class", "name": "androidx/compose/foundation/lazy/layout/DummyHandle.class", "size": 1261, "crc": 1636921189}, {"key": "androidx/compose/foundation/lazy/layout/IntervalList$Interval.class", "name": "androidx/compose/foundation/lazy/layout/IntervalList$Interval.class", "size": 2645, "crc": 1451465478}, {"key": "androidx/compose/foundation/lazy/layout/IntervalList.class", "name": "androidx/compose/foundation/lazy/layout/IntervalList.class", "size": 2074, "crc": -1943820696}, {"key": "androidx/compose/foundation/lazy/layout/IntervalListKt.class", "name": "androidx/compose/foundation/lazy/layout/IntervalListKt.class", "size": 2718, "crc": -1379491333}, {"key": "androidx/compose/foundation/lazy/layout/ItemFoundInScroll.class", "name": "androidx/compose/foundation/lazy/layout/ItemFoundInScroll.class", "size": 1891, "crc": -745174598}, {"key": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2$3.class", "name": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2$3.class", "size": 7296, "crc": -1852953128}, {"key": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2$5.class", "name": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2$5.class", "size": 3908, "crc": -553075921}, {"key": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2.class", "size": 12823, "crc": 347710044}, {"key": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt.class", "size": 4444, "crc": -845632332}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateItemElement.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateItemElement.class", "size": 6775, "crc": -1942261332}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateScrollScope.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateScrollScope.class", "size": 1917, "crc": -960567321}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationSpecsNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationSpecsNode.class", "size": 3758, "crc": -1049508046}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo$Interval.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo$Interval.class", "size": 3742, "crc": 2016628209}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo.class", "size": 5146, "crc": -1498973536}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$Companion$emptyBeyondBoundsScope$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$Companion$emptyBeyondBoundsScope$1.class", "size": 1214, "crc": -1212966918}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$Companion.class", "size": 1249, "crc": 540410822}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$WhenMappings.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$WhenMappings.class", "size": 898, "crc": 366673890}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$layout$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$layout$2.class", "size": 2560, "crc": 665617769}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal.class", "size": 9363, "crc": 1653640249}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocalKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocalKt.class", "size": 6658, "crc": 1516399408}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsState.class", "size": 848, "crc": 2105268269}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsStateKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsStateKt.class", "size": 4671, "crc": 571810513}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval$type$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval$type$1.class", "size": 1544, "crc": 1015694520}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval.class", "size": 1595, "crc": 436457302}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent.class", "size": 4788, "crc": 1887734788}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$1.class", "size": 1313, "crc": 833024532}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$Companion.class", "size": 1176, "crc": -1661180534}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$1.class", "size": 3967, "crc": 1917614048}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2$1.class", "size": 2620, "crc": -640028373}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2.class", "size": 5449, "crc": 1202150344}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1$1.class", "size": 2629, "crc": 1061901864}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1.class", "size": 5297, "crc": 875949613}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1$1.class", "size": 2643, "crc": -1390585389}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1.class", "size": 6216, "crc": 1027318393}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$cancelPlacementAnimation$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$cancelPlacementAnimation$1.class", "size": 4459, "crc": 1147559046}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$1.class", "size": 3810, "crc": -645185389}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$2.class", "size": 3806, "crc": -1054547884}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$3.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$3.class", "size": 3806, "crc": -522263919}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation.class", "size": 17069, "crc": -1645856237}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimationKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimationKt.class", "size": 1514, "crc": -429956633}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsElement.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsElement.class", "size": 4994, "crc": 1202366210}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsNode.class", "size": 7970, "crc": 1313999040}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo$updateAnimation$1$animation$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo$updateAnimation$1$animation$1.class", "size": 2228, "crc": 1356537756}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo.class", "size": 8214, "crc": 1596317806}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$1.class", "size": 3060, "crc": -1179495666}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$2.class", "size": 3051, "crc": -980892021}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$1.class", "size": 3130, "crc": 1556811906}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$2.class", "size": 3120, "crc": 598159353}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator.class", "size": 33406, "crc": -33516147}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimatorKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimatorKt.class", "size": 2369, "crc": 1955462440}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$2$1$invoke$$inlined$onDispose$1.class", "size": 2737, "crc": -1338299351}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$2$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$2$1.class", "size": 3563, "crc": -1634888091}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.class", "size": 7923, "crc": -1970536578}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent.class", "size": 4275, "crc": -521263644}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory.class", "size": 4577, "crc": 1858800360}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$1.class", "size": 2848, "crc": -2113145829}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$2.class", "size": 2190, "crc": 889728585}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt.class", "size": 3767, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProvider.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProvider.class", "size": 1581, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProviderKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProviderKt.class", "size": 1453, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemReusePolicy.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemReusePolicy.class", "size": 3105, "crc": -163211398}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap$Empty.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap$Empty.class", "size": 1456, "crc": -332671389}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap.class", "size": 1083, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$1.class", "size": 1622, "crc": 249819641}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2.class", "size": 2985, "crc": -1271732666}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$1$1$invoke$$inlined$onDispose$1.class", "size": 2358, "crc": 463528409}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$1$1.class", "size": 4006, "crc": 485370260}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$2$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$2$1.class", "size": 3283, "crc": -53388228}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$itemContentFactory$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$itemContentFactory$1$1.class", "size": 1987, "crc": -1091318779}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3.class", "size": 10425, "crc": -446657998}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$4.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$4.class", "size": 3033, "crc": -659422560}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt.class", "size": 9461, "crc": 1822384220}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScope.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScope.class", "size": 5683, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScopeImpl.class", "size": 10981, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItem.class", "size": 1735, "crc": -965677550}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemProvider.class", "size": 1080, "crc": -27171777}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState$Companion.class", "size": 1723, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState.class", "size": 4073, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItem.class", "size": 9229, "crc": 156562209}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1$invoke$$inlined$onDispose$1.class", "size": 2394, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1.class", "size": 3215, "crc": 1032024090}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$2.class", "size": 2523, "crc": 1529945750}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt.class", "size": 7484, "crc": 1993064635}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList$PinnedItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList$PinnedItem.class", "size": 981, "crc": -727417793}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList.class", "size": 8003, "crc": -1583419057}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$NestedPrefetchScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$NestedPrefetchScopeImpl.class", "size": 3046, "crc": 1297317702}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchHandle.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchHandle.class", "size": 853, "crc": 1668777183}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState.class", "size": 5817, "crc": 1389728282}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchStateKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchStateKt.class", "size": 2116, "crc": 1636770137}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticState.class", "size": 1329, "crc": -1051119731}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt.class", "size": 3429, "crc": -555376312}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifier.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifier.class", "size": 5092, "crc": 221560855}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$applySemantics$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$applySemantics$2.class", "size": 1857, "crc": 1367039111}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$indexForKeyMapping$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$indexForKeyMapping$1.class", "size": 2556, "crc": -249698439}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$1.class", "size": 1796, "crc": -1845203141}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$2.class", "size": 1799, "crc": 1372160487}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3$2.class", "size": 4177, "crc": 1940726455}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3.class", "size": 3356, "crc": 1421467555}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode.class", "size": 7280, "crc": 1760873527}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$1.class", "size": 1784, "crc": 2027616107}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$1.class", "size": 3190, "crc": 1248444120}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$2.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$2.class", "size": 2305, "crc": 768113554}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion.class", "size": 2456, "crc": -809325115}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$2$1$invoke$$inlined$onDispose$1.class", "size": 2646, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$2$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$2$1.class", "size": 3437, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$3.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$3.class", "size": 2394, "crc": 584759436}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder.class", "size": 11049, "crc": -981303249}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$1.class", "size": 3650, "crc": 195108150}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$2.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$2.class", "size": 2194, "crc": -673347721}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$holder$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$holder$1$1.class", "size": 1889, "crc": 643962110}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt.class", "size": 7406, "crc": 351151555}, {"key": "androidx/compose/foundation/lazy/layout/Lazy_androidKt.class", "name": "androidx/compose/foundation/lazy/layout/Lazy_androidKt.class", "size": 778, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/MutableIntervalList.class", "name": "androidx/compose/foundation/lazy/layout/MutableIntervalList.class", "size": 6744, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap$2$1.class", "name": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap$2$1.class", "size": 3893, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap.class", "name": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap.class", "size": 5830, "crc": -630218564}, {"key": "androidx/compose/foundation/lazy/layout/NestedPrefetchScope.class", "name": "androidx/compose/foundation/lazy/layout/NestedPrefetchScope.class", "size": 906, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/ObservableScopeInvalidator.class", "name": "androidx/compose/foundation/lazy/layout/ObservableScopeInvalidator.class", "size": 4450, "crc": 342876798}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$NestedPrefetchController.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$NestedPrefetchController.class", "size": 6107, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$resolveNestedPrefetchStates$1.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$resolveNestedPrefetchStates$1.class", "size": 4586, "crc": 454263341}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl.class", "size": 15602, "crc": 709987893}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider.class", "size": 4130, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchMetrics.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchMetrics.class", "size": 5608, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchRequest.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchRequest.class", "size": 910, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchRequestScope.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchRequestScope.class", "size": 603, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchScheduler.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchScheduler.class", "size": 841, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt.class", "size": 4287, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/StableValue.class", "name": "androidx/compose/foundation/lazy/layout/StableValue.class", "size": 2678, "crc": 1898758114}, {"key": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateModifierElement.class", "name": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateModifierElement.class", "size": 4434, "crc": 620626734}, {"key": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateNode.class", "name": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateNode.class", "size": 1965, "crc": 575000772}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyGridStaggeredGridSlotsProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyGridStaggeredGridSlotsProvider.class", "size": 1041, "crc": -518423380}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridAnimateScrollScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridAnimateScrollScope.class", "size": 8487, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsModifierKt.class", "size": 4118, "crc": 472634059}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsState.class", "size": 3041, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridCellsKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridCellsKt.class", "size": 1093, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyHorizontalStaggeredGrid$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyHorizontalStaggeredGrid$1.class", "size": 4140, "crc": -1260309082}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyVerticalStaggeredGrid$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyVerticalStaggeredGrid$1.class", "size": 4149, "crc": -1386322532}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$1.class", "size": 2001, "crc": -93880788}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$10.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$10.class", "size": 4335, "crc": -957646843}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$2$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$2$1.class", "size": 2462, "crc": -187506674}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$3.class", "size": 2465, "crc": -1053461093}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$4$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$4$1.class", "size": 2843, "crc": -438078004}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$5.class", "size": 4421, "crc": 547327845}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$6.class", "size": 2004, "crc": -763831851}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$7$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$7$1.class", "size": 2396, "crc": -1355544247}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$8.class", "size": 2399, "crc": 203961564}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$9$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$9$1.class", "size": 2753, "crc": 560490054}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$1.class", "size": 2186, "crc": 2015532611}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$10.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$10.class", "size": 4575, "crc": -404356949}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$2$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$2$1.class", "size": 2703, "crc": -1735610561}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$3.class", "size": 2706, "crc": 728007400}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$4$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$4$1.class", "size": 3084, "crc": -1704862859}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$5.class", "size": 4661, "crc": 185516329}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$6.class", "size": 2189, "crc": -1988617421}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$7$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$7$1.class", "size": 2637, "crc": -994527981}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$8.class", "size": 2640, "crc": -330692297}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$9$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$9$1.class", "size": 2994, "crc": 1481029747}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberColumnSlots$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberColumnSlots$1$1.class", "size": 5743, "crc": -1324923991}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberRowSlots$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberRowSlots$1$1.class", "size": 5463, "crc": 1623185757}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt.class", "size": 32105, "crc": -291446802}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridInterval.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridInterval.class", "size": 4208, "crc": 1523415264}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$1$1.class", "size": 1577, "crc": 1492376557}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$2.class", "size": 1584, "crc": 1025995097}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$3$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$3$1.class", "size": 1806, "crc": 1653781878}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$4.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$4.class", "size": 3798, "crc": -1440844002}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent.class", "size": 7965, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemInfo.class", "size": 1245, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProvider.class", "size": 1176, "crc": 522051470}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$1.class", "size": 5395, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$2.class", "size": 2022, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl.class", "size": 6504, "crc": 696590979}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$1.class", "size": 1436, "crc": -569034214}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$intervalContentState$1.class", "size": 2417, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$itemProviderState$1.class", "size": 3550, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt.class", "size": 5810, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScope.class", "size": 4506, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScopeImpl.class", "size": 2590, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt$LazyStaggeredGrid$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt$LazyStaggeredGrid$1.class", "size": 4107, "crc": 731330953}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt.class", "size": 16861, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$Companion.class", "size": 1051, "crc": 1747845717}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$SpannedItem.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$SpannedItem.class", "size": 1382, "crc": 510706949}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$getGaps$$inlined$binarySearchBy$default$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$getGaps$$inlined$binarySearchBy$default$1.class", "size": 3076, "crc": 592922069}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$setGaps$$inlined$binarySearchBy$default$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$setGaps$$inlined$binarySearchBy$default$1.class", "size": 3077, "crc": 771971440}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo.class", "size": 8451, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLayoutInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLayoutInfo.class", "size": 1845, "crc": 565989488}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext$measuredItemProvider$1.class", "size": 4344, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext.class", "size": 11872, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$1.class", "size": 1854, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$30.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$30.class", "size": 4626, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt.class", "size": 58879, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$WhenMappings.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$WhenMappings.class", "size": 937, "crc": 792739390}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$rememberStaggeredGridMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$rememberStaggeredGridMeasurePolicy$1$1.class", "size": 11056, "crc": 967137916}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt.class", "size": 9855, "crc": 722632145}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureProvider.class", "size": 7493, "crc": 23737132}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResult.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResult.class", "size": 12233, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$EmptyLazyStaggeredGridLayoutInfo$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$EmptyLazyStaggeredGridLayoutInfo$1.class", "size": 2005, "crc": 604931881}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$findVisibleItem$index$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$findVisibleItem$index$1.class", "size": 2005, "crc": -536979700}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt.class", "size": 4538, "crc": -1504066461}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasuredItem.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasuredItem.class", "size": 17870, "crc": 1740654359}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope$items$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope$items$1.class", "size": 1527, "crc": -918742757}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope.class", "size": 4028, "crc": 720133628}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScopeMarker.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScopeMarker.class", "size": 670, "crc": 138975410}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPosition.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPosition.class", "size": 12296, "crc": -454350680}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPositionKt.class", "size": 555, "crc": -326638121}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt$rememberLazyStaggeredGridSemanticState$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt$rememberLazyStaggeredGridSemanticState$1$1.class", "size": 4244, "crc": 233573686}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt.class", "size": 4138, "crc": 1794540743}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlotCache.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlotCache.class", "size": 3320, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlots.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlots.class", "size": 1282, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSpanProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSpanProvider.class", "size": 3378, "crc": -303319307}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$1.class", "size": 2506, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$2.class", "size": 1968, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion.class", "size": 1500, "crc": 512861230}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$remeasurementModifier$1.class", "size": 1666, "crc": -2145047391}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$requestScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$requestScrollToItem$1.class", "size": 3962, "crc": -1392863116}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scroll$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scroll$1.class", "size": 2077, "crc": 1827575184}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollPosition$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollPosition$1.class", "size": 1688, "crc": -1387231373}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollToItem$2.class", "size": 3825, "crc": -656618648}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollableState$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollableState$1.class", "size": 1763, "crc": -1052709943}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState.class", "size": 33290, "crc": 1482857013}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt$rememberLazyStaggeredGridState$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt$rememberLazyStaggeredGridState$1$1.class", "size": 1791, "crc": 111699003}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt.class", "size": 4721, "crc": -620009728}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/SpanRange.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/SpanRange.class", "size": 4277, "crc": 1471946461}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Adaptive.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Adaptive.class", "size": 3872, "crc": -225929166}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Fixed.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Fixed.class", "size": 2809, "crc": -931641007}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$FixedSize.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$FixedSize.class", "size": 2542, "crc": 1149681465}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells.class", "size": 1185, "crc": 506159751}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan$Companion.class", "size": 1477, "crc": -348015975}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan.class", "size": 1672, "crc": -682576533}, {"key": "androidx/compose/foundation/pager/DefaultPagerNestedScrollConnection.class", "name": "androidx/compose/foundation/pager/DefaultPagerNestedScrollConnection.class", "size": 5342, "crc": 1492374047}, {"key": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$1.class", "name": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$1.class", "size": 2427, "crc": -808046164}, {"key": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$2$1.class", "name": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$2$1.class", "size": 1647, "crc": -195014497}, {"key": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$2.class", "name": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$2.class", "size": 2313, "crc": 1421204329}, {"key": "androidx/compose/foundation/pager/DefaultPagerState$Companion.class", "name": "androidx/compose/foundation/pager/DefaultPagerState$Companion.class", "size": 1379, "crc": -1098005987}, {"key": "androidx/compose/foundation/pager/DefaultPagerState.class", "name": "androidx/compose/foundation/pager/DefaultPagerState.class", "size": 3697, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$2.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$2.class", "size": 5447, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$measurePolicy$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$measurePolicy$1$1.class", "size": 2204, "crc": 811920073}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$pagerItemProvider$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$pagerItemProvider$1$1.class", "size": 2212, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1$1.class", "size": 7469, "crc": -860287307}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1.class", "size": 4114, "crc": 580524554}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1.class", "size": 4129, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$1.class", "size": 1356, "crc": 988420679}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$intervalContentState$1.class", "size": 3047, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$itemProviderState$1.class", "size": 3182, "crc": 454545888}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt.class", "size": 27403, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "size": 4044, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt.class", "name": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt.class", "size": 1225, "crc": 512240656}, {"key": "androidx/compose/foundation/pager/MeasuredPage.class", "name": "androidx/compose/foundation/pager/MeasuredPage.class", "size": 10672, "crc": -**********}, {"key": "androidx/compose/foundation/pager/MeasuredPageKt.class", "name": "androidx/compose/foundation/pager/MeasuredPageKt.class", "size": 407, "crc": -79171969}, {"key": "androidx/compose/foundation/pager/PageInfo.class", "name": "androidx/compose/foundation/pager/PageInfo.class", "size": 755, "crc": -545416060}, {"key": "androidx/compose/foundation/pager/PageSize$Fill.class", "name": "androidx/compose/foundation/pager/PageSize$Fill.class", "size": 1323, "crc": 358858427}, {"key": "androidx/compose/foundation/pager/PageSize$Fixed.class", "name": "androidx/compose/foundation/pager/PageSize$Fixed.class", "size": 2290, "crc": -2103498554}, {"key": "androidx/compose/foundation/pager/PageSize.class", "name": "androidx/compose/foundation/pager/PageSize.class", "size": 946, "crc": 1398134650}, {"key": "androidx/compose/foundation/pager/PagerBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/pager/PagerBeyondBoundsModifierKt.class", "size": 3870, "crc": 738195958}, {"key": "androidx/compose/foundation/pager/PagerBeyondBoundsState.class", "name": "androidx/compose/foundation/pager/PagerBeyondBoundsState.class", "size": 2849, "crc": 521512166}, {"key": "androidx/compose/foundation/pager/PagerBringIntoViewSpec.class", "name": "androidx/compose/foundation/pager/PagerBringIntoViewSpec.class", "size": 3041, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerDebugConfig.class", "name": "androidx/compose/foundation/pager/PagerDebugConfig.class", "size": 1195, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerDefaults$flingBehavior$2$snapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/pager/PagerDefaults$flingBehavior$2$snapLayoutInfoProvider$1.class", "size": 2475, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerDefaults.class", "name": "androidx/compose/foundation/pager/PagerDefaults.class", "size": 10403, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerIntervalContent.class", "name": "androidx/compose/foundation/pager/PagerIntervalContent.class", "size": 2952, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerKt$HorizontalPager$1.class", "name": "androidx/compose/foundation/pager/PagerKt$HorizontalPager$1.class", "size": 4882, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerKt$VerticalPager$1.class", "name": "androidx/compose/foundation/pager/PagerKt$VerticalPager$1.class", "size": 4890, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$1.class", "size": 1692, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$2.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$2.class", "size": 1691, "crc": 1733801904}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$3.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$3.class", "size": 1692, "crc": 1429756458}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$4.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$4.class", "size": 1691, "crc": -1033794739}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1.class", "size": 2818, "crc": -100743691}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performBackwardPaging$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performBackwardPaging$1.class", "size": 3741, "crc": -1768404050}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performForwardPaging$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performForwardPaging$1.class", "size": 3732, "crc": -1472621842}, {"key": "androidx/compose/foundation/pager/PagerKt.class", "name": "androidx/compose/foundation/pager/PagerKt.class", "size": 19868, "crc": 1441628989}, {"key": "androidx/compose/foundation/pager/PagerLayoutInfo.class", "name": "androidx/compose/foundation/pager/PagerLayoutInfo.class", "size": 2174, "crc": 279110431}, {"key": "androidx/compose/foundation/pager/PagerLayoutInfoKt.class", "name": "androidx/compose/foundation/pager/PagerLayoutInfoKt.class", "size": 1234, "crc": -1622123204}, {"key": "androidx/compose/foundation/pager/PagerLayoutIntervalContent.class", "name": "androidx/compose/foundation/pager/PagerLayoutIntervalContent.class", "size": 4177, "crc": -2095377912}, {"key": "androidx/compose/foundation/pager/PagerLazyAnimateScrollScopeKt$PagerLazyAnimateScrollScope$1.class", "name": "androidx/compose/foundation/pager/PagerLazyAnimateScrollScopeKt$PagerLazyAnimateScrollScope$1.class", "size": 6683, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerLazyAnimateScrollScopeKt.class", "name": "androidx/compose/foundation/pager/PagerLazyAnimateScrollScopeKt.class", "size": 1261, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$1.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$1.class", "size": 4994, "crc": 576652405}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$2.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$2.class", "size": 1913, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider.class", "size": 6614, "crc": -935272247}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$14.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$14.class", "size": 4203, "crc": 960902105}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$4.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$4.class", "size": 2293, "crc": -942860937}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesAfter$1.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesAfter$1.class", "size": 3784, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesBefore$1.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesBefore$1.class", "size": 3786, "crc": -67056154}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt.class", "size": 30130, "crc": -1353741588}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1$measureResult$1.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1$measureResult$1.class", "size": 3285, "crc": 549377028}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1.class", "size": 13613, "crc": 704884664}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt.class", "size": 8327, "crc": 819383166}, {"key": "androidx/compose/foundation/pager/PagerMeasureResult.class", "name": "androidx/compose/foundation/pager/PagerMeasureResult.class", "size": 12165, "crc": -1463532440}, {"key": "androidx/compose/foundation/pager/PagerScope.class", "name": "androidx/compose/foundation/pager/PagerScope.class", "size": 471, "crc": 622677920}, {"key": "androidx/compose/foundation/pager/PagerScopeImpl.class", "name": "androidx/compose/foundation/pager/PagerScopeImpl.class", "size": 917, "crc": -844715255}, {"key": "androidx/compose/foundation/pager/PagerScrollPosition.class", "name": "androidx/compose/foundation/pager/PagerScrollPosition.class", "size": 7625, "crc": -918232833}, {"key": "androidx/compose/foundation/pager/PagerScrollPositionKt.class", "name": "androidx/compose/foundation/pager/PagerScrollPositionKt.class", "size": 1596, "crc": -1061393649}, {"key": "androidx/compose/foundation/pager/PagerSemanticsKt.class", "name": "androidx/compose/foundation/pager/PagerSemanticsKt.class", "size": 3729, "crc": 1985022463}, {"key": "androidx/compose/foundation/pager/PagerSnapDistance$Companion.class", "name": "androidx/compose/foundation/pager/PagerSnapDistance$Companion.class", "size": 1709, "crc": -2123249372}, {"key": "androidx/compose/foundation/pager/PagerSnapDistance.class", "name": "androidx/compose/foundation/pager/PagerSnapDistance.class", "size": 1038, "crc": 542756377}, {"key": "androidx/compose/foundation/pager/PagerSnapDistanceKt.class", "name": "androidx/compose/foundation/pager/PagerSnapDistanceKt.class", "size": 732, "crc": -1373086302}, {"key": "androidx/compose/foundation/pager/PagerSnapDistanceMaxPages.class", "name": "androidx/compose/foundation/pager/PagerSnapDistanceMaxPages.class", "size": 2830, "crc": 1121333855}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$1.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$1.class", "size": 1937, "crc": 796089289}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3.class", "size": 1915, "crc": -1598641804}, {"key": "androidx/compose/foundation/pager/PagerState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/pager/PagerState$remeasurementModifier$1.class", "size": 1500, "crc": -64807908}, {"key": "androidx/compose/foundation/pager/PagerState$requestScrollToPage$1.class", "name": "androidx/compose/foundation/pager/PagerState$requestScrollToPage$1.class", "size": 3750, "crc": 1836386388}, {"key": "androidx/compose/foundation/pager/PagerState$scroll$1.class", "name": "androidx/compose/foundation/pager/PagerState$scroll$1.class", "size": 1940, "crc": 773664760}, {"key": "androidx/compose/foundation/pager/PagerState$scrollToPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$scrollToPage$2.class", "size": 5311, "crc": 1880138415}, {"key": "androidx/compose/foundation/pager/PagerState$scrollableState$1.class", "name": "androidx/compose/foundation/pager/PagerState$scrollableState$1.class", "size": 1602, "crc": -1437284391}, {"key": "androidx/compose/foundation/pager/PagerState$settledPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$settledPage$2.class", "size": 1598, "crc": 1364539543}, {"key": "androidx/compose/foundation/pager/PagerState$targetPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$targetPage$2.class", "size": 2085, "crc": -431074444}, {"key": "androidx/compose/foundation/pager/PagerState.class", "name": "androidx/compose/foundation/pager/PagerState.class", "size": 41752, "crc": -503287384}, {"key": "androidx/compose/foundation/pager/PagerStateKt$EmptyLayoutInfo$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$EmptyLayoutInfo$1.class", "size": 1856, "crc": 1288897899}, {"key": "androidx/compose/foundation/pager/PagerStateKt$UnitDensity$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$UnitDensity$1.class", "size": 1064, "crc": -682666238}, {"key": "androidx/compose/foundation/pager/PagerStateKt$animateScrollToPage$2$3.class", "name": "androidx/compose/foundation/pager/PagerStateKt$animateScrollToPage$2$3.class", "size": 2839, "crc": 1314620397}, {"key": "androidx/compose/foundation/pager/PagerStateKt$animateScrollToPage$2.class", "name": "androidx/compose/foundation/pager/PagerStateKt$animateScrollToPage$2.class", "size": 7191, "crc": 1973943276}, {"key": "androidx/compose/foundation/pager/PagerStateKt$rememberPagerState$1$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$rememberPagerState$1$1.class", "size": 1834, "crc": 1765819573}, {"key": "androidx/compose/foundation/pager/PagerStateKt.class", "name": "androidx/compose/foundation/pager/PagerStateKt.class", "size": 14050, "crc": -1094649966}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$1.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$1.class", "size": 1887, "crc": 575878969}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$2$1.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$2$1.class", "size": 2435, "crc": 841499171}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior.class", "size": 3990, "crc": 1626294830}, {"key": "androidx/compose/foundation/relocation/BringIntoViewParent.class", "name": "androidx/compose/foundation/relocation/BringIntoViewParent.class", "size": 1228, "crc": -369544973}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequester.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequester.class", "size": 1637, "crc": 968089396}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterElement.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterElement.class", "size": 3348, "crc": -1498483240}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$1.class", "size": 2058, "crc": -1280630740}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl.class", "size": 5479, "crc": 836188319}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt.class", "size": 1940, "crc": -1703707606}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewRequesterKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewRequesterKt.class", "size": 1711, "crc": -720169745}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewResponderKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewResponderKt.class", "size": 3760, "crc": 857036975}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode.class", "size": 3560, "crc": -625816242}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponder.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponder.class", "size": 1279, "crc": 1263128077}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderElement.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderElement.class", "size": 3348, "crc": 1202941516}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$TraverseKey.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$TraverseKey.class", "size": 923, "crc": -736646022}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$1$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$1$1.class", "size": 2786, "crc": 840429200}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$1.class", "size": 4747, "crc": 1793366294}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$2.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$2.class", "size": 4821, "crc": 1665374078}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2.class", "size": 5244, "crc": 868527107}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$parentRect$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$parentRect$1.class", "size": 3472, "crc": -395697572}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode.class", "size": 7022, "crc": 1056355493}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponder_androidKt$defaultBringIntoViewParent$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponder_androidKt$defaultBringIntoViewParent$1.class", "size": 3095, "crc": -1711327966}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponder_androidKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponder_androidKt.class", "size": 1738, "crc": -449223669}, {"key": "androidx/compose/foundation/relocation/ScrollIntoView.class", "name": "androidx/compose/foundation/relocation/ScrollIntoView.class", "size": 1412, "crc": 128459589}, {"key": "androidx/compose/foundation/relocation/ScrollIntoView__ScrollIntoViewRequesterKt$scrollIntoView$2.class", "name": "androidx/compose/foundation/relocation/ScrollIntoView__ScrollIntoViewRequesterKt$scrollIntoView$2.class", "size": 2801, "crc": 873530601}, {"key": "androidx/compose/foundation/relocation/ScrollIntoView__ScrollIntoViewRequesterKt.class", "name": "androidx/compose/foundation/relocation/ScrollIntoView__ScrollIntoViewRequesterKt.class", "size": 3261, "crc": -1641440555}, {"key": "androidx/compose/foundation/selection/SelectableElement.class", "name": "androidx/compose/foundation/selection/SelectableElement.class", "size": 5607, "crc": 1880083538}, {"key": "androidx/compose/foundation/selection/SelectableGroupKt$selectableGroup$1.class", "name": "androidx/compose/foundation/selection/SelectableGroupKt$selectableGroup$1.class", "size": 1733, "crc": -946980790}, {"key": "androidx/compose/foundation/selection/SelectableGroupKt.class", "name": "androidx/compose/foundation/selection/SelectableGroupKt.class", "size": 1239, "crc": -1917644351}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable$2.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable$2.class", "size": 6376, "crc": -378206754}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6844, "crc": -1282126075}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3380, "crc": -2073518436}, {"key": "androidx/compose/foundation/selection/SelectableKt.class", "name": "androidx/compose/foundation/selection/SelectableKt.class", "size": 7027, "crc": 2104304961}, {"key": "androidx/compose/foundation/selection/SelectableNode.class", "name": "androidx/compose/foundation/selection/SelectableNode.class", "size": 3691, "crc": -129607237}, {"key": "androidx/compose/foundation/selection/ToggleableElement.class", "name": "androidx/compose/foundation/selection/ToggleableElement.class", "size": 5702, "crc": 361996869}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable$2.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable$2.class", "size": 6418, "crc": -2144361087}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6847, "crc": 894079033}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3378, "crc": 838604970}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$2.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$2.class", "size": 6676, "crc": -190259406}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 7053, "crc": -222461454}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3551, "crc": -1993378811}, {"key": "androidx/compose/foundation/selection/ToggleableKt.class", "name": "androidx/compose/foundation/selection/ToggleableKt.class", "size": 11482, "crc": 276664996}, {"key": "androidx/compose/foundation/selection/ToggleableNode$1.class", "name": "androidx/compose/foundation/selection/ToggleableNode$1.class", "size": 1769, "crc": -1730586628}, {"key": "androidx/compose/foundation/selection/ToggleableNode$_onClick$1.class", "name": "androidx/compose/foundation/selection/ToggleableNode$_onClick$1.class", "size": 1854, "crc": -26001345}, {"key": "androidx/compose/foundation/selection/ToggleableNode.class", "name": "androidx/compose/foundation/selection/ToggleableNode.class", "size": 5163, "crc": -1929752426}, {"key": "androidx/compose/foundation/selection/TriStateToggleableElement.class", "name": "androidx/compose/foundation/selection/TriStateToggleableElement.class", "size": 5910, "crc": 1748204630}, {"key": "androidx/compose/foundation/selection/TriStateToggleableNode.class", "name": "androidx/compose/foundation/selection/TriStateToggleableNode.class", "size": 3980, "crc": -1235047077}, {"key": "androidx/compose/foundation/shape/AbsoluteCutCornerShape.class", "name": "androidx/compose/foundation/shape/AbsoluteCutCornerShape.class", "size": 5407, "crc": -2126291256}, {"key": "androidx/compose/foundation/shape/AbsoluteCutCornerShapeKt.class", "name": "androidx/compose/foundation/shape/AbsoluteCutCornerShapeKt.class", "size": 4801, "crc": 450267993}, {"key": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShape.class", "name": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShape.class", "size": 5290, "crc": -1643823939}, {"key": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShapeKt.class", "name": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShapeKt.class", "size": 4905, "crc": -1781055521}, {"key": "androidx/compose/foundation/shape/CornerBasedShape.class", "name": "androidx/compose/foundation/shape/CornerBasedShape.class", "size": 5396, "crc": -1120922100}, {"key": "androidx/compose/foundation/shape/CornerSize.class", "name": "androidx/compose/foundation/shape/CornerSize.class", "size": 839, "crc": 655596887}, {"key": "androidx/compose/foundation/shape/CornerSizeKt$ZeroCornerSize$1.class", "name": "androidx/compose/foundation/shape/CornerSizeKt$ZeroCornerSize$1.class", "size": 1735, "crc": -1393355637}, {"key": "androidx/compose/foundation/shape/CornerSizeKt.class", "name": "androidx/compose/foundation/shape/CornerSizeKt.class", "size": 2027, "crc": -464488047}, {"key": "androidx/compose/foundation/shape/CutCornerShape.class", "name": "androidx/compose/foundation/shape/CutCornerShape.class", "size": 5449, "crc": -260874621}, {"key": "androidx/compose/foundation/shape/CutCornerShapeKt.class", "name": "androidx/compose/foundation/shape/CutCornerShapeKt.class", "size": 4589, "crc": -468908385}, {"key": "androidx/compose/foundation/shape/DpCornerSize.class", "name": "androidx/compose/foundation/shape/DpCornerSize.class", "size": 3873, "crc": 1293079385}, {"key": "androidx/compose/foundation/shape/GenericShape.class", "name": "androidx/compose/foundation/shape/GenericShape.class", "size": 3531, "crc": -1416556906}, {"key": "androidx/compose/foundation/shape/PercentCornerSize.class", "name": "androidx/compose/foundation/shape/PercentCornerSize.class", "size": 3271, "crc": 2130740982}, {"key": "androidx/compose/foundation/shape/PxCornerSize.class", "name": "androidx/compose/foundation/shape/PxCornerSize.class", "size": 2806, "crc": -1714102328}, {"key": "androidx/compose/foundation/shape/RoundedCornerShape.class", "name": "androidx/compose/foundation/shape/RoundedCornerShape.class", "size": 5332, "crc": 1624363461}, {"key": "androidx/compose/foundation/shape/RoundedCornerShapeKt.class", "name": "androidx/compose/foundation/shape/RoundedCornerShapeKt.class", "size": 4966, "crc": -1950011386}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$1.class", "size": 9804, "crc": -628552165}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$2.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$2.class", "size": 2147, "crc": -450459203}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$finalModifier$1$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$finalModifier$1$1.class", "size": 2799, "crc": 2039921927}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$DefaultCursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$DefaultCursorHandle$1.class", "size": 1892, "crc": 245751567}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1$1.class", "size": 5354, "crc": -2115218361}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1.class", "size": 2920, "crc": -1316397561}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1.class", "size": 5887, "crc": -1006290843}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt.class", "size": 9777, "crc": -246802375}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2$1.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2$1.class", "size": 3646, "crc": 493341946}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2.class", "size": 4506, "crc": -1290650786}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$2.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$2.class", "size": 2518, "crc": -479507874}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt.class", "size": 13152, "crc": -1138106055}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$1$1.class", "size": 4232, "crc": 905551021}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$2$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$2$1.class", "size": 4360, "crc": -954353224}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$3.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$3.class", "size": 7666, "crc": -1238193299}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$4.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$4.class", "size": 4776, "crc": 666227471}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1$1.class", "size": 1474, "crc": -1276328507}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1$2.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1$2.class", "size": 1474, "crc": 1547996920}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1.class", "size": 3021, "crc": -432448950}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$1$1.class", "size": 2273, "crc": -664427218}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$1.class", "size": 9497, "crc": -865260437}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$2.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$2.class", "size": 2028, "crc": 1164896002}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$copyDisabledToolbar$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$copyDisabledToolbar$1$1.class", "size": 2495, "crc": -888352521}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt.class", "size": 20555, "crc": -1570315909}, {"key": "androidx/compose/foundation/text/BasicTextFieldDefaults.class", "name": "androidx/compose/foundation/text/BasicTextFieldDefaults.class", "size": 1358, "crc": -1582324375}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$1.class", "size": 5466, "crc": -246647962}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$10.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$10.class", "size": 1926, "crc": -2045751241}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$11$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$11$1.class", "size": 2602, "crc": -1547005849}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$12.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$12.class", "size": 5165, "crc": 1035095869}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$13.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$13.class", "size": 1896, "crc": -1683508503}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$15.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$15.class", "size": 4938, "crc": -1001099743}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$16.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$16.class", "size": 1925, "crc": 485673023}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$18.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$18.class", "size": 5112, "crc": 973820400}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$2$1.class", "size": 3716, "crc": 1485603669}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1$invoke$$inlined$onDispose$1.class", "size": 2313, "crc": -291731684}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1.class", "size": 3635, "crc": -393095297}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1$1.class", "size": 14879, "crc": -2100908385}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1.class", "size": 7022, "crc": -446636576}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$5.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$5.class", "size": 5881, "crc": -1158765493}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$6.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$6.class", "size": 1895, "crc": -791645777}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$7$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$7$1.class", "size": 2807, "crc": -1703548748}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$8$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$8$1.class", "size": 3454, "crc": -1721366370}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$9.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$9.class", "size": 4989, "crc": 1559899474}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$DefaultTextFieldDecorator$1$Decoration$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$DefaultTextFieldDecorator$1$Decoration$1.class", "size": 2340, "crc": 770562196}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$DefaultTextFieldDecorator$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$DefaultTextFieldDecorator$1.class", "size": 3183, "crc": -543610691}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$1$1.class", "size": 1621, "crc": -2035834818}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$2$1.class", "size": 4678, "crc": -1413508588}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$3.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$3.class", "size": 1926, "crc": 1274281952}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$cursorHandleState$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$cursorHandleState$2$1.class", "size": 1961, "crc": 190648526}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$1$1.class", "size": 1638, "crc": -720212263}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$2$1.class", "size": 4823, "crc": -1819234984}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$3$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$3$1.class", "size": 1638, "crc": -1038500417}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$4$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$4$1.class", "size": 4823, "crc": -339725903}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$5.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$5.class", "size": 1938, "crc": 1619550210}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$endHandleState$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$endHandleState$2$1.class", "size": 1972, "crc": 1839075655}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$startHandleState$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$startHandleState$2$1.class", "size": 1976, "crc": 1511027914}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt.class", "size": 78502, "crc": 881859645}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$1.class", "size": 2912, "crc": -993483591}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$2$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$2$1.class", "size": 2746, "crc": 38049227}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$3.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$3.class", "size": 3296, "crc": -687739949}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$4.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$4.class", "size": 2633, "crc": 2105738894}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$5.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$5.class", "size": 3017, "crc": 797144811}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$6.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$6.class", "size": 2686, "crc": 571152623}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$7.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$7.class", "size": 3070, "crc": -1707015936}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$1$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$1$1.class", "size": 1730, "crc": 1132390629}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$2$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$2$1.class", "size": 1769, "crc": 158378603}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$2$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$2$1.class", "size": 2680, "crc": -1666809277}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$3$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$3$1.class", "size": 2736, "crc": 875179407}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$4$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$4$1.class", "size": 2736, "crc": -735883199}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$5$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$5$1.class", "size": 2328, "crc": 1693865955}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$6.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$6.class", "size": 4848, "crc": -1895675201}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$onPlaceholderLayout$1$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$onPlaceholderLayout$1$1.class", "size": 2578, "crc": 1602086920}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$styledText$1$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$styledText$1$1.class", "size": 2235, "crc": 2022428413}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$styledText$2$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$styledText$2$1.class", "size": 1875, "crc": -929273310}, {"key": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$1.class", "size": 2189, "crc": 2038367981}, {"key": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$2.class", "name": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$2.class", "size": 1507, "crc": 1048209229}, {"key": "androidx/compose/foundation/text/BasicTextKt.class", "name": "androidx/compose/foundation/text/BasicTextKt.class", "size": 56653, "crc": -1826344530}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$1.class", "size": 1630, "crc": -296098238}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$2$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$2$1.class", "size": 2417, "crc": -1987313730}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$3.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$3.class", "size": 3014, "crc": -1594042799}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$4.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$4.class", "size": 1662, "crc": -151859128}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$5$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$5$1.class", "size": 2449, "crc": 1825133594}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$6.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$6.class", "size": 3202, "crc": -1599379358}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1$1$1.class", "size": 2904, "crc": -1352980415}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1$1.class", "size": 4998, "crc": 2138774111}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1$2.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1$2.class", "size": 2277, "crc": 1842609636}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1.class", "size": 5775, "crc": -545962750}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1$1.class", "size": 2507, "crc": 247727703}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1.class", "size": 4832, "crc": -1024520736}, {"key": "androidx/compose/foundation/text/ClickableTextKt.class", "name": "androidx/compose/foundation/text/ClickableTextKt.class", "size": 19702, "crc": 1299156612}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-1$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-1$1.class", "size": 3179, "crc": -1396956105}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-2$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-2$1.class", "size": 3179, "crc": 1703672661}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-3$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-3$1.class", "size": 3179, "crc": 1095999663}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-4$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-4$1.class", "size": 3179, "crc": 1431848158}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt.class", "size": 2849, "crc": -1695171462}, {"key": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt$lambda-1$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt$lambda-1$1.class", "size": 3172, "crc": 37662172}, {"key": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt.class", "name": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt.class", "size": 1749, "crc": -708445772}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$1$1.class", "size": 1502, "crc": 1275529047}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$2.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$2.class", "size": 2329, "crc": -651118566}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$3$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$3$1.class", "size": 1516, "crc": -843031641}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$4.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$4.class", "size": 2444, "crc": -367951231}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$5$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$5$1.class", "size": 1493, "crc": -14886414}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$6.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$6.class", "size": 2293, "crc": 1357899940}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$1.class", "size": 3052, "crc": 1595351446}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$2.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$2.class", "size": 2374, "crc": 38635878}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt.class", "size": 13114, "crc": -1276553640}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$1.class", "size": 1910, "crc": -303530905}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$1.class", "size": 1660, "crc": 225661737}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$2.class", "size": 3241, "crc": 506988120}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1.class", "size": 5879, "crc": -841057268}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$1$invoke$$inlined$onDispose$1.class", "size": 2277, "crc": -933219508}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$1.class", "size": 3314, "crc": -1046075205}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$1$invoke$$inlined$onDispose$1.class", "size": 1954, "crc": 782906679}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$1.class", "size": 4742, "crc": 835222018}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2$measure$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2$measure$2.class", "size": 2021, "crc": 2144137770}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2.class", "size": 10241, "crc": 2139766408}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1.class", "size": 10411, "crc": -2135835424}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$coreTextFieldModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$coreTextFieldModifier$1$1.class", "size": 1681, "crc": 751542564}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1.class", "size": 9945, "crc": 391474425}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5.class", "size": 7410, "crc": -228501455}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$6.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$6.class", "size": 5121, "crc": -384077698}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$decorationBoxModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$decorationBoxModifier$1.class", "size": 2446, "crc": 558381864}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$drawModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$drawModifier$1$1.class", "size": 5082, "crc": 1355233324}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1$1$1.class", "size": 5140, "crc": 1092285888}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1.class", "size": 5366, "crc": 1879030480}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$onPositionedModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$onPositionedModifier$1$1.class", "size": 5789, "crc": 499174765}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$1$1.class", "size": 2032, "crc": -362557280}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$2$1.class", "size": 4947, "crc": 399248453}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$scrollerPosition$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$scrollerPosition$1$1.class", "size": 2142, "crc": 121189422}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$1.class", "size": 2375, "crc": -1525089157}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$10.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$10.class", "size": 1643, "crc": -691427467}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$2.class", "size": 3960, "crc": -1759966904}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$3.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$3.class", "size": 4695, "crc": 1372011814}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$4.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$4.class", "size": 3984, "crc": -503378467}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$5.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$5.class", "size": 2064, "crc": -1075213488}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$6.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$6.class", "size": 1942, "crc": -794471172}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$7.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$7.class", "size": 1755, "crc": 521448222}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$8.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$8.class", "size": 1741, "crc": 831067280}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$9.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1$9.class", "size": 1639, "crc": -1018739090}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1.class", "size": 7420, "crc": -1115013623}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$stylusHandwritingModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$stylusHandwritingModifier$1$1.class", "size": 3214, "crc": -28805673}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextFieldRootBox$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextFieldRootBox$2.class", "size": 2528, "crc": -1347280357}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$SelectionToolbarAndHandles$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$SelectionToolbarAndHandles$2.class", "size": 1983, "crc": -393815423}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1.class", "size": 1078, "crc": -1598306784}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$1.class", "size": 4072, "crc": 572665494}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2$1.class", "size": 1912, "crc": 2109197632}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2.class", "size": 4441, "crc": 1104144043}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1.class", "size": 4875, "crc": 767271176}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1.class", "size": 4478, "crc": -790601443}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$3$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$3$1.class", "size": 2489, "crc": 732826577}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$4.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$4.class", "size": 1876, "crc": -1576579644}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$previewKeyEventToDeselectOnBack$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$previewKeyEventToDeselectOnBack$1.class", "size": 2907, "crc": -1387020662}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt.class", "size": 79963, "crc": 884841415}, {"key": "androidx/compose/foundation/text/DeadKeyCombiner.class", "name": "androidx/compose/foundation/text/DeadKeyCombiner.class", "size": 2458, "crc": 1462998632}, {"key": "androidx/compose/foundation/text/EmptyMeasurePolicy$placementBlock$1.class", "name": "androidx/compose/foundation/text/EmptyMeasurePolicy$placementBlock$1.class", "size": 1596, "crc": 843394014}, {"key": "androidx/compose/foundation/text/EmptyMeasurePolicy.class", "name": "androidx/compose/foundation/text/EmptyMeasurePolicy.class", "size": 2736, "crc": -1690042364}, {"key": "androidx/compose/foundation/text/FixedMotionDurationScale.class", "name": "androidx/compose/foundation/text/FixedMotionDurationScale.class", "size": 3092, "crc": -1125741519}, {"key": "androidx/compose/foundation/text/Handle.class", "name": "androidx/compose/foundation/text/Handle.class", "size": 1470, "crc": 1378241993}, {"key": "androidx/compose/foundation/text/HandleState.class", "name": "androidx/compose/foundation/text/HandleState.class", "size": 1487, "crc": -1595492330}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$$inlined$debugInspectorInfo$1.class", "size": 3249, "crc": -300990659}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$2.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$2.class", "size": 11955, "crc": -341745987}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt.class", "size": 3925, "crc": 1253726740}, {"key": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier$measure$1.class", "name": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier$measure$1.class", "size": 5227, "crc": 807536828}, {"key": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier.class", "name": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier.class", "size": 7288, "crc": 247059333}, {"key": "androidx/compose/foundation/text/InlineTextContent.class", "name": "androidx/compose/foundation/text/InlineTextContent.class", "size": 1996, "crc": -200937882}, {"key": "androidx/compose/foundation/text/InlineTextContentKt.class", "name": "androidx/compose/foundation/text/InlineTextContentKt.class", "size": 1998, "crc": -268152536}, {"key": "androidx/compose/foundation/text/InternalFoundationTextApi.class", "name": "androidx/compose/foundation/text/InternalFoundationTextApi.class", "size": 1163, "crc": -1035015864}, {"key": "androidx/compose/foundation/text/KeyCommand.class", "name": "androidx/compose/foundation/text/KeyCommand.class", "size": 4793, "crc": 1929223742}, {"key": "androidx/compose/foundation/text/KeyEventHelpers_androidKt.class", "name": "androidx/compose/foundation/text/KeyEventHelpers_androidKt.class", "size": 1392, "crc": -851180252}, {"key": "androidx/compose/foundation/text/KeyMapping.class", "name": "androidx/compose/foundation/text/KeyMapping.class", "size": 848, "crc": 1673313972}, {"key": "androidx/compose/foundation/text/KeyMappingKt$commonKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$commonKeyMapping$1.class", "size": 4741, "crc": 433378948}, {"key": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$1.class", "size": 1409, "crc": 2120127964}, {"key": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$2$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$2$1.class", "size": 3286, "crc": -1397966996}, {"key": "androidx/compose/foundation/text/KeyMappingKt.class", "name": "androidx/compose/foundation/text/KeyMappingKt.class", "size": 2026, "crc": 1915098089}, {"key": "androidx/compose/foundation/text/KeyMapping_androidKt$platformDefaultKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMapping_androidKt$platformDefaultKeyMapping$1.class", "size": 2612, "crc": -820882214}, {"key": "androidx/compose/foundation/text/KeyMapping_androidKt.class", "name": "androidx/compose/foundation/text/KeyMapping_androidKt.class", "size": 968, "crc": 1770674040}, {"key": "androidx/compose/foundation/text/KeyboardActionRunner.class", "name": "androidx/compose/foundation/text/KeyboardActionRunner.class", "size": 4707, "crc": 1616293432}, {"key": "androidx/compose/foundation/text/KeyboardActionScope.class", "name": "androidx/compose/foundation/text/KeyboardActionScope.class", "size": 626, "crc": 649920333}, {"key": "androidx/compose/foundation/text/KeyboardActions$Companion.class", "name": "androidx/compose/foundation/text/KeyboardActions$Companion.class", "size": 1338, "crc": -302763869}, {"key": "androidx/compose/foundation/text/KeyboardActions.class", "name": "androidx/compose/foundation/text/KeyboardActions.class", "size": 5335, "crc": -2076690214}, {"key": "androidx/compose/foundation/text/KeyboardActionsKt.class", "name": "androidx/compose/foundation/text/KeyboardActionsKt.class", "size": 1452, "crc": 165450875}, {"key": "androidx/compose/foundation/text/KeyboardOptions$Companion.class", "name": "androidx/compose/foundation/text/KeyboardOptions$Companion.class", "size": 1649, "crc": 518149076}, {"key": "androidx/compose/foundation/text/KeyboardOptions.class", "name": "androidx/compose/foundation/text/KeyboardOptions.class", "size": 20137, "crc": -1295177137}, {"key": "androidx/compose/foundation/text/LegacyTextFieldState$onImeActionPerformed$1.class", "name": "androidx/compose/foundation/text/LegacyTextFieldState$onImeActionPerformed$1.class", "size": 1948, "crc": -687384851}, {"key": "androidx/compose/foundation/text/LegacyTextFieldState$onValueChange$1.class", "name": "androidx/compose/foundation/text/LegacyTextFieldState$onValueChange$1.class", "size": 3045, "crc": 1362433739}, {"key": "androidx/compose/foundation/text/LegacyTextFieldState$onValueChangeOriginal$1.class", "name": "androidx/compose/foundation/text/LegacyTextFieldState$onValueChangeOriginal$1.class", "size": 1687, "crc": 99644165}, {"key": "androidx/compose/foundation/text/LegacyTextFieldState.class", "name": "androidx/compose/foundation/text/LegacyTextFieldState.class", "size": 19697, "crc": -15003011}, {"key": "androidx/compose/foundation/text/LinksTextMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/text/LinksTextMeasurePolicy$measure$1.class", "size": 4487, "crc": 896583205}, {"key": "androidx/compose/foundation/text/LinksTextMeasurePolicy.class", "name": "androidx/compose/foundation/text/LinksTextMeasurePolicy.class", "size": 2706, "crc": -798990132}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$1.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$1.class", "size": 4155, "crc": -1897753269}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$2.class", "size": 4152, "crc": 2049367686}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2.class", "size": 4712, "crc": -1621994748}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$2.class", "size": 1885, "crc": -1494423069}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$3.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$3.class", "size": 1599, "crc": -233424335}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$4.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$4.class", "size": 1601, "crc": 1036111358}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$5.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$5.class", "size": 2294, "crc": 757417382}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$2.class", "size": 1843, "crc": -1991008537}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$3.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$3.class", "size": 1557, "crc": -1244337127}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$4.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$4.class", "size": 1559, "crc": -1770940966}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$5.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$5.class", "size": 2252, "crc": -1306979310}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectPreDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectPreDragGesturesWithObserver$2.class", "size": 7287, "crc": 996770345}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt.class", "size": 5063, "crc": -968341614}, {"key": "androidx/compose/foundation/text/MappedKeys.class", "name": "androidx/compose/foundation/text/MappedKeys.class", "size": 4712, "crc": -1000557039}, {"key": "androidx/compose/foundation/text/PasswordInputTransformation.class", "name": "androidx/compose/foundation/text/PasswordInputTransformation.class", "size": 4585, "crc": -2129750939}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2$1.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2$1.class", "size": 6888, "crc": -615670272}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2.class", "size": 5621, "crc": 1327377946}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt.class", "size": 2665, "crc": 2013361639}, {"key": "androidx/compose/foundation/text/SecureTextFieldController$focusChangeModifier$1.class", "name": "androidx/compose/foundation/text/SecureTextFieldController$focusChangeModifier$1.class", "size": 1932, "crc": 1872777684}, {"key": "androidx/compose/foundation/text/SecureTextFieldController$observeHideEvents$2.class", "name": "androidx/compose/foundation/text/SecureTextFieldController$observeHideEvents$2.class", "size": 3773, "crc": -800020581}, {"key": "androidx/compose/foundation/text/SecureTextFieldController$passwordInputTransformation$1.class", "name": "androidx/compose/foundation/text/SecureTextFieldController$passwordInputTransformation$1.class", "size": 1400, "crc": 1737558911}, {"key": "androidx/compose/foundation/text/SecureTextFieldController.class", "name": "androidx/compose/foundation/text/SecureTextFieldController.class", "size": 6108, "crc": 742987395}, {"key": "androidx/compose/foundation/text/StringHelpersKt.class", "name": "androidx/compose/foundation/text/StringHelpersKt.class", "size": 1466, "crc": -148040455}, {"key": "androidx/compose/foundation/text/StringHelpers_androidKt.class", "name": "androidx/compose/foundation/text/StringHelpers_androidKt.class", "size": 3020, "crc": -1560933637}, {"key": "androidx/compose/foundation/text/StringHelpers_jvmKt.class", "name": "androidx/compose/foundation/text/StringHelpers_jvmKt.class", "size": 913, "crc": -1354221363}, {"key": "androidx/compose/foundation/text/TextAnnotatorScope.class", "name": "androidx/compose/foundation/text/TextAnnotatorScope.class", "size": 1358, "crc": 179320462}, {"key": "androidx/compose/foundation/text/TextContextMenuItems.class", "name": "androidx/compose/foundation/text/TextContextMenuItems.class", "size": 3020, "crc": -661404626}, {"key": "androidx/compose/foundation/text/TextDelegate$Companion.class", "name": "androidx/compose/foundation/text/TextDelegate$Companion.class", "size": 1417, "crc": -508981875}, {"key": "androidx/compose/foundation/text/TextDelegate.class", "name": "androidx/compose/foundation/text/TextDelegate.class", "size": 13408, "crc": 1660922344}, {"key": "androidx/compose/foundation/text/TextDelegateKt.class", "name": "androidx/compose/foundation/text/TextDelegateKt.class", "size": 5321, "crc": 795925838}, {"key": "androidx/compose/foundation/text/TextDragObserver.class", "name": "androidx/compose/foundation/text/TextDragObserver.class", "size": 859, "crc": -260211268}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1$1.class", "size": 3744, "crc": 1290791858}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$2$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$2$1.class", "size": 4466, "crc": -1892566308}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1.class", "size": 9151, "crc": -724915364}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursorAnimationSpec$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursorAnimationSpec$1.class", "size": 2175, "crc": -1324082808}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt.class", "size": 4079, "crc": 1259069239}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion$restartInput$1.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion$restartInput$1.class", "size": 3217, "crc": -10849983}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion$updateTextLayoutResult$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion$updateTextLayoutResult$1$1$1.class", "size": 2384, "crc": 622454680}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion.class", "size": 20537, "crc": -2002474220}, {"key": "androidx/compose/foundation/text/TextFieldDelegate.class", "name": "androidx/compose/foundation/text/TextFieldDelegate.class", "size": 7091, "crc": -863360757}, {"key": "androidx/compose/foundation/text/TextFieldDelegateKt.class", "name": "androidx/compose/foundation/text/TextFieldDelegateKt.class", "size": 3163, "crc": 1008476020}, {"key": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt$interceptDPadAndMoveFocus$1.class", "name": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt$interceptDPadAndMoveFocus$1.class", "size": 3743, "crc": -1720126426}, {"key": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt.class", "size": 2263, "crc": 695573982}, {"key": "androidx/compose/foundation/text/TextFieldGestureModifiersKt.class", "name": "androidx/compose/foundation/text/TextFieldGestureModifiersKt.class", "size": 2239, "crc": 1159978686}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$1.class", "size": 2005, "crc": -693307639}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$1.class", "size": 1731, "crc": 1234926106}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$2.class", "size": 1733, "crc": -1690909803}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$3.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$3.class", "size": 2186, "crc": -1994280769}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$4.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$4.class", "size": 2263, "crc": 108793900}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$5.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$5.class", "size": 2395, "crc": -320813630}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$6.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$6.class", "size": 2391, "crc": 1971019431}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$7.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$7.class", "size": 2394, "crc": 1423010913}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$8.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$8.class", "size": 2392, "crc": 1656133632}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$WhenMappings.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$WhenMappings.class", "size": 3290, "crc": 1340380974}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2.class", "size": 8400, "crc": 1916946258}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput.class", "size": 13546, "crc": -656778729}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$1.class", "size": 1948, "crc": -180739949}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2$1$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2$1$1.class", "size": 1921, "crc": -1839953841}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2.class", "size": 8488, "crc": 575100675}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt.class", "size": 3870, "crc": 1674365687}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput_androidKt.class", "size": 948, "crc": 1591826758}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1$invoke$$inlined$onDispose$1.class", "size": 3454, "crc": 1956347873}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1.class", "size": 3902, "crc": -22817151}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$1.class", "size": 5739, "crc": 2037739643}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$2.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$2.class", "size": 5676, "crc": 633306759}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1.class", "size": 5806, "crc": -1298134427}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$2.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$2.class", "size": 2127, "crc": -2108960831}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1.class", "size": 5756, "crc": 1208787486}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1.class", "size": 10219, "crc": 594415854}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt.class", "size": 2399, "crc": 1094592915}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$WhenMappings.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$WhenMappings.class", "size": 864, "crc": 470325334}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$$inlined$debugInspectorInfo$1.class", "size": 3533, "crc": 166149768}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$scrollableState$1$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$scrollableState$1$1.class", "size": 2001, "crc": 1171154149}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollBackward$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollBackward$2.class", "size": 1863, "crc": 977121718}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollForward$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollForward$2.class", "size": 1890, "crc": -304203415}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1.class", "size": 5302, "crc": -721384543}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2.class", "size": 8138, "crc": 983935626}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt.class", "size": 8532, "crc": -1507691308}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$1.class", "size": 2616, "crc": 3238806}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$2.class", "size": 2480, "crc": -976523900}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion.class", "size": 1438, "crc": -1587895236}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition.class", "size": 8775, "crc": 1782502601}, {"key": "androidx/compose/foundation/text/TextFieldSize.class", "name": "androidx/compose/foundation/text/TextFieldSize.class", "size": 4422, "crc": 974961876}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1$1.class", "size": 2089, "crc": -1779604294}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1.class", "size": 3480, "crc": 1467885751}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1.class", "size": 10336, "crc": -944697917}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt.class", "size": 1335, "crc": -373089520}, {"key": "androidx/compose/foundation/text/TextLayoutHelperKt.class", "name": "androidx/compose/foundation/text/TextLayoutHelperKt.class", "size": 4556, "crc": 1358847480}, {"key": "androidx/compose/foundation/text/TextLayoutResultProxy.class", "name": "androidx/compose/foundation/text/TextLayoutResultProxy.class", "size": 7748, "crc": 1829450573}, {"key": "androidx/compose/foundation/text/TextLayoutResultProxyKt.class", "name": "androidx/compose/foundation/text/TextLayoutResultProxyKt.class", "size": 1345, "crc": -1454498670}, {"key": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$1$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$1$1.class", "size": 2207, "crc": 2110315640}, {"key": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$2$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$2$1.class", "size": 4346, "crc": 938421242}, {"key": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$2.class", "name": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$2.class", "size": 1701, "crc": 260359844}, {"key": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$1$1$invoke$$inlined$onDispose$1.class", "size": 2489, "crc": 1902419353}, {"key": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$1$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$1$1.class", "size": 3484, "crc": 1445323513}, {"key": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$2.class", "name": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$2.class", "size": 2549, "crc": 987824225}, {"key": "androidx/compose/foundation/text/TextLinkScope$shapeForRange$1$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$shapeForRange$1$1.class", "size": 1941, "crc": 2078562829}, {"key": "androidx/compose/foundation/text/TextLinkScope$shouldMeasureLinks$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$shouldMeasureLinks$1.class", "size": 2004, "crc": -1913166517}, {"key": "androidx/compose/foundation/text/TextLinkScope$textRange$1$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$textRange$1$1.class", "size": 1440, "crc": -447043071}, {"key": "androidx/compose/foundation/text/TextLinkScope$textRange$1$layoutResult$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$textRange$1$layoutResult$1.class", "size": 1526, "crc": 1931762485}, {"key": "androidx/compose/foundation/text/TextLinkScope.class", "name": "androidx/compose/foundation/text/TextLinkScope.class", "size": 28243, "crc": 1313803164}, {"key": "androidx/compose/foundation/text/TextLinkScopeKt.class", "name": "androidx/compose/foundation/text/TextLinkScopeKt.class", "size": 478, "crc": -1297993866}, {"key": "androidx/compose/foundation/text/TextMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/text/TextMeasurePolicy$measure$1.class", "size": 4677, "crc": -755773025}, {"key": "androidx/compose/foundation/text/TextMeasurePolicy.class", "name": "androidx/compose/foundation/text/TextMeasurePolicy.class", "size": 8353, "crc": 957359097}, {"key": "androidx/compose/foundation/text/TextPointerIcon_androidKt.class", "name": "androidx/compose/foundation/text/TextPointerIcon_androidKt.class", "size": 910, "crc": 40501581}, {"key": "androidx/compose/foundation/text/TextRangeLayoutMeasureResult.class", "name": "androidx/compose/foundation/text/TextRangeLayoutMeasureResult.class", "size": 1821, "crc": -219175262}, {"key": "androidx/compose/foundation/text/TextRangeLayoutMeasureScope.class", "name": "androidx/compose/foundation/text/TextRangeLayoutMeasureScope.class", "size": 1615, "crc": 1687776558}, {"key": "androidx/compose/foundation/text/TextRangeLayoutModifier.class", "name": "androidx/compose/foundation/text/TextRangeLayoutModifier.class", "size": 1933, "crc": -25791992}, {"key": "androidx/compose/foundation/text/TextRangeScopeMeasurePolicy.class", "name": "androidx/compose/foundation/text/TextRangeScopeMeasurePolicy.class", "size": 890, "crc": -1706389575}, {"key": "androidx/compose/foundation/text/TouchMode_androidKt.class", "name": "androidx/compose/foundation/text/TouchMode_androidKt.class", "size": 789, "crc": 1838541796}, {"key": "androidx/compose/foundation/text/UndoManager$Entry.class", "name": "androidx/compose/foundation/text/UndoManager$Entry.class", "size": 2044, "crc": 838734806}, {"key": "androidx/compose/foundation/text/UndoManager.class", "name": "androidx/compose/foundation/text/UndoManager.class", "size": 4781, "crc": -1593314768}, {"key": "androidx/compose/foundation/text/UndoManagerKt.class", "name": "androidx/compose/foundation/text/UndoManagerKt.class", "size": 584, "crc": -937151512}, {"key": "androidx/compose/foundation/text/UndoManager_jvmKt.class", "name": "androidx/compose/foundation/text/UndoManager_jvmKt.class", "size": 496, "crc": 110414890}, {"key": "androidx/compose/foundation/text/ValidatingOffsetMapping.class", "name": "androidx/compose/foundation/text/ValidatingOffsetMapping.class", "size": 1962, "crc": -727379359}, {"key": "androidx/compose/foundation/text/ValidatingOffsetMappingKt.class", "name": "androidx/compose/foundation/text/ValidatingOffsetMappingKt.class", "size": 5017, "crc": -295082490}, {"key": "androidx/compose/foundation/text/VerticalScrollLayoutModifier$measure$1.class", "name": "androidx/compose/foundation/text/VerticalScrollLayoutModifier$measure$1.class", "size": 4934, "crc": 1556028010}, {"key": "androidx/compose/foundation/text/VerticalScrollLayoutModifier.class", "name": "androidx/compose/foundation/text/VerticalScrollLayoutModifier.class", "size": 7147, "crc": 231048513}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorElement.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorElement.class", "size": 3301, "crc": 1305504776}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode$composeImm$2.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode$composeImm$2.class", "size": 2018, "crc": 1813665464}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode$pointerInputNode$1.class", "size": 1909, "crc": -386144788}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode.class", "size": 4266, "crc": 1240496503}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetector_androidKt.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetector_androidKt.class", "size": 1837, "crc": 2003398367}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerElement.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerElement.class", "size": 2575, "crc": 1720938925}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$composeImm$2.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$composeImm$2.class", "size": 1980, "crc": 1245454647}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$onFocusEvent$1.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$onFocusEvent$1.class", "size": 3867, "crc": -1272301588}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode.class", "size": 3328, "crc": 1945237110}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandler_androidKt.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandler_androidKt.class", "size": 1100, "crc": 1767828872}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingElementWithNegativePadding.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingElementWithNegativePadding.class", "size": 4994, "crc": 2081296906}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingKt.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingKt.class", "size": 3160, "crc": -1345720712}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1$1.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1$1.class", "size": 10622, "crc": -889520157}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1.class", "size": 4220, "crc": 71058508}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode.class", "size": 4213, "crc": -1270419564}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNodeWithNegativePadding$measure$1.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNodeWithNegativePadding$measure$1.class", "size": 2180, "crc": -510380339}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNodeWithNegativePadding.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNodeWithNegativePadding.class", "size": 3365, "crc": 1585518924}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwriting_androidKt.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwriting_androidKt.class", "size": 748, "crc": 1148353935}, {"key": "androidx/compose/foundation/text/input/AllCapsTransformation.class", "name": "androidx/compose/foundation/text/input/AllCapsTransformation.class", "size": 5849, "crc": 72161037}, {"key": "androidx/compose/foundation/text/input/FilterChain.class", "name": "androidx/compose/foundation/text/input/FilterChain.class", "size": 4369, "crc": -3769745}, {"key": "androidx/compose/foundation/text/input/InputTransformation$Companion.class", "name": "androidx/compose/foundation/text/input/InputTransformation$Companion.class", "size": 1206, "crc": 1028287359}, {"key": "androidx/compose/foundation/text/input/InputTransformation.class", "name": "androidx/compose/foundation/text/input/InputTransformation.class", "size": 1722, "crc": 1806268638}, {"key": "androidx/compose/foundation/text/input/InputTransformationByValue.class", "name": "androidx/compose/foundation/text/input/InputTransformationByValue.class", "size": 4754, "crc": 1339021536}, {"key": "androidx/compose/foundation/text/input/InputTransformationKt.class", "name": "androidx/compose/foundation/text/input/InputTransformationKt.class", "size": 3048, "crc": 10692649}, {"key": "androidx/compose/foundation/text/input/KeyboardActionHandler.class", "name": "androidx/compose/foundation/text/input/KeyboardActionHandler.class", "size": 853, "crc": 946449923}, {"key": "androidx/compose/foundation/text/input/MaxLengthFilter.class", "name": "androidx/compose/foundation/text/input/MaxLengthFilter.class", "size": 3778, "crc": -2065091287}, {"key": "androidx/compose/foundation/text/input/OutputTransformation.class", "name": "androidx/compose/foundation/text/input/OutputTransformation.class", "size": 799, "crc": -1267164402}, {"key": "androidx/compose/foundation/text/input/TextFieldBuffer$ChangeList.class", "name": "androidx/compose/foundation/text/input/TextFieldBuffer$ChangeList.class", "size": 1006, "crc": 1439513397}, {"key": "androidx/compose/foundation/text/input/TextFieldBuffer.class", "name": "androidx/compose/foundation/text/input/TextFieldBuffer.class", "size": 14003, "crc": -1612795467}, {"key": "androidx/compose/foundation/text/input/TextFieldBufferKt.class", "name": "androidx/compose/foundation/text/input/TextFieldBufferKt.class", "size": 5102, "crc": -260247913}, {"key": "androidx/compose/foundation/text/input/TextFieldCharSequence.class", "name": "androidx/compose/foundation/text/input/TextFieldCharSequence.class", "size": 6376, "crc": 373315070}, {"key": "androidx/compose/foundation/text/input/TextFieldCharSequenceKt.class", "name": "androidx/compose/foundation/text/input/TextFieldCharSequenceKt.class", "size": 1706, "crc": -352524760}, {"key": "androidx/compose/foundation/text/input/TextFieldDecorator.class", "name": "androidx/compose/foundation/text/input/TextFieldDecorator.class", "size": 1034, "crc": -38991344}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits$Companion.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits$Companion.class", "size": 1340, "crc": -198534888}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits$MultiLine.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits$MultiLine.class", "size": 2892, "crc": -807518171}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits$SingleLine.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits$SingleLine.class", "size": 1207, "crc": 1909198724}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits.class", "size": 1236, "crc": -1825816358}, {"key": "androidx/compose/foundation/text/input/TextFieldState$NotifyImeListener.class", "name": "androidx/compose/foundation/text/input/TextFieldState$NotifyImeListener.class", "size": 1019, "crc": 1259028299}, {"key": "androidx/compose/foundation/text/input/TextFieldState$Saver.class", "name": "androidx/compose/foundation/text/input/TextFieldState$Saver.class", "size": 4514, "crc": -1611280595}, {"key": "androidx/compose/foundation/text/input/TextFieldState$WhenMappings.class", "name": "androidx/compose/foundation/text/input/TextFieldState$WhenMappings.class", "size": 1018, "crc": -528882511}, {"key": "androidx/compose/foundation/text/input/TextFieldState.class", "name": "androidx/compose/foundation/text/input/TextFieldState.class", "size": 22858, "crc": -148888214}, {"key": "androidx/compose/foundation/text/input/TextFieldStateKt$rememberTextFieldState$1$1.class", "name": "androidx/compose/foundation/text/input/TextFieldStateKt$rememberTextFieldState$1$1.class", "size": 1718, "crc": 1984535389}, {"key": "androidx/compose/foundation/text/input/TextFieldStateKt.class", "name": "androidx/compose/foundation/text/input/TextFieldStateKt.class", "size": 7764, "crc": -1781504743}, {"key": "androidx/compose/foundation/text/input/TextHighlightType$Companion.class", "name": "androidx/compose/foundation/text/input/TextHighlightType$Companion.class", "size": 1417, "crc": 214979739}, {"key": "androidx/compose/foundation/text/input/TextHighlightType.class", "name": "androidx/compose/foundation/text/input/TextHighlightType.class", "size": 2819, "crc": -1354138551}, {"key": "androidx/compose/foundation/text/input/TextObfuscationMode$Companion.class", "name": "androidx/compose/foundation/text/input/TextObfuscationMode$Companion.class", "size": 1503, "crc": 1960556349}, {"key": "androidx/compose/foundation/text/input/TextObfuscationMode.class", "name": "androidx/compose/foundation/text/input/TextObfuscationMode.class", "size": 2954, "crc": 122982813}, {"key": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver$special$$inlined$createSaver$1.class", "name": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver$special$$inlined$createSaver$1.class", "size": 7089, "crc": -2010887503}, {"key": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver.class", "name": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver.class", "size": 6625, "crc": 943307485}, {"key": "androidx/compose/foundation/text/input/TextUndoManager$Companion.class", "name": "androidx/compose/foundation/text/input/TextUndoManager$Companion.class", "size": 975, "crc": -85659557}, {"key": "androidx/compose/foundation/text/input/TextUndoManager.class", "name": "androidx/compose/foundation/text/input/TextUndoManager.class", "size": 9173, "crc": 941786007}, {"key": "androidx/compose/foundation/text/input/TextUndoManagerKt.class", "name": "androidx/compose/foundation/text/input/TextUndoManagerKt.class", "size": 5500, "crc": -897941745}, {"key": "androidx/compose/foundation/text/input/UndoState.class", "name": "androidx/compose/foundation/text/input/UndoState.class", "size": 2068, "crc": -1066137133}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$1.class", "size": 3977, "crc": 967832966}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$1.class", "size": 1745, "crc": -1750327442}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$2.class", "size": 2381, "crc": -1923314575}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1.class", "size": 5500, "crc": 423256287}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$request$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$request$1.class", "size": 2759, "crc": -909299574}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1.class", "size": 7788, "crc": -1842572527}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2.class", "size": 5710, "crc": -418911318}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter.class", "size": 10410, "crc": 1317286361}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextFieldKeyEventHandler.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextFieldKeyEventHandler.class", "size": 5121, "crc": 1554748108}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$1.class", "size": 2213, "crc": 1750032508}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2.class", "size": 2289, "crc": -56183730}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$1.class", "size": 6752, "crc": -1778327961}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$1.class", "size": 1654, "crc": 33292715}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$2.class", "size": 2303, "crc": -2109269674}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1.class", "size": 5082, "crc": -32848063}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$1.class", "size": 1989, "crc": -1811421925}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$textInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$textInputSession$1.class", "size": 10314, "crc": 977376578}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3.class", "size": 13895, "crc": 969801011}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt.class", "size": 9493, "crc": 215323459}, {"key": "androidx/compose/foundation/text/input/internal/Api25CommitContentImpl.class", "name": "androidx/compose/foundation/text/input/internal/Api25CommitContentImpl.class", "size": 1634, "crc": -467926302}, {"key": "androidx/compose/foundation/text/input/internal/Api34LegacyPerformHandwritingGestureImpl.class", "name": "androidx/compose/foundation/text/input/internal/Api34LegacyPerformHandwritingGestureImpl.class", "size": 4997, "crc": -1966182125}, {"key": "androidx/compose/foundation/text/input/internal/Api34PerformHandwritingGestureImpl.class", "name": "androidx/compose/foundation/text/input/internal/Api34PerformHandwritingGestureImpl.class", "size": 3252, "crc": 1539574496}, {"key": "androidx/compose/foundation/text/input/internal/Api34StartStylusHandwriting.class", "name": "androidx/compose/foundation/text/input/internal/Api34StartStylusHandwriting.class", "size": 1467, "crc": -1551453614}, {"key": "androidx/compose/foundation/text/input/internal/ChangeTracker$Change.class", "name": "androidx/compose/foundation/text/input/internal/ChangeTracker$Change.class", "size": 3715, "crc": -1347257440}, {"key": "androidx/compose/foundation/text/input/internal/ChangeTracker.class", "name": "androidx/compose/foundation/text/input/internal/ChangeTracker.class", "size": 8830, "crc": -2114267568}, {"key": "androidx/compose/foundation/text/input/internal/CodepointHelpers_jvmKt.class", "name": "androidx/compose/foundation/text/input/internal/CodepointHelpers_jvmKt.class", "size": 1048, "crc": 1437411471}, {"key": "androidx/compose/foundation/text/input/internal/CodepointTransformation$Companion.class", "name": "androidx/compose/foundation/text/input/internal/CodepointTransformation$Companion.class", "size": 833, "crc": -481941253}, {"key": "androidx/compose/foundation/text/input/internal/CodepointTransformation.class", "name": "androidx/compose/foundation/text/input/internal/CodepointTransformation.class", "size": 1030, "crc": -38919837}, {"key": "androidx/compose/foundation/text/input/internal/CodepointTransformationKt.class", "name": "androidx/compose/foundation/text/input/internal/CodepointTransformationKt.class", "size": 3702, "crc": -1137859245}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager.class", "size": 1545, "crc": -1341112612}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImpl.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImpl.class", "size": 4712, "crc": 173505014}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi21.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi21.class", "size": 2351, "crc": 2056345850}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi24.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi24.class", "size": 1535, "crc": -1282026159}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi34.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi34.class", "size": 1651, "crc": -1754925308}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt$ComposeInputMethodManagerFactory$1.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt$ComposeInputMethodManagerFactory$1.class", "size": 2344, "crc": 1584361536}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt.class", "size": 2417, "crc": -1365546454}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi33Helper.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi33Helper.class", "size": 2289, "crc": 1700712182}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi34Helper.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi34Helper.class", "size": 2404, "crc": -1141042472}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoBuilder_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoBuilder_androidKt.class", "size": 7212, "crc": 1768546951}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$1.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$1.class", "size": 1864, "crc": -753996152}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$2.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$2.class", "size": 2602, "crc": 1895214963}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1.class", "size": 4642, "crc": 2102991549}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController.class", "size": 10278, "crc": 1593537346}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2$1.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2$1.class", "size": 4468, "crc": 854270616}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2.class", "size": 4711, "crc": 880436543}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnimationState.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnimationState.class", "size": 5032, "crc": -1248581985}, {"key": "androidx/compose/foundation/text/input/internal/EditCommandKt.class", "name": "androidx/compose/foundation/text/input/internal/EditCommandKt.class", "size": 7384, "crc": -1124405980}, {"key": "androidx/compose/foundation/text/input/internal/EditingBuffer$Companion.class", "name": "androidx/compose/foundation/text/input/internal/EditingBuffer$Companion.class", "size": 938, "crc": -218209728}, {"key": "androidx/compose/foundation/text/input/internal/EditingBuffer.class", "name": "androidx/compose/foundation/text/input/internal/EditingBuffer.class", "size": 10907, "crc": -1990848890}, {"key": "androidx/compose/foundation/text/input/internal/EditingBufferKt.class", "name": "androidx/compose/foundation/text/input/internal/EditingBufferKt.class", "size": 1226, "crc": -1365392432}, {"key": "androidx/compose/foundation/text/input/internal/EditorInfoApi34.class", "name": "androidx/compose/foundation/text/input/internal/EditorInfoApi34.class", "size": 1968, "crc": 189144495}, {"key": "androidx/compose/foundation/text/input/internal/EditorInfo_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/EditorInfo_androidKt.class", "size": 6208, "crc": -412014927}, {"key": "androidx/compose/foundation/text/input/internal/GapBuffer.class", "name": "androidx/compose/foundation/text/input/internal/GapBuffer.class", "size": 4405, "crc": -273043396}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34$performRemoveSpaceGesture$newText$1.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34$performRemoveSpaceGesture$newText$1.class", "size": 2298, "crc": 1309593853}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34$performRemoveSpaceGesture$newText$2.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34$performRemoveSpaceGesture$newText$2.class", "size": 2287, "crc": -6483375}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34.class", "size": 38553, "crc": -1043580239}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt$compoundEditCommand$1.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt$compoundEditCommand$1.class", "size": 1598, "crc": 1543549972}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt.class", "size": 15452, "crc": -820344598}, {"key": "androidx/compose/foundation/text/input/internal/IndexTransformationType.class", "name": "androidx/compose/foundation/text/input/internal/IndexTransformationType.class", "size": 1737, "crc": -1009163927}, {"key": "androidx/compose/foundation/text/input/internal/InputEventCallback2.class", "name": "androidx/compose/foundation/text/input/internal/InputEventCallback2.class", "size": 1620, "crc": -1267084069}, {"key": "androidx/compose/foundation/text/input/internal/InputMethodManager.class", "name": "androidx/compose/foundation/text/input/internal/InputMethodManager.class", "size": 1335, "crc": -14416065}, {"key": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl$imm$2.class", "name": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl$imm$2.class", "size": 2059, "crc": -426495762}, {"key": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl.class", "name": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl.class", "size": 4517, "crc": -826230442}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifier.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifier.class", "size": 6630, "crc": 589979876}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode$launchTextInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode$launchTextInputSession$1.class", "size": 4692, "crc": -72021291}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode.class", "size": 9444, "crc": 1375399496}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNodeKt.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNodeKt.class", "size": 1913, "crc": -297213995}, {"key": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoBuilder_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoBuilder_androidKt.class", "size": 8061, "crc": -1600512795}, {"key": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoController.class", "name": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoController.class", "size": 6761, "crc": 1687691684}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter$LegacyPlatformTextInputNode.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter$LegacyPlatformTextInputNode.class", "size": 2647, "crc": 1889716684}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter.class", "size": 3960, "crc": 689360262}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt$inputMethodManagerFactory$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt$inputMethodManagerFactory$1.class", "size": 1857, "crc": -2145931226}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt.class", "size": 3340, "crc": -767998767}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$baseInputConnection$2.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$baseInputConnection$2.class", "size": 1808, "crc": 1607620534}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$createInputConnection$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$createInputConnection$1.class", "size": 4499, "crc": -169469188}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$onEditCommand$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$onEditCommand$1.class", "size": 1806, "crc": -518514985}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$onImeActionPerformed$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$onImeActionPerformed$1.class", "size": 1661, "crc": 1712647262}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest.class", "size": 15464, "crc": 1107518310}, {"key": "androidx/compose/foundation/text/input/internal/LocaleListHelper.class", "name": "androidx/compose/foundation/text/input/internal/LocaleListHelper.class", "size": 4362, "crc": -1652859923}, {"key": "androidx/compose/foundation/text/input/internal/MaskCodepointTransformation.class", "name": "androidx/compose/foundation/text/input/internal/MaskCodepointTransformation.class", "size": 2530, "crc": 909791109}, {"key": "androidx/compose/foundation/text/input/internal/MathUtilsKt.class", "name": "androidx/compose/foundation/text/input/internal/MathUtilsKt.class", "size": 3964, "crc": -2101640773}, {"key": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculator.class", "name": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculator.class", "size": 5724, "crc": -1634089484}, {"key": "androidx/compose/foundation/text/input/internal/OpArray$Companion.class", "name": "androidx/compose/foundation/text/input/internal/OpArray$Companion.class", "size": 934, "crc": -1571008323}, {"key": "androidx/compose/foundation/text/input/internal/OpArray.class", "name": "androidx/compose/foundation/text/input/internal/OpArray.class", "size": 5268, "crc": -213922345}, {"key": "androidx/compose/foundation/text/input/internal/PartialGapBuffer$Companion.class", "name": "androidx/compose/foundation/text/input/internal/PartialGapBuffer$Companion.class", "size": 1018, "crc": 976629539}, {"key": "androidx/compose/foundation/text/input/internal/PartialGapBuffer.class", "name": "androidx/compose/foundation/text/input/internal/PartialGapBuffer.class", "size": 6089, "crc": -1391582953}, {"key": "androidx/compose/foundation/text/input/internal/RecordingInputConnection$performHandwritingGesture$1.class", "name": "androidx/compose/foundation/text/input/internal/RecordingInputConnection$performHandwritingGesture$1.class", "size": 2058, "crc": -1988842076}, {"key": "androidx/compose/foundation/text/input/internal/RecordingInputConnection.class", "name": "androidx/compose/foundation/text/input/internal/RecordingInputConnection.class", "size": 23662, "crc": -142488214}, {"key": "androidx/compose/foundation/text/input/internal/RecordingInputConnection_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/RecordingInputConnection_androidKt.class", "size": 2071, "crc": 1506768174}, {"key": "androidx/compose/foundation/text/input/internal/SelectionWedgeAffinity.class", "name": "androidx/compose/foundation/text/input/internal/SelectionWedgeAffinity.class", "size": 3577, "crc": 1039452802}, {"key": "androidx/compose/foundation/text/input/internal/SingleLineCodepointTransformation.class", "name": "androidx/compose/foundation/text/input/internal/SingleLineCodepointTransformation.class", "size": 1696, "crc": 1267887986}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$commitContentDelegateInputConnection$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$commitContentDelegateInputConnection$1.class", "size": 3860, "crc": -945234305}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$commitText$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$commitText$1.class", "size": 2057, "crc": 1280783836}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$deleteSurroundingText$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$deleteSurroundingText$1.class", "size": 1916, "crc": 1411438516}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$deleteSurroundingTextInCodePoints$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$deleteSurroundingTextInCodePoints$1.class", "size": 1952, "crc": -1082974221}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$endBatchEditInternal$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$endBatchEditInternal$1.class", "size": 3688, "crc": -1103420475}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$finishComposingText$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$finishComposingText$1.class", "size": 1802, "crc": -1449369388}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$performContextMenuAction$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$performContextMenuAction$1.class", "size": 2173, "crc": -1645164976}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$setComposingRegion$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$setComposingRegion$1.class", "size": 1892, "crc": -1145815024}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$setComposingText$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$setComposingText$1.class", "size": 2075, "crc": 634879601}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$setSelection$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$setSelection$1.class", "size": 1736, "crc": 448732026}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$terminalInputConnection$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$terminalInputConnection$1.class", "size": 2021, "crc": -213802972}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection.class", "size": 18689, "crc": 2120255389}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection_androidKt.class", "size": 4609, "crc": -1595284237}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifier.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifier.class", "size": 8609, "crc": 1697076654}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierKt.class", "size": 3813, "crc": 1203492853}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$measureHorizontalScroll$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$measureHorizontalScroll$1.class", "size": 3653, "crc": -1427529412}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$measureVerticalScroll$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$measureVerticalScroll$1.class", "size": 3647, "crc": 2128856776}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$1.class", "size": 3675, "crc": 1623875213}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$2.class", "size": 4179, "crc": 773049865}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1.class", "size": 4548, "crc": 355031230}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$updateScrollState$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$updateScrollState$1.class", "size": 5110, "crc": 391412078}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode.class", "size": 24788, "crc": 1861357049}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifier.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifier.class", "size": 9583, "crc": -1320018337}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierKt.class", "size": 1602, "crc": 745570743}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$1.class", "size": 3095, "crc": -1990051059}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$10.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$10.class", "size": 1831, "crc": -879846537}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$2.class", "size": 2352, "crc": 174640258}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$3.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$3.class", "size": 2548, "crc": 923617440}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$4.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$4.class", "size": 3714, "crc": -161359127}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$5.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$5.class", "size": 1700, "crc": 1212291204}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$6.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$6.class", "size": 2284, "crc": 627104049}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$7.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$7.class", "size": 2478, "crc": -500704771}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$8.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$8.class", "size": 1942, "crc": 1255716733}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$9.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$9.class", "size": 1827, "crc": -718583313}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$1.class", "size": 2838, "crc": 435140274}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$2.class", "size": 6152, "crc": 93893457}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$3.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$3.class", "size": 2884, "crc": 1421141863}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$4.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$4.class", "size": 3815, "crc": 289256604}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$5.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$5.class", "size": 3460, "crc": 2111921664}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$6.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$6.class", "size": 3220, "crc": -1983473273}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$7.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$7.class", "size": 2283, "crc": 700759956}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$keyboardActionScope$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$keyboardActionScope$1.class", "size": 3669, "crc": -1538950683}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onFocusChange$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onFocusChange$1.class", "size": 3977, "crc": 1776913141}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onImeActionPerformed$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onImeActionPerformed$1.class", "size": 1871, "crc": -323580315}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onKeyEvent$1.class", "size": 1773, "crc": -712258672}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onObservedReadsChanged$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onObservedReadsChanged$1.class", "size": 2176, "crc": -417934315}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$1.class", "size": 4310, "crc": -614841557}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2$1.class", "size": 2399, "crc": 442263814}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2.class", "size": 5450, "crc": 980726779}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$3.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$3.class", "size": 4583, "crc": 624032048}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$requestFocus$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$requestFocus$1.class", "size": 2194, "crc": 739717089}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1.class", "size": 6313, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1.class", "size": 4782, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$receiveContentConfigurationProvider$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$receiveContentConfigurationProvider$1.class", "size": 2446, "crc": -76709171}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$1.class", "size": 1915, "crc": 762744148}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1.class", "size": 6467, "crc": **********}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1.class", "size": 4508, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$stylusHandwritingNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$stylusHandwritingNode$1.class", "size": 3302, "crc": -2130159416}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode.class", "size": 34382, "crc": 1486511958}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$1.class", "size": 5071, "crc": -310318469}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$2.class", "size": 6071, "crc": -2056233540}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt.class", "size": 4909, "crc": -261270069}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$WhenMappings.class", "size": 3313, "crc": -1110659369}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$onKeyEvent$2$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$onKeyEvent$2$1.class", "size": 2180, "crc": 802161968}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$onKeyEvent$2$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$onKeyEvent$2$2.class", "size": 2182, "crc": 875901578}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler.class", "size": 23651, "crc": 1020410454}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler_androidKt.class", "size": 1989, "crc": 786409720}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$CacheRecord.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$CacheRecord.class", "size": 7063, "crc": -902725156}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion$mutationPolicy$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion$mutationPolicy$1.class", "size": 2652, "crc": 24925825}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion.class", "size": 1725, "crc": -1746440102}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs.class", "size": 4768, "crc": 20440652}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion$mutationPolicy$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion$mutationPolicy$1.class", "size": 2403, "crc": 1844577913}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion.class", "size": 1743, "crc": 1955593187}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs.class", "size": 3591, "crc": -403269064}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache.class", "size": 21594, "crc": 571364389}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifier.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifier.class", "size": 8182, "crc": -2062159299}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode$measure$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode$measure$1.class", "size": 2048, "crc": 341580609}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode.class", "size": 9610, "crc": **********}, {"key": "androidx/compose/foundation/text/input/internal/TextInputSession.class", "name": "androidx/compose/foundation/text/input/internal/TextInputSession.class", "size": 2358, "crc": **********}, {"key": "androidx/compose/foundation/text/input/internal/TextLayoutState$layoutWithNewMeasureInputs$1$textLayoutProvider$1.class", "name": "androidx/compose/foundation/text/input/internal/TextLayoutState$layoutWithNewMeasureInputs$1$textLayoutProvider$1.class", "size": 2117, "crc": -380274539}, {"key": "androidx/compose/foundation/text/input/internal/TextLayoutState.class", "name": "androidx/compose/foundation/text/input/internal/TextLayoutState.class", "size": 12685, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/TextLayoutStateKt.class", "name": "androidx/compose/foundation/text/input/internal/TextLayoutStateKt.class", "size": 4366, "crc": 778919807}, {"key": "androidx/compose/foundation/text/input/internal/ToCharArray_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/ToCharArray_androidKt.class", "size": 1158, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion$WhenMappings.class", "size": 1068, "crc": **********}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion.class", "size": 11012, "crc": 1393189136}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$TransformedText.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$TransformedText.class", "size": 3818, "crc": -1861210581}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$codepointTransformedText$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$codepointTransformedText$1$1.class", "size": 3804, "crc": -856466291}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$1.class", "size": 2304, "crc": -1912800657}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$2$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$2$1.class", "size": 2493, "crc": -2091552660}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$outputTransformedText$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$outputTransformedText$1$1.class", "size": 3352, "crc": 1742347797}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState.class", "size": 29610, "crc": 396077034}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldStateKt.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldStateKt.class", "size": 2511, "crc": -664119797}, {"key": "androidx/compose/foundation/text/input/internal/WedgeAffinity.class", "name": "androidx/compose/foundation/text/input/internal/WedgeAffinity.class", "size": 1532, "crc": -851115800}, {"key": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt$textFieldMagnifierNode$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt$textFieldMagnifierNode$1.class", "size": 2201, "crc": -1330134945}, {"key": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt.class", "size": 2451, "crc": -1752347673}, {"key": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt$detectPressDownGesture$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt$detectPressDownGesture$2.class", "size": 7656, "crc": -576503624}, {"key": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt.class", "size": 2696, "crc": 390167339}, {"key": "androidx/compose/foundation/text/input/internal/selection/TapOnPosition.class", "name": "androidx/compose/foundation/text/input/internal/selection/TapOnPosition.class", "size": 618, "crc": 1322070825}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState$Companion.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState$Companion.class", "size": 1356, "crc": -100770651}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState.class", "size": 5277, "crc": 277559438}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt$WhenMappings.class", "size": 950, "crc": 1363544662}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt.class", "size": 6050, "crc": -1894321087}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNode.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNode.class", "size": 2750, "crc": -1421827840}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$1.class", "size": 2421, "crc": 1159779296}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$2.class", "size": 2973, "crc": -1321118774}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$1.class", "size": 3756, "crc": -1249879611}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2$1.class", "size": 4956, "crc": 2022251714}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2.class", "size": 3651, "crc": -75530610}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1.class", "size": 4772, "crc": -1504552308}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28.class", "size": 12126, "crc": 1742516564}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelection$Companion.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelection$Companion.class", "size": 1024, "crc": 1583960697}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelection.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelection.class", "size": 27463, "crc": -1996931314}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelectionState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelectionState.class", "size": 1224, "crc": -259361534}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$InputType.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$InputType.class", "size": 1896, "crc": 270114802}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onDrag$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onDrag$1.class", "size": 1883, "crc": 1796043259}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onDragDone$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onDragDone$1.class", "size": 1547, "crc": -1853111704}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onExtend$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onExtend$1.class", "size": 1554, "crc": 1116689143}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onExtendDrag$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onExtendDrag$1.class", "size": 1570, "crc": 866393525}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onStart$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onStart$1.class", "size": 1614, "crc": -1963484761}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver.class", "size": 8751, "crc": -955606387}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onDrag$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onDrag$1.class", "size": 1798, "crc": 185187114}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onDragStop$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onDragStop$1.class", "size": 1523, "crc": -968973739}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onStart$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onStart$1.class", "size": 1813, "crc": 725020192}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver.class", "size": 10775, "crc": -1824271079}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$WhenMappings.class", "size": 1106, "crc": -87607797}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$1.class", "size": 4194, "crc": -475506536}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$2.class", "size": 4299, "crc": -2140137034}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3$1.class", "size": 2622, "crc": -1820583105}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3.class", "size": 4600, "crc": -285797257}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2.class", "size": 5101, "crc": -1006839810}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$1.class", "size": 2485, "crc": 778558725}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$2.class", "size": 3010, "crc": -245028797}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$3.class", "size": 1923, "crc": -996449508}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$4.class", "size": 1923, "crc": -1613749729}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$5.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$5.class", "size": 3669, "crc": -355308513}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$1.class", "size": 2539, "crc": 539918613}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$2.class", "size": 3050, "crc": 1202408158}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$3.class", "size": 1942, "crc": -560960608}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$4.class", "size": 1942, "crc": -1214476714}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$5.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$5.class", "size": 5156, "crc": -424882363}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$6.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$6.class", "size": 2144, "crc": 1376615548}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1$1.class", "size": 5981, "crc": -864052128}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1.class", "size": 6975, "crc": 484335645}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2.class", "size": 5311, "crc": 62651870}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3$1.class", "size": 1404, "crc": 118258079}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3.class", "size": 4835, "crc": -545922434}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTouchMode$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTouchMode$2.class", "size": 4788, "crc": -1048691709}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$menuItem$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$menuItem$1.class", "size": 2801, "crc": -1558277201}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeChanges$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeChanges$1.class", "size": 2049, "crc": 1682522531}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeChanges$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeChanges$2$1.class", "size": 3972, "crc": -60774775}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeChanges$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeChanges$2$2.class", "size": 3982, "crc": 523507750}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeChanges$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeChanges$2.class", "size": 4324, "crc": -410906938}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$2.class", "size": 1980, "crc": 734834197}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$3.class", "size": 2107, "crc": -674332470}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$4.class", "size": 2680, "crc": 1670084758}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$2.class", "size": 5483, "crc": -523193311}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$3.class", "size": 2705, "crc": -500281205}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$1.class", "size": 4212, "crc": 487119225}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$1.class", "size": 2361, "crc": 912746302}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$2.class", "size": 1754, "crc": -1470748317}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2.class", "size": 4970, "crc": 936657241}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$3.class", "size": 4386, "crc": 2061792568}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2.class", "size": 5378, "crc": -382376746}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$showTextToolbar$$inlined$menuItem$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$showTextToolbar$$inlined$menuItem$1.class", "size": 3211, "crc": -1447922143}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$showTextToolbar$$inlined$menuItem$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$showTextToolbar$$inlined$menuItem$2.class", "size": 3092, "crc": 77402364}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$showTextToolbar$$inlined$menuItem$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$showTextToolbar$$inlined$menuItem$3.class", "size": 3090, "crc": -1922430503}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$showTextToolbar$$inlined$menuItem$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$showTextToolbar$$inlined$menuItem$4.class", "size": 3105, "crc": -1444684473}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState.class", "size": 59796, "crc": -601906038}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt.class", "size": 1587, "crc": 849204188}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "size": 3233, "crc": -314758264}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "size": 3251, "crc": -1433227500}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$3.class", "size": 3235, "crc": -586497122}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$4.class", "size": 3239, "crc": 1632000285}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1.class", "size": 5505, "crc": -1035401015}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt.class", "size": 1868, "crc": 903792487}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt$WhenMappings.class", "size": 1104, "crc": -306358669}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt.class", "size": 4395, "crc": 642857573}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextToolbarState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextToolbarState.class", "size": 1677, "crc": -416644603}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextDeleteType.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextDeleteType.class", "size": 1674, "crc": 971059796}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextEditType.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextEditType.class", "size": 1609, "crc": 819515442}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextFieldEditUndoBehavior.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextFieldEditUndoBehavior.class", "size": 1713, "crc": -1469653743}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion$Saver$1.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion$Saver$1.class", "size": 3810, "crc": 1426314059}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion.class", "size": 1500, "crc": -2147400342}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation.class", "size": 5544, "crc": 957352942}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperationKt.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperationKt.class", "size": 4522, "crc": 66101238}, {"key": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion$createSaver$1.class", "name": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion$createSaver$1.class", "size": 6822, "crc": -77409845}, {"key": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion.class", "name": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion.class", "size": 1854, "crc": -300795640}, {"key": "androidx/compose/foundation/text/input/internal/undo/UndoManager.class", "name": "androidx/compose/foundation/text/input/internal/undo/UndoManager.class", "size": 5697, "crc": -1538441395}, {"key": "androidx/compose/foundation/text/modifiers/InlineDensity$Companion.class", "name": "androidx/compose/foundation/text/modifiers/InlineDensity$Companion.class", "size": 1158, "crc": 587607233}, {"key": "androidx/compose/foundation/text/modifiers/InlineDensity.class", "name": "androidx/compose/foundation/text/modifiers/InlineDensity.class", "size": 4981, "crc": 2022001143}, {"key": "androidx/compose/foundation/text/modifiers/LayoutUtilsKt.class", "name": "androidx/compose/foundation/text/modifiers/LayoutUtilsKt.class", "size": 2383, "crc": -1641107895}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer$Companion.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer$Companion.class", "size": 3915, "crc": 2128950906}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer.class", "size": 6567, "crc": 1276535164}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainerKt.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainerKt.class", "size": 1311, "crc": -319712510}, {"key": "androidx/compose/foundation/text/modifiers/ModifierUtilsKt.class", "name": "androidx/compose/foundation/text/modifiers/ModifierUtilsKt.class", "size": 1363, "crc": -953450038}, {"key": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache.class", "name": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache.class", "size": 14431, "crc": 1144452671}, {"key": "androidx/compose/foundation/text/modifiers/ParagraphLayoutCache.class", "name": "androidx/compose/foundation/text/modifiers/ParagraphLayoutCache.class", "size": 15854, "crc": 982679141}, {"key": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringElement.class", "name": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringElement.class", "size": 14618, "crc": 1710265627}, {"key": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringNode.class", "name": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringNode.class", "size": 11897, "crc": -276222639}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController$modifier$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController$modifier$1.class", "size": 1851, "crc": 830612466}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$1.class", "size": 1739, "crc": -518807121}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$2.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$2.class", "size": 1729, "crc": -1672775496}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController.class", "size": 11707, "crc": -1250495018}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$longPressDragObserver$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$longPressDragObserver$1.class", "size": 5275, "crc": -202573121}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$mouseSelectionObserver$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$mouseSelectionObserver$1.class", "size": 5812, "crc": -2014681037}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt.class", "size": 2638, "crc": -1009195321}, {"key": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams$Companion.class", "name": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams$Companion.class", "size": 1302, "crc": -565770256}, {"key": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams.class", "name": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams.class", "size": 4216, "crc": 295690064}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringElement.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringElement.class", "size": 10364, "crc": 1621895600}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$TextSubstitutionValue.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$TextSubstitutionValue.class", "size": 5652, "crc": 1673549964}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$1.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$1.class", "size": 5528, "crc": -200700279}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$2.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$2.class", "size": 2061, "crc": -614496208}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$3.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$3.class", "size": 2357, "crc": 202120742}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$4.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$4.class", "size": 1633, "crc": -641444541}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$measure$1.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$measure$1.class", "size": 2005, "crc": 1750852582}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode.class", "size": 31298, "crc": 816439963}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNodeKt.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNodeKt.class", "size": 825, "crc": 1871618847}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleElement.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleElement.class", "size": 6195, "crc": -1765970252}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$TextSubstitutionValue.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$TextSubstitutionValue.class", "size": 5242, "crc": 1114133295}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$1.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$1.class", "size": 4152, "crc": -367958020}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$2.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$2.class", "size": 2062, "crc": -544568129}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$3.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$3.class", "size": 2105, "crc": -711691318}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$4.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$4.class", "size": 1597, "crc": -1448271898}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$measure$1.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$measure$1.class", "size": 1993, "crc": -1927833024}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode.class", "size": 23560, "crc": -1102545193}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$HandlePopup$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$HandlePopup$1.class", "size": 2549, "crc": -933670118}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$1$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$1$1$1.class", "size": 1837, "crc": 597830297}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$2$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$2$1.class", "size": 1833, "crc": -2002461627}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1.class", "size": 12841, "crc": 1074355155}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1.class", "size": 4198, "crc": -1207871495}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$2.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$2.class", "size": 2537, "crc": -1364824823}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$semanticsModifier$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$semanticsModifier$1$1.class", "size": 3406, "crc": 1546878023}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandleIcon$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandleIcon$1.class", "size": 2221, "crc": 98076334}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1$1.class", "size": 6063, "crc": -800253497}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1.class", "size": 3328, "crc": -1924403586}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1.class", "size": 6316, "crc": 110091581}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt.class", "size": 19919, "crc": -273981233}, {"key": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection$Companion.class", "name": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection$Companion.class", "size": 976, "crc": 667194799}, {"key": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection.class", "size": 26148, "crc": 791896488}, {"key": "androidx/compose/foundation/text/selection/BoundaryFunction.class", "name": "androidx/compose/foundation/text/selection/BoundaryFunction.class", "size": 851, "crc": -2107479053}, {"key": "androidx/compose/foundation/text/selection/ClicksCounter.class", "name": "androidx/compose/foundation/text/selection/ClicksCounter.class", "size": 3191, "crc": 234907597}, {"key": "androidx/compose/foundation/text/selection/CrossStatus.class", "name": "androidx/compose/foundation/text/selection/CrossStatus.class", "size": 1557, "crc": -867253216}, {"key": "androidx/compose/foundation/text/selection/Direction.class", "name": "androidx/compose/foundation/text/selection/Direction.class", "size": 1531, "crc": -16921114}, {"key": "androidx/compose/foundation/text/selection/DownResolution.class", "name": "androidx/compose/foundation/text/selection/DownResolution.class", "size": 1611, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/HandleImageCache.class", "name": "androidx/compose/foundation/text/selection/HandleImageCache.class", "size": 2166, "crc": 731980202}, {"key": "androidx/compose/foundation/text/selection/HandlePositionProvider.class", "name": "androidx/compose/foundation/text/selection/HandlePositionProvider.class", "size": 4214, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/MouseSelectionObserver.class", "name": "androidx/compose/foundation/text/selection/MouseSelectionObserver.class", "size": 1212, "crc": 772692383}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout$WhenMappings.class", "size": 957, "crc": 425878060}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout$createSubSelections$2$1.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout$createSubSelections$2$1.class", "size": 2805, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout.class", "size": 14315, "crc": -843265548}, {"key": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegate.class", "name": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegate.class", "size": 9911, "crc": 893778906}, {"key": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegateKt.class", "name": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegateKt.class", "size": 8688, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/OffsetProvider.class", "name": "androidx/compose/foundation/text/selection/OffsetProvider.class", "size": 567, "crc": -582957961}, {"key": "androidx/compose/foundation/text/selection/Selectable.class", "name": "androidx/compose/foundation/text/selection/Selectable.class", "size": 2243, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectableInfo.class", "name": "androidx/compose/foundation/text/selection/SelectableInfo.class", "size": 5861, "crc": -705641275}, {"key": "androidx/compose/foundation/text/selection/Selection$AnchorInfo.class", "name": "androidx/compose/foundation/text/selection/Selection$AnchorInfo.class", "size": 3613, "crc": -90193809}, {"key": "androidx/compose/foundation/text/selection/Selection.class", "name": "androidx/compose/foundation/text/selection/Selection.class", "size": 4883, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Paragraph$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Paragraph$1$1.class", "size": 1679, "crc": -338039184}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Word$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Word$1$1.class", "size": 1642, "crc": -1458849618}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion.class", "size": 6295, "crc": 1303738863}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment.class", "size": 1213, "crc": -346189254}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$anchorSnappedToWordBoundary$2.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$anchorSnappedToWordBoundary$2.class", "size": 3352, "crc": -726325880}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$currentRawLine$2.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$currentRawLine$2.class", "size": 2166, "crc": 818413045}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt.class", "size": 11406, "crc": -1662977764}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$DisableSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$DisableSelection$1.class", "size": 2027, "crc": -2119484539}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$1$1.class", "size": 2159, "crc": 1789656928}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$2.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$2.class", "size": 2258, "crc": 59755097}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$1$1.class", "size": 4255, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$positionProvider$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$positionProvider$1$1.class", "size": 2138, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$positionProvider$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$positionProvider$1$2.class", "size": 2136, "crc": 332460052}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1.class", "size": 10284, "crc": 97732823}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1.class", "size": 3978, "crc": -568228931}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3.class", "size": 4572, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$invoke$$inlined$onDispose$1.class", "size": 2392, "crc": -665582090}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1.class", "size": 3170, "crc": -752739345}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$5.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$5.class", "size": 2901, "crc": -192906268}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$registrarImpl$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$registrarImpl$1.class", "size": 1730, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$sam$androidx_compose_foundation_text_selection_OffsetProvider$0.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$sam$androidx_compose_foundation_text_selection_OffsetProvider$0.class", "size": 1876, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt.class", "size": 15597, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$awaitDown$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$awaitDown$1.class", "size": 1693, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$1.class", "size": 1959, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$1.class", "size": 2250, "crc": -18614018}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$2.class", "size": 2499, "crc": 1606656201}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$1.class", "size": 1983, "crc": 1257049782}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$shouldConsumeUp$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$shouldConsumeUp$1.class", "size": 2262, "crc": 1268016599}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$shouldConsumeUp$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$shouldConsumeUp$2.class", "size": 2511, "crc": -1480902946}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1$1.class", "size": 7545, "crc": 654390326}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1.class", "size": 5039, "crc": 583016205}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGesturePointerInputBtf2$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGesturePointerInputBtf2$2.class", "size": 8277, "crc": 401408090}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$1.class", "size": 1919, "crc": 1459858962}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$2.class", "size": 2165, "crc": -126226014}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$1.class", "size": 1979, "crc": 1224456721}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$dragCompletedWithUp$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$dragCompletedWithUp$1.class", "size": 2235, "crc": 768959053}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$1.class", "size": 2082, "crc": 1049871386}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1$firstDragPastSlop$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1$firstDragPastSlop$1.class", "size": 2240, "crc": 490039824}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1.class", "size": 5825, "crc": -201615960}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$dragCompletedWithUp$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$dragCompletedWithUp$1.class", "size": 2250, "crc": -1725870079}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1$1.class", "size": 4701, "crc": -2086834499}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1.class", "size": 4108, "crc": -1768870646}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt.class", "size": 26039, "crc": 1551773836}, {"key": "androidx/compose/foundation/text/selection/SelectionHandleAnchor.class", "name": "androidx/compose/foundation/text/selection/SelectionHandleAnchor.class", "size": 1606, "crc": -1063819424}, {"key": "androidx/compose/foundation/text/selection/SelectionHandleInfo.class", "name": "androidx/compose/foundation/text/selection/SelectionHandleInfo.class", "size": 4842, "crc": 1689297641}, {"key": "androidx/compose/foundation/text/selection/SelectionHandlesKt.class", "name": "androidx/compose/foundation/text/selection/SelectionHandlesKt.class", "size": 3895, "crc": -1085948611}, {"key": "androidx/compose/foundation/text/selection/SelectionLayout.class", "name": "androidx/compose/foundation/text/selection/SelectionLayout.class", "size": 2643, "crc": -1449124612}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder$WhenMappings.class", "size": 941, "crc": -1137309090}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder.class", "size": 7930, "crc": -1034121030}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt$WhenMappings.class", "size": 931, "crc": 800183188}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt$isCollapsed$1.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt$isCollapsed$1.class", "size": 2056, "crc": -8117266}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt.class", "size": 6471, "crc": 382033113}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$1.class", "size": 2046, "crc": 12623910}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$2.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$2.class", "size": 1881, "crc": 1910300747}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1$1$1.class", "size": 1781, "crc": 262962070}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1.class", "size": 6592, "crc": 1333034688}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$1.class", "size": 1853, "crc": 444109677}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2$1.class", "size": 4728, "crc": -412358632}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2.class", "size": 3524, "crc": 599844210}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1.class", "size": 5090, "crc": 407889960}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt.class", "size": 10593, "crc": 766791609}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$1.class", "size": 2020, "crc": 1599854997}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$2.class", "size": 4109, "crc": 113178172}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$3.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$3.class", "size": 3182, "crc": -320332165}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$4.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$4.class", "size": 3351, "crc": -406833031}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$5.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$5.class", "size": 1698, "crc": -2038497234}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$6.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$6.class", "size": 2054, "crc": 500120953}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$7.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$7.class", "size": 2691, "crc": 451733519}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$detectNonConsumingTap$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$detectNonConsumingTap$2.class", "size": 4706, "crc": 464661061}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$getSelectionLayout-Wko1d7g$$inlined$compareBy$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$getSelectionLayout-Wko1d7g$$inlined$compareBy$1.class", "size": 2710, "crc": 1953383348}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$handleDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$handleDragObserver$1.class", "size": 7414, "crc": 1574107574}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$1.class", "size": 1333, "crc": -1937215677}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$2.class", "size": 1729, "crc": -791276424}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$3.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$3.class", "size": 1842, "crc": 1244944778}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$4.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$4.class", "size": 1514, "crc": -1021607276}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$5.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$5.class", "size": 2064, "crc": -1634695466}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1$1.class", "size": 1745, "crc": -182310533}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1.class", "size": 4490, "crc": -1701022560}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$1.class", "size": 1789, "crc": -1751404886}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$2.class", "size": 2229, "crc": -377532560}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$1.class", "size": 1374, "crc": -1654605389}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$2.class", "size": 1332, "crc": -1868062496}, {"key": "androidx/compose/foundation/text/selection/SelectionManager.class", "name": "androidx/compose/foundation/text/selection/SelectionManager.class", "size": 47328, "crc": -1948289580}, {"key": "androidx/compose/foundation/text/selection/SelectionManagerKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionManagerKt$WhenMappings.class", "size": 914, "crc": -221529368}, {"key": "androidx/compose/foundation/text/selection/SelectionManagerKt.class", "name": "androidx/compose/foundation/text/selection/SelectionManagerKt.class", "size": 11431, "crc": -1868421036}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "size": 3056, "crc": -1984771645}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "size": 3061, "crc": -72191650}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1.class", "size": 4871, "crc": 871959783}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$1$1.class", "size": 2242, "crc": -364296319}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$1.class", "size": 2205, "crc": 1149784165}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$2.class", "size": 2560, "crc": 636149881}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1.class", "size": 3487, "crc": -1983346146}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1.class", "size": 9167, "crc": 1693032024}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt.class", "size": 3530, "crc": 642779215}, {"key": "androidx/compose/foundation/text/selection/SelectionMode$Horizontal.class", "name": "androidx/compose/foundation/text/selection/SelectionMode$Horizontal.class", "size": 1805, "crc": 1893454301}, {"key": "androidx/compose/foundation/text/selection/SelectionMode$Vertical.class", "name": "androidx/compose/foundation/text/selection/SelectionMode$Vertical.class", "size": 1800, "crc": 1084505332}, {"key": "androidx/compose/foundation/text/selection/SelectionMode.class", "name": "androidx/compose/foundation/text/selection/SelectionMode.class", "size": 3393, "crc": -816596557}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrar$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrar$Companion.class", "size": 897, "crc": 929757399}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrar.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrar.class", "size": 2914, "crc": 718486219}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$1.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$1.class", "size": 2189, "crc": -1151889513}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$2.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$2.class", "size": 1652, "crc": -1142653768}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion.class", "size": 1485, "crc": -1617519282}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$sort$1.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$sort$1.class", "size": 3038, "crc": 577229132}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl.class", "size": 18717, "crc": 1298593709}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarKt$LocalSelectionRegistrar$1.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarKt$LocalSelectionRegistrar$1.class", "size": 1404, "crc": 1114166159}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarKt.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarKt.class", "size": 2301, "crc": -1416766600}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1$1.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1$1.class", "size": 3343, "crc": -1539585603}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1.class", "size": 5778, "crc": 1611622224}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$2.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$2.class", "size": 2216, "crc": -2037504347}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt.class", "size": 7596, "crc": -512679067}, {"key": "androidx/compose/foundation/text/selection/SingleSelectionLayout$Companion.class", "name": "androidx/compose/foundation/text/selection/SingleSelectionLayout$Companion.class", "size": 1010, "crc": 192760217}, {"key": "androidx/compose/foundation/text/selection/SingleSelectionLayout.class", "name": "androidx/compose/foundation/text/selection/SingleSelectionLayout.class", "size": 6762, "crc": 2120840882}, {"key": "androidx/compose/foundation/text/selection/TextFieldPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/TextFieldPreparedSelection.class", "size": 10243, "crc": 154123358}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cursorDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cursorDragObserver$1.class", "size": 6312, "crc": 1364799169}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$handleDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$handleDragObserver$1.class", "size": 5329, "crc": 1025983469}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$mouseSelectionObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$mouseSelectionObserver$1.class", "size": 5641, "crc": -718864122}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$onValueChange$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$onValueChange$1.class", "size": 1629, "crc": -1508594931}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$copy$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$copy$1.class", "size": 1602, "crc": 791640760}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$cut$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$cut$1.class", "size": 1492, "crc": -1631094682}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$paste$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$paste$1.class", "size": 1498, "crc": 1994882966}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$selectAll$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$selectAll$1.class", "size": 1447, "crc": -914141218}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$touchSelectionObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$touchSelectionObserver$1.class", "size": 9943, "crc": 991615224}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager.class", "size": 36071, "crc": 506164537}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$1$1.class", "size": 1539, "crc": -1741180907}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$2$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$2$1.class", "size": 4202, "crc": 96888411}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$3.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$3.class", "size": 2239, "crc": -134901695}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$WhenMappings.class", "size": 941, "crc": -125497243}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt.class", "size": 12284, "crc": -1508682109}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "size": 3154, "crc": 19397148}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "size": 3163, "crc": -1739653386}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$3.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$3.class", "size": 3156, "crc": -979280746}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$4.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$4.class", "size": 3160, "crc": 1163798066}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1.class", "size": 6559, "crc": -473279505}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$1$1.class", "size": 2323, "crc": -1540731423}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$1.class", "size": 2250, "crc": -85564246}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$2.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$2.class", "size": 2614, "crc": 1161845206}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1.class", "size": 3537, "crc": -1226457356}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1.class", "size": 9414, "crc": -636877027}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt.class", "size": 3161, "crc": -904441603}, {"key": "androidx/compose/foundation/text/selection/TextPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/TextPreparedSelection.class", "size": 3041, "crc": 1685848883}, {"key": "androidx/compose/foundation/text/selection/TextPreparedSelectionState.class", "name": "androidx/compose/foundation/text/selection/TextPreparedSelectionState.class", "size": 1333, "crc": 1680326195}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColors.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColors.class", "size": 2568, "crc": 467663212}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColorsKt$LocalTextSelectionColors$1.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColorsKt$LocalTextSelectionColors$1.class", "size": 1463, "crc": -2064065517}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColorsKt.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColorsKt.class", "size": 2580, "crc": -2023159876}, {"key": "androidx/compose/foundation/text/selection/TextSelectionDelegateKt.class", "name": "androidx/compose/foundation/text/selection/TextSelectionDelegateKt.class", "size": 2573, "crc": -1272211055}, {"key": "META-INF/androidx.compose.foundation_foundation.version", "name": "META-INF/androidx.compose.foundation_foundation.version", "size": 6, "crc": 1621725393}, {"key": "META-INF/foundation_release.kotlin_module", "name": "META-INF/foundation_release.kotlin_module", "size": 6970, "crc": -723998231}]