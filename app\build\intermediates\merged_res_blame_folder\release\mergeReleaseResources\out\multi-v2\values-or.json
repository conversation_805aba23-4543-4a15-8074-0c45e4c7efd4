{"logs": [{"outputFile": "com.falaileh.nisso.app-mergeReleaseResources-54:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9360fea9cc06fc380f90ebb846dd3e6a\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "56,57", "startColumns": "4,4", "startOffsets": "5334,5419", "endColumns": "84,87", "endOffsets": "5414,5502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8dca2d17bb97bd73368ea919040cc370\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,995,1077,1147,1222,1299,1375,1458,1525", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,74,76,75,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,990,1072,1142,1217,1294,1370,1453,1520,1639"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,48,49,50,51,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3604,3701,3788,3880,3980,4066,4143,4241,4329,4416,4494,4666,4736,4811,4888,5065,5148,5215", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,74,76,75,82,66,118", "endOffsets": "3696,3783,3875,3975,4061,4138,4236,4324,4411,4489,4571,4731,4806,4883,4959,5143,5210,5329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85e1e1d8941ac4fc444c1da209e0c205\\transformed\\appcompat-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,4576", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,4661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\910260a50c4cc0fe03b922548d59c7fb\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,52", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2869,2972,3074,3177,3282,3383,3485,4964", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "2967,3069,3172,3277,3378,3480,3599,5060"}}]}]}