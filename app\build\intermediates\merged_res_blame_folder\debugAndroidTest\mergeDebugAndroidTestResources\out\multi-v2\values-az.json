{"logs": [{"outputFile": "com.falaileh.nisso.test.app-mergeDebugAndroidTestResources-34:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\910260a50c4cc0fe03b922548d59c7fb\\transformed\\core-1.13.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,206,308,411,515,616,721,2117", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "201,303,406,510,611,716,827,2213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8dca2d17bb97bd73368ea919040cc370\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,1006,1095,1167,1241,1317,1390,1471,1537", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,1001,1090,1162,1236,1312,1385,1466,1532,1649"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,932,1020,1117,1218,1309,1390,1478,1570,1652,1733,1822,1894,1968,2044,2218,2299,2365", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "927,1015,1112,1213,1304,1385,1473,1565,1647,1728,1817,1889,1963,2039,2112,2294,2360,2477"}}]}]}