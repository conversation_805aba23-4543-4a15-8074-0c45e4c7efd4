-- Merging decision tree log ---
manifest
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml:2:1-41:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd78f57ea890de7588788587cddbfd94\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.airbnb.android:lottie-compose:6.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fa0b41867b982c746198a8fcd934979\transformed\lottie-compose-6.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d78d52374479b344b4520d482b99f827\transformed\lottie-6.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85e1e1d8941ac4fc444c1da209e0c205\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tv:tv-material:1.0.0-alpha07] C:\Users\<USER>\.gradle\caches\8.13\transforms\222b43d0dc078a9d4ee4b487cad82e7c\transformed\tv-material-1.0.0-alpha07\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.tv:tv-foundation:1.0.0-alpha07] C:\Users\<USER>\.gradle\caches\8.13\transforms\48c2a2c068bdbced5cb6a1ecc2bc07cf\transformed\tv-foundation-1.0.0-alpha07\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9f26681ff02d0a312dda2531b926043\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c13eab19914d47e72f44ea3adf9836\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710f80bc8d27a52d0f5991c4e9f01e20\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fc9f849bdbda9836ccf017647436434\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9360fea9cc06fc380f90ebb846dd3e6a\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cef652a05ff6a34fbf98efb83696a9d\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b28f39c3e2e2a42b118721980d47993d\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9333af6fa328e353e5f458bdc9962583\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\208e07ea2e340b9a3b4ef665b17067f1\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f575c49d055b9863b20b8383b5f1ce9e\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b75ff885a4387959cc1f8838035c0761\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a8c685185a220556199cd40e6eedf3\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0a355783c152e455a099310ea9e727e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a5d6db7f5a6e26ac65f19fbee79c6b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4e8a16770ee0cbecf0e234ef7e8feff\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f7b62e9c8dbbb384435a424bf2a5ac4\transformed\media3-datasource-1.0.0-beta03\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\9900e997213f6891e0a5bec4d646b758\transformed\media3-extractor-1.0.0-beta03\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-decoder:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\92f6bb34143ed66ee00abfd6d8b8373d\transformed\media3-decoder-1.0.0-beta03\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-database:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cdc7da6bf2ae70a152511d5d7d62a5\transformed\media3-database-1.0.0-beta03\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-common:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\145e061b47a116dd274ee2a14cfb8ba7\transformed\media3-common-1.0.0-beta03\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.media3:media3-exoplayer:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\54903ff84a4690cb020fc1e9d9860152\transformed\media3-exoplayer-1.0.0-beta03\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d6b21c1ae075bb005429dc4c336842\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5cc506e396f29fd258b4f5e91326bdad\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aff6c14532e3ca16ee9849ce8218bdeb\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e13264f8a5e457aa03639dd7199c5070\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b191ec88feb25e056db9fffa7904dfe4\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca317fdd63ce7c8d9f1a273065b16bcb\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cbec5eff64fe905f4db1ea5b946f7f4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c2509a0068532fe005405776069e9a\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de2dcbf3459e4a801c3dc6729a95f7e6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6a67e73b1379ea40f79c239dcad68e\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\323a85cbb5520e176b3916be7239d2f6\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf42671a75f982be109a068148eb8bcc\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\e29cad113d57be3e067bfcf14b86b427\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\482ffcf29b6e8d892d0acd21c537d2fa\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\08dbf47124c3644e56c71bd6c2399d27\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fce9a6080c7d502abe696bf08e0bfda\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\957c17e0dd04d78e6c3bf61b73005d55\transformed\lifecycle-livedata-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c85e0055dd40dbcc19d8071acc49d0e\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\e20d84cbe2db8cdbe8383e58d24cc5f7\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa97a0b9a4b07d29068ec1edd0b93c51\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d3baca6be93504d1c9a29d0327812c5\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ec27ecf4e0e2b77d47cec65bbd5d03\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8dca2d17bb97bd73368ea919040cc370\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0c054722ccb56004411aa0c49c12b57\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0714f79536ad61645fc1954b99bc485\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa5ed0abfb6055bf9790958d048b28ba\transformed\activity-compose-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\65a253d802f7d6f78bd1e5266178114d\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4838813f3df61de0850f23d52b1a4b2\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3accded29686b5491a8c71dce186ebd4\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cd99f3de52d8ee5f39e08ac44964d20\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2239f5756734f1171cfd3d2d74262e0\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1f59e9dbc22de0795ade0ed5643292\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c18230a4644677ba01fc0047afa73e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f13868df578ecea865dfe5f01741c86\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca14587393821a125b74bc2c7165a856\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6d60f7b6b6eb911416d4fe0173d9a39\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6578356e73273a33e87485d3aa259d5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1688656747656bd53f2272fa7071e970\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\145e061b47a116dd274ee2a14cfb8ba7\transformed\media3-common-1.0.0-beta03\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\145e061b47a116dd274ee2a14cfb8ba7\transformed\media3-common-1.0.0-beta03\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-exoplayer:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\54903ff84a4690cb020fc1e9d9860152\transformed\media3-exoplayer-1.0.0-beta03\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-exoplayer:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\54903ff84a4690cb020fc1e9d9860152\transformed\media3-exoplayer-1.0.0-beta03\AndroidManifest.xml:24:5-79
	android:name
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:7:22-76
uses-feature#android.hardware.touchscreen
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:10:5-12:36
	android:required
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:12:9-33
	android:name
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:11:9-52
uses-feature#android.software.leanback
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:13:5-15:35
	android:required
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:15:9-32
	android:name
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:14:9-49
uses-feature#android.hardware.wifi
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:16:5-18:35
	android:required
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:18:9-32
	android:name
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:17:9-45
application
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:20:5-39:19
INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml:20:5-39:19
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d78d52374479b344b4520d482b99f827\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d78d52374479b344b4520d482b99f827\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c18230a4644677ba01fc0047afa73e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c18230a4644677ba01fc0047afa73e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6578356e73273a33e87485d3aa259d5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6578356e73273a33e87485d3aa259d5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:25:9-35
	android:label
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:24:9-41
	android:icon
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:23:9-43
	android:allowBackup
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:21:9-35
	android:banner
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:22:9-45
	android:theme
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:26:9-43
activity#com.falaileh.nisso.MainActivity
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:27:9-38:20
	android:screenOrientation
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:30:13-50
	android:exported
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:29:13-36
	android:theme
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:31:13-58
	android:name
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:28:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:32:13-37:29
action#android.intent.action.MAIN
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:33:17-69
	android:name
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:33:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:35:17-77
	android:name
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:35:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:36:17-86
	android:name
		ADDED from E:\Nisso\app\src\main\AndroidManifest.xml:36:27-83
uses-sdk
INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml
INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd78f57ea890de7588788587cddbfd94\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd78f57ea890de7588788587cddbfd94\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.airbnb.android:lottie-compose:6.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fa0b41867b982c746198a8fcd934979\transformed\lottie-compose-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie-compose:6.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fa0b41867b982c746198a8fcd934979\transformed\lottie-compose-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d78d52374479b344b4520d482b99f827\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d78d52374479b344b4520d482b99f827\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85e1e1d8941ac4fc444c1da209e0c205\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85e1e1d8941ac4fc444c1da209e0c205\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.tv:tv-material:1.0.0-alpha07] C:\Users\<USER>\.gradle\caches\8.13\transforms\222b43d0dc078a9d4ee4b487cad82e7c\transformed\tv-material-1.0.0-alpha07\AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-material:1.0.0-alpha07] C:\Users\<USER>\.gradle\caches\8.13\transforms\222b43d0dc078a9d4ee4b487cad82e7c\transformed\tv-material-1.0.0-alpha07\AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-foundation:1.0.0-alpha07] C:\Users\<USER>\.gradle\caches\8.13\transforms\48c2a2c068bdbced5cb6a1ecc2bc07cf\transformed\tv-foundation-1.0.0-alpha07\AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-foundation:1.0.0-alpha07] C:\Users\<USER>\.gradle\caches\8.13\transforms\48c2a2c068bdbced5cb6a1ecc2bc07cf\transformed\tv-foundation-1.0.0-alpha07\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9f26681ff02d0a312dda2531b926043\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9f26681ff02d0a312dda2531b926043\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c13eab19914d47e72f44ea3adf9836\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50c13eab19914d47e72f44ea3adf9836\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710f80bc8d27a52d0f5991c4e9f01e20\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\710f80bc8d27a52d0f5991c4e9f01e20\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fc9f849bdbda9836ccf017647436434\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fc9f849bdbda9836ccf017647436434\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9360fea9cc06fc380f90ebb846dd3e6a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9360fea9cc06fc380f90ebb846dd3e6a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cef652a05ff6a34fbf98efb83696a9d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cef652a05ff6a34fbf98efb83696a9d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b28f39c3e2e2a42b118721980d47993d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b28f39c3e2e2a42b118721980d47993d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9333af6fa328e353e5f458bdc9962583\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9333af6fa328e353e5f458bdc9962583\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\208e07ea2e340b9a3b4ef665b17067f1\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\208e07ea2e340b9a3b4ef665b17067f1\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f575c49d055b9863b20b8383b5f1ce9e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f575c49d055b9863b20b8383b5f1ce9e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b75ff885a4387959cc1f8838035c0761\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b75ff885a4387959cc1f8838035c0761\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a8c685185a220556199cd40e6eedf3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\54a8c685185a220556199cd40e6eedf3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0a355783c152e455a099310ea9e727e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0a355783c152e455a099310ea9e727e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a5d6db7f5a6e26ac65f19fbee79c6b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a5d6db7f5a6e26ac65f19fbee79c6b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4e8a16770ee0cbecf0e234ef7e8feff\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4e8a16770ee0cbecf0e234ef7e8feff\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f7b62e9c8dbbb384435a424bf2a5ac4\transformed\media3-datasource-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-datasource:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f7b62e9c8dbbb384435a424bf2a5ac4\transformed\media3-datasource-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-extractor:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\9900e997213f6891e0a5bec4d646b758\transformed\media3-extractor-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-extractor:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\9900e997213f6891e0a5bec4d646b758\transformed\media3-extractor-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-decoder:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\92f6bb34143ed66ee00abfd6d8b8373d\transformed\media3-decoder-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-decoder:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\92f6bb34143ed66ee00abfd6d8b8373d\transformed\media3-decoder-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-database:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cdc7da6bf2ae70a152511d5d7d62a5\transformed\media3-database-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-database:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\87cdc7da6bf2ae70a152511d5d7d62a5\transformed\media3-database-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-common:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\145e061b47a116dd274ee2a14cfb8ba7\transformed\media3-common-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-common:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\145e061b47a116dd274ee2a14cfb8ba7\transformed\media3-common-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\54903ff84a4690cb020fc1e9d9860152\transformed\media3-exoplayer-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-exoplayer:1.0.0-beta03] C:\Users\<USER>\.gradle\caches\8.13\transforms\54903ff84a4690cb020fc1e9d9860152\transformed\media3-exoplayer-1.0.0-beta03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d6b21c1ae075bb005429dc4c336842\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d6b21c1ae075bb005429dc4c336842\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5cc506e396f29fd258b4f5e91326bdad\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5cc506e396f29fd258b4f5e91326bdad\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aff6c14532e3ca16ee9849ce8218bdeb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aff6c14532e3ca16ee9849ce8218bdeb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e13264f8a5e457aa03639dd7199c5070\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e13264f8a5e457aa03639dd7199c5070\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b191ec88feb25e056db9fffa7904dfe4\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b191ec88feb25e056db9fffa7904dfe4\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca317fdd63ce7c8d9f1a273065b16bcb\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca317fdd63ce7c8d9f1a273065b16bcb\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cbec5eff64fe905f4db1ea5b946f7f4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cbec5eff64fe905f4db1ea5b946f7f4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c2509a0068532fe005405776069e9a\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c2509a0068532fe005405776069e9a\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de2dcbf3459e4a801c3dc6729a95f7e6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de2dcbf3459e4a801c3dc6729a95f7e6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6a67e73b1379ea40f79c239dcad68e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6a67e73b1379ea40f79c239dcad68e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\323a85cbb5520e176b3916be7239d2f6\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\323a85cbb5520e176b3916be7239d2f6\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf42671a75f982be109a068148eb8bcc\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf42671a75f982be109a068148eb8bcc\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\e29cad113d57be3e067bfcf14b86b427\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\e29cad113d57be3e067bfcf14b86b427\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\482ffcf29b6e8d892d0acd21c537d2fa\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\482ffcf29b6e8d892d0acd21c537d2fa\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\08dbf47124c3644e56c71bd6c2399d27\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\08dbf47124c3644e56c71bd6c2399d27\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fce9a6080c7d502abe696bf08e0bfda\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fce9a6080c7d502abe696bf08e0bfda\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\957c17e0dd04d78e6c3bf61b73005d55\transformed\lifecycle-livedata-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\957c17e0dd04d78e6c3bf61b73005d55\transformed\lifecycle-livedata-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c85e0055dd40dbcc19d8071acc49d0e\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c85e0055dd40dbcc19d8071acc49d0e\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\e20d84cbe2db8cdbe8383e58d24cc5f7\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\e20d84cbe2db8cdbe8383e58d24cc5f7\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa97a0b9a4b07d29068ec1edd0b93c51\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa97a0b9a4b07d29068ec1edd0b93c51\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d3baca6be93504d1c9a29d0327812c5\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d3baca6be93504d1c9a29d0327812c5\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ec27ecf4e0e2b77d47cec65bbd5d03\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ec27ecf4e0e2b77d47cec65bbd5d03\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8dca2d17bb97bd73368ea919040cc370\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8dca2d17bb97bd73368ea919040cc370\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0c054722ccb56004411aa0c49c12b57\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0c054722ccb56004411aa0c49c12b57\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0714f79536ad61645fc1954b99bc485\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0714f79536ad61645fc1954b99bc485\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa5ed0abfb6055bf9790958d048b28ba\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa5ed0abfb6055bf9790958d048b28ba\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\65a253d802f7d6f78bd1e5266178114d\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\65a253d802f7d6f78bd1e5266178114d\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4838813f3df61de0850f23d52b1a4b2\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4838813f3df61de0850f23d52b1a4b2\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3accded29686b5491a8c71dce186ebd4\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3accded29686b5491a8c71dce186ebd4\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cd99f3de52d8ee5f39e08ac44964d20\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cd99f3de52d8ee5f39e08ac44964d20\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2239f5756734f1171cfd3d2d74262e0\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2239f5756734f1171cfd3d2d74262e0\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1f59e9dbc22de0795ade0ed5643292\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1f59e9dbc22de0795ade0ed5643292\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c18230a4644677ba01fc0047afa73e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c18230a4644677ba01fc0047afa73e0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f13868df578ecea865dfe5f01741c86\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f13868df578ecea865dfe5f01741c86\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca14587393821a125b74bc2c7165a856\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca14587393821a125b74bc2c7165a856\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6d60f7b6b6eb911416d4fe0173d9a39\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6d60f7b6b6eb911416d4fe0173d9a39\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6578356e73273a33e87485d3aa259d5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6578356e73273a33e87485d3aa259d5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1688656747656bd53f2272fa7071e970\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1688656747656bd53f2272fa7071e970\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\Nisso\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6578356e73273a33e87485d3aa259d5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6578356e73273a33e87485d3aa259d5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.falaileh.nisso.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.falaileh.nisso.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
