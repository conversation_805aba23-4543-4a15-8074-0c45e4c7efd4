[{"key": "androidx/test/espresso/util/TreeIterables.class", "name": "androidx/test/espresso/util/TreeIterables.class", "size": 3402, "crc": 714201810}, {"key": "androidx/test/espresso/util/TreeIterables$ViewTreeViewer.class", "name": "androidx/test/espresso/util/TreeIterables$ViewTreeViewer.class", "size": 1673, "crc": -553392007}, {"key": "androidx/test/espresso/util/TreeIterables$ViewAndDistance.class", "name": "androidx/test/espresso/util/TreeIterables$ViewAndDistance.class", "size": 937, "crc": 1734867302}, {"key": "androidx/test/espresso/util/TreeIterables$ViewAndDistance-IA.class", "name": "androidx/test/espresso/util/TreeIterables$ViewAndDistance-IA.class", "size": 154, "crc": 1692042465}, {"key": "androidx/test/espresso/util/TreeIterables$TreeViewer.class", "name": "androidx/test/espresso/util/TreeIterables$TreeViewer.class", "size": 407, "crc": -69792108}, {"key": "androidx/test/espresso/util/TreeIterables$TreeTraversalIterable.class", "name": "androidx/test/espresso/util/TreeIterables$TreeTraversalIterable.class", "size": 2736, "crc": -922925361}, {"key": "androidx/test/espresso/util/TreeIterables$TreeTraversalIterable-IA.class", "name": "androidx/test/espresso/util/TreeIterables$TreeTraversalIterable-IA.class", "size": 160, "crc": -1246780436}, {"key": "androidx/test/espresso/util/TreeIterables$TreeTraversalIterable$1.class", "name": "androidx/test/espresso/util/TreeIterables$TreeTraversalIterable$1.class", "size": 2223, "crc": -505854650}, {"key": "androidx/test/espresso/util/TreeIterables$TraversalStrategy.class", "name": "androidx/test/espresso/util/TreeIterables$TraversalStrategy.class", "size": 2276, "crc": 525884933}, {"key": "androidx/test/espresso/util/TreeIterables$TraversalStrategy-IA.class", "name": "androidx/test/espresso/util/TreeIterables$TraversalStrategy-IA.class", "size": 156, "crc": 1740784488}, {"key": "androidx/test/espresso/util/TreeIterables$TraversalStrategy$2.class", "name": "androidx/test/espresso/util/TreeIterables$TraversalStrategy$2.class", "size": 1197, "crc": -111682575}, {"key": "androidx/test/espresso/util/TreeIterables$TraversalStrategy$2-IA.class", "name": "androidx/test/espresso/util/TreeIterables$TraversalStrategy$2-IA.class", "size": 158, "crc": -322679260}, {"key": "androidx/test/espresso/util/TreeIterables$TraversalStrategy$1.class", "name": "androidx/test/espresso/util/TreeIterables$TraversalStrategy$1.class", "size": 1195, "crc": 665463231}, {"key": "androidx/test/espresso/util/TreeIterables$TraversalStrategy$1-IA.class", "name": "androidx/test/espresso/util/TreeIterables$TraversalStrategy$1-IA.class", "size": 158, "crc": -263836761}, {"key": "androidx/test/espresso/util/TreeIterables$DistanceRecordingTreeViewer.class", "name": "androidx/test/espresso/util/TreeIterables$DistanceRecordingTreeViewer.class", "size": 2470, "crc": 1837232717}, {"key": "androidx/test/espresso/util/TreeIterables$1.class", "name": "androidx/test/espresso/util/TreeIterables$1.class", "size": 1668, "crc": 959068713}, {"key": "androidx/test/espresso/util/TracingUtil.class", "name": "androidx/test/espresso/util/TracingUtil.class", "size": 2765, "crc": 851375959}, {"key": "androidx/test/espresso/util/HumanReadables.class", "name": "androidx/test/espresso/util/HumanReadables.class", "size": 10404, "crc": 1589260617}, {"key": "androidx/test/espresso/util/HumanReadables$1.class", "name": "androidx/test/espresso/util/HumanReadables$1.class", "size": 2445, "crc": 740476382}, {"key": "androidx/test/espresso/util/EspressoOptional.class", "name": "androidx/test/espresso/util/EspressoOptional.class", "size": 4144, "crc": -1702399725}, {"key": "androidx/test/espresso/util/ActivityLifecycles.class", "name": "androidx/test/espresso/util/ActivityLifecycles.class", "size": 1309, "crc": -1117960077}, {"key": "androidx/test/espresso/screenshot/ViewInteractionCapture.class", "name": "androidx/test/espresso/screenshot/ViewInteractionCapture.class", "size": 2478, "crc": 905661428}, {"key": "androidx/test/espresso/screenshot/ImageCaptureViewAction.class", "name": "androidx/test/espresso/screenshot/ImageCaptureViewAction.class", "size": 3136, "crc": -1786244177}, {"key": "androidx/test/espresso/screenshot/CaptureImageException.class", "name": "androidx/test/espresso/screenshot/CaptureImageException.class", "size": 1020, "crc": 1662678347}, {"key": "androidx/test/espresso/remote/annotation/RemoteMsgField.class", "name": "androidx/test/espresso/remote/annotation/RemoteMsgField.class", "size": 443, "crc": 1659027106}, {"key": "androidx/test/espresso/remote/annotation/RemoteMsgConstructor.class", "name": "androidx/test/espresso/remote/annotation/RemoteMsgConstructor.class", "size": 439, "crc": -1061742276}, {"key": "androidx/test/espresso/remote/RemoteProtocolException.class", "name": "androidx/test/espresso/remote/RemoteProtocolException.class", "size": 654, "crc": 809667441}, {"key": "androidx/test/espresso/remote/RemoteInteractionRegistry.class", "name": "androidx/test/espresso/remote/RemoteInteractionRegistry.class", "size": 1283, "crc": 2135699295}, {"key": "androidx/test/espresso/remote/RemoteInteraction.class", "name": "androidx/test/espresso/remote/RemoteInteraction.class", "size": 1127, "crc": 444973558}, {"key": "androidx/test/espresso/remote/RemoteEspressoException.class", "name": "androidx/test/espresso/remote/RemoteEspressoException.class", "size": 654, "crc": 276757646}, {"key": "androidx/test/espresso/remote/NoopRemoteInteraction.class", "name": "androidx/test/espresso/remote/NoopRemoteInteraction.class", "size": 1666, "crc": -1860364713}, {"key": "androidx/test/espresso/remote/NoopRemoteInteraction$2.class", "name": "androidx/test/espresso/remote/NoopRemoteInteraction$2.class", "size": 1188, "crc": -802755982}, {"key": "androidx/test/espresso/remote/NoopRemoteInteraction$1.class", "name": "androidx/test/espresso/remote/NoopRemoteInteraction$1.class", "size": 1188, "crc": -716331279}, {"key": "androidx/test/espresso/remote/NoRemoteEspressoInstanceException.class", "name": "androidx/test/espresso/remote/NoRemoteEspressoInstanceException.class", "size": 510, "crc": -1242053130}, {"key": "androidx/test/espresso/remote/MethodInvocation.class", "name": "androidx/test/espresso/remote/MethodInvocation.class", "size": 6580, "crc": -936509460}, {"key": "androidx/test/espresso/remote/MethodInvocation$MethodKey.class", "name": "androidx/test/espresso/remote/MethodInvocation$MethodKey.class", "size": 1894, "crc": 445968051}, {"key": "androidx/test/espresso/remote/IInteractionExecutionStatus.class", "name": "androidx/test/espresso/remote/IInteractionExecutionStatus.class", "size": 472, "crc": 2125887868}, {"key": "androidx/test/espresso/remote/IInteractionExecutionStatus$Stub.class", "name": "androidx/test/espresso/remote/IInteractionExecutionStatus$Stub.class", "size": 1707, "crc": 383198194}, {"key": "androidx/test/espresso/remote/IInteractionExecutionStatus$Stub$Proxy.class", "name": "androidx/test/espresso/remote/IInteractionExecutionStatus$Stub$Proxy.class", "size": 1263, "crc": -569164550}, {"key": "androidx/test/espresso/remote/EspressoRemoteMessage.class", "name": "androidx/test/espresso/remote/EspressoRemoteMessage.class", "size": 326, "crc": 2134911760}, {"key": "androidx/test/espresso/remote/EspressoRemoteMessage$To.class", "name": "androidx/test/espresso/remote/EspressoRemoteMessage$To.class", "size": 369, "crc": -574508040}, {"key": "androidx/test/espresso/remote/EspressoRemoteMessage$From.class", "name": "androidx/test/espresso/remote/EspressoRemoteMessage$From.class", "size": 416, "crc": 763215046}, {"key": "androidx/test/espresso/remote/ConstructorInvocation.class", "name": "androidx/test/espresso/remote/ConstructorInvocation.class", "size": 6312, "crc": -1854681222}, {"key": "androidx/test/espresso/remote/ConstructorInvocation$ConstructorKey.class", "name": "androidx/test/espresso/remote/ConstructorInvocation$ConstructorKey.class", "size": 1342, "crc": -219422930}, {"key": "androidx/test/espresso/remote/Bindable.class", "name": "androidx/test/espresso/remote/Bindable.class", "size": 259, "crc": 958783340}, {"key": "androidx/test/espresso/matcher/ViewMatchers.class", "name": "androidx/test/espresso/matcher/ViewMatchers.class", "size": 20341, "crc": 901615946}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithTextMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithTextMatcher.class", "size": 2694, "crc": 2053395617}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithTextMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithTextMatcher-IA.class", "size": 156, "crc": 1820268187}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithTagValueMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithTagValueMatcher.class", "size": 2158, "crc": -1860430724}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithTagValueMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithTagValueMatcher-IA.class", "size": 160, "crc": -761213488}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithTagKeyMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithTagKeyMatcher.class", "size": 2541, "crc": 1548571647}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithTagKeyMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithTagKeyMatcher-IA.class", "size": 158, "crc": -1136901097}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithSpinnerTextMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithSpinnerTextMatcher.class", "size": 2492, "crc": 604200969}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithSpinnerTextMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithSpinnerTextMatcher-IA.class", "size": 163, "crc": 1728972895}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithSpinnerTextIdMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithSpinnerTextIdMatcher.class", "size": 3057, "crc": 1413269626}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithSpinnerTextIdMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithSpinnerTextIdMatcher-IA.class", "size": 165, "crc": -972245239}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithResourceNameMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithResourceNameMatcher.class", "size": 2936, "crc": -150796481}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithResourceNameMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithResourceNameMatcher-IA.class", "size": 164, "crc": -248765509}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithParentMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithParentMatcher.class", "size": 2163, "crc": -891020960}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithParentMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithParentMatcher-IA.class", "size": 158, "crc": -1592976941}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithParentIndexMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithParentIndexMatcher.class", "size": 2854, "crc": 266855534}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithParentIndexMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithParentIndexMatcher-IA.class", "size": 163, "crc": 1690214955}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithInputTypeMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithInputTypeMatcher.class", "size": 2018, "crc": 441732516}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithInputTypeMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithInputTypeMatcher-IA.class", "size": 161, "crc": -218735279}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithIdMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithIdMatcher.class", "size": 3937, "crc": 1240746722}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithIdMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithIdMatcher-IA.class", "size": 154, "crc": 389595112}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithHintMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithHintMatcher.class", "size": 2167, "crc": -1404803388}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithHintMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithHintMatcher-IA.class", "size": 156, "crc": -2077532874}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithEffectiveVisibilityMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithEffectiveVisibilityMatcher.class", "size": 2699, "crc": -833200686}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithEffectiveVisibilityMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithEffectiveVisibilityMatcher-IA.class", "size": 171, "crc": 1428498536}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionTextMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionTextMatcher.class", "size": 2387, "crc": -370642899}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionTextMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionTextMatcher-IA.class", "size": 174, "crc": 568135678}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionMatcher.class", "size": 2256, "crc": 1172912495}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionMatcher-IA.class", "size": 170, "crc": 334168529}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionFromIdMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionFromIdMatcher.class", "size": 2909, "crc": -1438212415}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionFromIdMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithContentDescriptionFromIdMatcher-IA.class", "size": 176, "crc": 1885669188}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithClassNameMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithClassNameMatcher.class", "size": 2310, "crc": -1346810604}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithClassNameMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithClassNameMatcher-IA.class", "size": 161, "crc": 492442340}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithChildMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithChildMatcher.class", "size": 2804, "crc": -1345843009}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithChildMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithChildMatcher-IA.class", "size": 157, "crc": -761188128}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithCheckBoxStateMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithCheckBoxStateMatcher.class", "size": 2481, "crc": -1784578233}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithCheckBoxStateMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithCheckBoxStateMatcher-IA.class", "size": 165, "crc": -1904637304}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithCharSequenceMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithCharSequenceMatcher.class", "size": 4178, "crc": -2120710454}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithCharSequenceMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithCharSequenceMatcher-IA.class", "size": 164, "crc": 1093222085}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithCharSequenceMatcher$TextViewMethod.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithCharSequenceMatcher$TextViewMethod.class", "size": 1587, "crc": -477161307}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithAlphaMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithAlphaMatcher.class", "size": 1869, "crc": 546782806}, {"key": "androidx/test/espresso/matcher/ViewMatchers$WithAlphaMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$WithAlphaMatcher-IA.class", "size": 157, "crc": 1057473129}, {"key": "androidx/test/espresso/matcher/ViewMatchers$Visibility.class", "name": "androidx/test/espresso/matcher/ViewMatchers$Visibility.class", "size": 2447, "crc": 269426923}, {"key": "androidx/test/espresso/matcher/ViewMatchers$SupportsInputMethodsMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$SupportsInputMethodsMatcher.class", "size": 1757, "crc": -1704038344}, {"key": "androidx/test/espresso/matcher/ViewMatchers$SupportsInputMethodsMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$SupportsInputMethodsMatcher-IA.class", "size": 168, "crc": 895834148}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsSelectedMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsSelectedMatcher.class", "size": 1874, "crc": -287154704}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsSelectedMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsSelectedMatcher-IA.class", "size": 158, "crc": 1126232421}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsRootMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsRootMatcher.class", "size": 1634, "crc": -2024471354}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsRootMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsRootMatcher-IA.class", "size": 154, "crc": -1526614362}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsJavascriptEnabledMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsJavascriptEnabledMatcher.class", "size": 1967, "crc": -776615279}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsJavascriptEnabledMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsJavascriptEnabledMatcher-IA.class", "size": 167, "crc": -919233064}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsFocusedMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsFocusedMatcher.class", "size": 1867, "crc": -1553058064}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsFocusedMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsFocusedMatcher-IA.class", "size": 157, "crc": 1518328080}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsFocusableMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsFocusableMatcher.class", "size": 1881, "crc": 209912786}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsFocusableMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsFocusableMatcher-IA.class", "size": 159, "crc": -117968300}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsEnabledMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsEnabledMatcher.class", "size": 1867, "crc": 292878926}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsEnabledMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsEnabledMatcher-IA.class", "size": 157, "crc": 1220978509}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsDisplayingAtLeastMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsDisplayingAtLeastMatcher.class", "size": 5106, "crc": -1943697700}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsDisplayingAtLeastMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsDisplayingAtLeastMatcher-IA.class", "size": 167, "crc": 152991559}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsDisplayedMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsDisplayedMatcher.class", "size": 2441, "crc": 1537314651}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsDisplayedMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsDisplayedMatcher-IA.class", "size": 159, "crc": -324979576}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsDescendantOfAMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsDescendantOfAMatcher.class", "size": 2425, "crc": -91209860}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsDescendantOfAMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsDescendantOfAMatcher-IA.class", "size": 163, "crc": -43806509}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsClickableMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsClickableMatcher.class", "size": 1881, "crc": -1926356329}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsClickableMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsClickableMatcher-IA.class", "size": 159, "crc": 1915528099}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsAssignableFromMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsAssignableFromMatcher.class", "size": 2200, "crc": 226776588}, {"key": "androidx/test/espresso/matcher/ViewMatchers$IsAssignableFromMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$IsAssignableFromMatcher-IA.class", "size": 164, "crc": 654040996}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasSiblingMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasSiblingMatcher.class", "size": 3026, "crc": -1085879590}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasSiblingMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasSiblingMatcher-IA.class", "size": 158, "crc": 1011103427}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasMinimumChildCountMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasMinimumChildCountMatcher.class", "size": 2072, "crc": -1636778353}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasMinimumChildCountMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasMinimumChildCountMatcher-IA.class", "size": 168, "crc": 1139865781}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasLinksMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasLinksMatcher.class", "size": 1849, "crc": -2041913192}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasLinksMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasLinksMatcher-IA.class", "size": 156, "crc": -1378621090}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasImeActionMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasImeActionMatcher.class", "size": 2676, "crc": -2080973063}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasImeActionMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasImeActionMatcher-IA.class", "size": 160, "crc": -94735878}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasFocusMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasFocusMatcher.class", "size": 1834, "crc": -698129088}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasFocusMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasFocusMatcher-IA.class", "size": 156, "crc": 1879078285}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasErrorTextMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasErrorTextMatcher.class", "size": 2250, "crc": 151492177}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasErrorTextMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasErrorTextMatcher-IA.class", "size": 160, "crc": 1941107259}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasDescendantMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasDescendantMatcher.class", "size": 3562, "crc": 2054455173}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasDescendantMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasDescendantMatcher-IA.class", "size": 161, "crc": 914793819}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasDescendantMatcher$$ExternalSyntheticLambda0.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasDescendantMatcher$$ExternalSyntheticLambda0.class", "size": 1027, "crc": 1879975519}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasContentDescriptionMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasContentDescriptionMatcher.class", "size": 1645, "crc": -1919508634}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasContentDescriptionMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasContentDescriptionMatcher-IA.class", "size": 169, "crc": 1595345500}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasChildCountMatcher.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasChildCountMatcher.class", "size": 2028, "crc": -803186693}, {"key": "androidx/test/espresso/matcher/ViewMatchers$HasChildCountMatcher-IA.class", "name": "androidx/test/espresso/matcher/ViewMatchers$HasChildCountMatcher-IA.class", "size": 161, "crc": -1696520701}, {"key": "androidx/test/espresso/matcher/ViewMatchers$2.class", "name": "androidx/test/espresso/matcher/ViewMatchers$2.class", "size": 1048, "crc": 1910072496}, {"key": "androidx/test/espresso/matcher/ViewMatchers$1.class", "name": "androidx/test/espresso/matcher/ViewMatchers$1.class", "size": 3085, "crc": 1817804253}, {"key": "androidx/test/espresso/matcher/RootMatchers.class", "name": "androidx/test/espresso/matcher/RootMatchers.class", "size": 4570, "crc": 1875082286}, {"key": "androidx/test/espresso/matcher/RootMatchers$WithDecorView.class", "name": "androidx/test/espresso/matcher/RootMatchers$WithDecorView.class", "size": 1676, "crc": 1179619006}, {"key": "androidx/test/espresso/matcher/RootMatchers$IsTouchable.class", "name": "androidx/test/espresso/matcher/RootMatchers$IsTouchable.class", "size": 1564, "crc": -360631357}, {"key": "androidx/test/espresso/matcher/RootMatchers$IsSystemAlertWindow.class", "name": "androidx/test/espresso/matcher/RootMatchers$IsSystemAlertWindow.class", "size": 1790, "crc": -325016791}, {"key": "androidx/test/espresso/matcher/RootMatchers$IsSubwindowOfCurrentActivity.class", "name": "androidx/test/espresso/matcher/RootMatchers$IsSubwindowOfCurrentActivity.class", "size": 1530, "crc": -360192612}, {"key": "androidx/test/espresso/matcher/RootMatchers$IsPlatformPopup.class", "name": "androidx/test/espresso/matcher/RootMatchers$IsPlatformPopup.class", "size": 1848, "crc": -1799813887}, {"key": "androidx/test/espresso/matcher/RootMatchers$IsFocusable.class", "name": "androidx/test/espresso/matcher/RootMatchers$IsFocusable.class", "size": 1564, "crc": -1048238949}, {"key": "androidx/test/espresso/matcher/RootMatchers$IsDialog.class", "name": "androidx/test/espresso/matcher/RootMatchers$IsDialog.class", "size": 1741, "crc": -1229044779}, {"key": "androidx/test/espresso/matcher/RootMatchers$HasWindowLayoutParams.class", "name": "androidx/test/espresso/matcher/RootMatchers$HasWindowLayoutParams.class", "size": 1412, "crc": 761261208}, {"key": "androidx/test/espresso/matcher/RootMatchers$HasWindowFocus.class", "name": "androidx/test/espresso/matcher/RootMatchers$HasWindowFocus.class", "size": 1210, "crc": 912882482}, {"key": "androidx/test/espresso/matcher/PreferenceMatchers.class", "name": "androidx/test/espresso/matcher/PreferenceMatchers.class", "size": 2269, "crc": -1366760851}, {"key": "androidx/test/espresso/matcher/PreferenceMatchers$6.class", "name": "androidx/test/espresso/matcher/PreferenceMatchers$6.class", "size": 1441, "crc": -273835346}, {"key": "androidx/test/espresso/matcher/PreferenceMatchers$5.class", "name": "androidx/test/espresso/matcher/PreferenceMatchers$5.class", "size": 1197, "crc": 176092926}, {"key": "androidx/test/espresso/matcher/PreferenceMatchers$4.class", "name": "androidx/test/espresso/matcher/PreferenceMatchers$4.class", "size": 1600, "crc": 1279564504}, {"key": "androidx/test/espresso/matcher/PreferenceMatchers$3.class", "name": "androidx/test/espresso/matcher/PreferenceMatchers$3.class", "size": 2347, "crc": -1350928173}, {"key": "androidx/test/espresso/matcher/PreferenceMatchers$2.class", "name": "androidx/test/espresso/matcher/PreferenceMatchers$2.class", "size": 1554, "crc": -115819193}, {"key": "androidx/test/espresso/matcher/PreferenceMatchers$1.class", "name": "androidx/test/espresso/matcher/PreferenceMatchers$1.class", "size": 2346, "crc": -371886029}, {"key": "androidx/test/espresso/matcher/LayoutMatchers.class", "name": "androidx/test/espresso/matcher/LayoutMatchers.class", "size": 783, "crc": 1674947931}, {"key": "androidx/test/espresso/matcher/LayoutMatchers$2.class", "name": "androidx/test/espresso/matcher/LayoutMatchers$2.class", "size": 1292, "crc": -68174785}, {"key": "androidx/test/espresso/matcher/LayoutMatchers$1.class", "name": "androidx/test/espresso/matcher/LayoutMatchers$1.class", "size": 1430, "crc": -1654769972}, {"key": "androidx/test/espresso/matcher/HasBackgroundMatcher.class", "name": "androidx/test/espresso/matcher/HasBackgroundMatcher.class", "size": 2520, "crc": 960635709}, {"key": "androidx/test/espresso/matcher/CursorMatchers.class", "name": "androidx/test/espresso/matcher/CursorMatchers.class", "size": 11166, "crc": -778852673}, {"key": "androidx/test/espresso/matcher/CursorMatchers$CursorMatcher.class", "name": "androidx/test/espresso/matcher/CursorMatchers$CursorMatcher.class", "size": 5288, "crc": 1909580681}, {"key": "androidx/test/espresso/matcher/CursorMatchers$CursorMatcher-IA.class", "name": "androidx/test/espresso/matcher/CursorMatchers$CursorMatcher-IA.class", "size": 156, "crc": 352506217}, {"key": "androidx/test/espresso/matcher/CursorMatchers$CursorDataRetriever.class", "name": "androidx/test/espresso/matcher/CursorMatchers$CursorDataRetriever.class", "size": 500, "crc": 122792595}, {"key": "androidx/test/espresso/matcher/CursorMatchers$7.class", "name": "androidx/test/espresso/matcher/CursorMatchers$7.class", "size": 1337, "crc": 279599777}, {"key": "androidx/test/espresso/matcher/CursorMatchers$6.class", "name": "androidx/test/espresso/matcher/CursorMatchers$6.class", "size": 1389, "crc": 87059962}, {"key": "androidx/test/espresso/matcher/CursorMatchers$5.class", "name": "androidx/test/espresso/matcher/CursorMatchers$5.class", "size": 1383, "crc": -382320665}, {"key": "androidx/test/espresso/matcher/CursorMatchers$4.class", "name": "androidx/test/espresso/matcher/CursorMatchers$4.class", "size": 1387, "crc": 1184782805}, {"key": "androidx/test/espresso/matcher/CursorMatchers$3.class", "name": "androidx/test/espresso/matcher/CursorMatchers$3.class", "size": 1383, "crc": 1887636584}, {"key": "androidx/test/espresso/matcher/CursorMatchers$2.class", "name": "androidx/test/espresso/matcher/CursorMatchers$2.class", "size": 1377, "crc": 397812688}, {"key": "androidx/test/espresso/matcher/CursorMatchers$1.class", "name": "androidx/test/espresso/matcher/CursorMatchers$1.class", "size": 1285, "crc": 207183244}, {"key": "androidx/test/espresso/matcher/BoundedMatcher.class", "name": "androidx/test/espresso/matcher/BoundedMatcher.class", "size": 2297, "crc": -1083994960}, {"key": "androidx/test/espresso/matcher/BoundedDiagnosingMatcher.class", "name": "androidx/test/espresso/matcher/BoundedDiagnosingMatcher.class", "size": 2855, "crc": 1332860039}, {"key": "androidx/test/espresso/internal/inject/TargetContext.class", "name": "androidx/test/espresso/internal/inject/TargetContext.class", "size": 326, "crc": -1470065283}, {"key": "androidx/test/espresso/internal/inject/InstrumentationContext.class", "name": "androidx/test/espresso/internal/inject/InstrumentationContext.class", "size": 344, "crc": 1640588559}, {"key": "androidx/test/espresso/internal/data/model/ViewData.class", "name": "androidx/test/espresso/internal/data/model/ViewData.class", "size": 3149, "crc": -1222670698}, {"key": "androidx/test/espresso/internal/data/model/TestFlow.class", "name": "androidx/test/espresso/internal/data/model/TestFlow.class", "size": 3865, "crc": 1983546743}, {"key": "androidx/test/espresso/internal/data/model/TestArtifact.class", "name": "androidx/test/espresso/internal/data/model/TestArtifact.class", "size": 2609, "crc": -1340176641}, {"key": "androidx/test/espresso/internal/data/model/ScreenData.class", "name": "androidx/test/espresso/internal/data/model/ScreenData.class", "size": 3172, "crc": -706555922}, {"key": "androidx/test/espresso/internal/data/model/ActionData.class", "name": "androidx/test/espresso/internal/data/model/ActionData.class", "size": 5215, "crc": -443491242}, {"key": "androidx/test/espresso/internal/data/TestFlowVisualizer.class", "name": "androidx/test/espresso/internal/data/TestFlowVisualizer.class", "size": 12171, "crc": 1999338362}, {"key": "androidx/test/espresso/internal/data/TestFlowVisualizer$$ExternalSyntheticBackport0.class", "name": "androidx/test/espresso/internal/data/TestFlowVisualizer$$ExternalSyntheticBackport0.class", "size": 653, "crc": 1894963221}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/internal/InternalFutures.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/internal/InternalFutures.class", "size": 554, "crc": 1595238877}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/internal/InternalFutureFailureAccess.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/internal/InternalFutureFailureAccess.class", "size": 367, "crc": -367182684}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/Uninterruptibles.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/Uninterruptibles.class", "size": 788, "crc": -1073306309}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/TrustedListenableFutureTask.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/TrustedListenableFutureTask.class", "size": 2268, "crc": -197027655}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/TrustedListenableFutureTask$TrustedFutureInterruptibleTask.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/TrustedListenableFutureTask$TrustedFutureInterruptibleTask.class", "size": 1707, "crc": 1462669194}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ThreadFactoryBuilder.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ThreadFactoryBuilder.class", "size": 2387, "crc": 329753194}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ThreadFactoryBuilder$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ThreadFactoryBuilder$1.class", "size": 2212, "crc": -686562112}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/SettableFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/SettableFuture.class", "size": 1068, "crc": 866349119}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/Platform.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/Platform.class", "size": 626, "crc": -1125842197}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/OverflowAvoidingLockSupport.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/OverflowAvoidingLockSupport.class", "size": 422, "crc": 380839163}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/NullnessCasts.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/NullnessCasts.class", "size": 384, "crc": 990539460}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors.class", "size": 2056, "crc": 185922567}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$ScheduledListeningDecorator.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$ScheduledListeningDecorator.class", "size": 3337, "crc": -405139215}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$ScheduledListeningDecorator$NeverSuccessfulListenableFutureTask.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$ScheduledListeningDecorator$NeverSuccessfulListenableFutureTask.class", "size": 1653, "crc": -1444433218}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$ScheduledListeningDecorator$ListenableScheduledTask.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$ScheduledListeningDecorator$ListenableScheduledTask.class", "size": 1698, "crc": 1295327530}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$ListeningDecorator.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$ListeningDecorator.class", "size": 1721, "crc": -650740247}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$5.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/MoreExecutors$5.class", "size": 1292, "crc": -1092261998}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ListeningExecutorService.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ListeningExecutorService.class", "size": 342, "crc": -537967629}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ListenableScheduledFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ListenableScheduledFuture.class", "size": 297, "crc": -1117561936}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ListenableFutureTask.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ListenableFutureTask.class", "size": 1850, "crc": 1487865342}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/InterruptibleTask.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/InterruptibleTask.class", "size": 3959, "crc": 940752008}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/InterruptibleTask$DoNothingRunnable.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/InterruptibleTask$DoNothingRunnable.class", "size": 735, "crc": -247244126}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/InterruptibleTask$Blocker.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/InterruptibleTask$Blocker.class", "size": 1446, "crc": -1276673845}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/InterruptibleTask$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/InterruptibleTask$1.class", "size": 327, "crc": 62534823}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ImmediateFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ImmediateFuture.class", "size": 2686, "crc": 4852014}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ImmediateFuture$ImmediateFailedFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ImmediateFuture$ImmediateFailedFuture.class", "size": 707, "crc": -849820505}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/GwtFuturesCatchingSpecialization.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/GwtFuturesCatchingSpecialization.class", "size": 209, "crc": 2030534357}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/GwtFluentFutureCatchingSpecialization.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/GwtFluentFutureCatchingSpecialization.class", "size": 374, "crc": -910108048}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/Futures.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/Futures.class", "size": 2166, "crc": -1487355375}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ForwardingListenableFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ForwardingListenableFuture.class", "size": 890, "crc": -60439435}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ForwardingListenableFuture$SimpleForwardingListenableFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ForwardingListenableFuture$SimpleForwardingListenableFuture.class", "size": 1071, "crc": -423930622}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ForwardingFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ForwardingFuture.class", "size": 1037, "crc": -270118239}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/FluentFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/FluentFuture.class", "size": 490, "crc": 2044962050}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/FluentFuture$TrustedFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/FluentFuture$TrustedFuture.class", "size": 1269, "crc": 1626745644}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ExecutionList.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ExecutionList.class", "size": 2782, "crc": 1811516622}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ExecutionList$RunnableExecutorPair.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/ExecutionList$RunnableExecutorPair.class", "size": 853, "crc": -1682120754}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/DirectExecutor.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/DirectExecutor.class", "size": 1129, "crc": 230189945}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractTransformFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractTransformFuture.class", "size": 3997, "crc": -1878505405}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractTransformFuture$TransformFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractTransformFuture$TransformFuture.class", "size": 1197, "crc": -950355229}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractListeningExecutorService.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractListeningExecutorService.class", "size": 1921, "crc": -677275979}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture.class", "size": 21203, "crc": 1954243539}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Waiter.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Waiter.class", "size": 1708, "crc": 613376466}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$UnsafeAtomicHelper.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$UnsafeAtomicHelper.class", "size": 5106, "crc": -505473788}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$UnsafeAtomicHelper$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$UnsafeAtomicHelper$1.class", "size": 1473, "crc": 776157234}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$UnsafeAtomicHelper$$ExternalSyntheticBackportWithForwarding0.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$UnsafeAtomicHelper$$ExternalSyntheticBackportWithForwarding0.class", "size": 581, "crc": -2089092778}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$TrustedFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$TrustedFuture.class", "size": 1414, "crc": -1914203143}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Trusted.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Trusted.class", "size": 484, "crc": -1736994883}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$SynchronizedHelper.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$SynchronizedHelper.class", "size": 4369, "crc": -596122772}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$SetFuture.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$SetFuture.class", "size": 1883, "crc": 62270268}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$SafeAtomicHelper.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$SafeAtomicHelper.class", "size": 4951, "crc": -1132305081}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$SafeAtomicHelper$$ExternalSyntheticBackportWithForwarding0.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$SafeAtomicHelper$$ExternalSyntheticBackportWithForwarding0.class", "size": 638, "crc": -867948815}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Listener.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Listener.class", "size": 860, "crc": -1977893606}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Failure.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Failure.class", "size": 1065, "crc": -522173801}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Failure$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Failure$1.class", "size": 636, "crc": -394126520}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Cancellation.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$Cancellation.class", "size": 919, "crc": 141933963}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$AtomicHelper.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$AtomicHelper.class", "size": 2463, "crc": -1821703607}, {"key": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/util/concurrent/AbstractFuture$1.class", "size": 318, "crc": -1004771167}, {"key": "androidx/test/espresso/core/internal/deps/guava/primitives/IntsMethodsForWeb.class", "name": "androidx/test/espresso/core/internal/deps/guava/primitives/IntsMethodsForWeb.class", "size": 174, "crc": 887194026}, {"key": "androidx/test/espresso/core/internal/deps/guava/primitives/Ints.class", "name": "androidx/test/espresso/core/internal/deps/guava/primitives/Ints.class", "size": 389, "crc": -1501465874}, {"key": "androidx/test/espresso/core/internal/deps/guava/primitives/Booleans.class", "name": "androidx/test/espresso/core/internal/deps/guava/primitives/Booleans.class", "size": 291, "crc": 1154638701}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/UnmodifiableListIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/UnmodifiableListIterator.class", "size": 834, "crc": 86813549}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/UnmodifiableIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/UnmodifiableIterator.class", "size": 702, "crc": 625573597}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/TransformedIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/TransformedIterator.class", "size": 927, "crc": -933311069}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/SingletonImmutableSet.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/SingletonImmutableSet.class", "size": 1571, "crc": 467217150}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Sets.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Sets.class", "size": 959, "crc": -387517541}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableSet.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableSet.class", "size": 2346, "crc": 1515104062}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap.class", "size": 6680, "crc": -1016823537}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap$KeysOrValuesAsList.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap$KeysOrValuesAsList.class", "size": 980, "crc": 2082741924}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap$KeySet.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap$KeySet.class", "size": 1677, "crc": -1727232964}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap$EntrySet.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap$EntrySet.class", "size": 2471, "crc": -176332453}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap$EntrySet$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableMap$EntrySet$1.class", "size": 1850, "crc": 1574236650}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableList.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/RegularImmutableList.class", "size": 1482, "crc": 648965371}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/RangeGwtSerializationDependencies.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/RangeGwtSerializationDependencies.class", "size": 414, "crc": 447759951}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Range.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Range.class", "size": 3974, "crc": -705084643}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Platform.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Platform.class", "size": 734, "crc": 1282070538}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Ordering.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Ordering.class", "size": 1293, "crc": -1071021260}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ObjectArrays.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ObjectArrays.class", "size": 1263, "crc": 1517274696}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/NullnessCasts.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/NullnessCasts.class", "size": 297, "crc": -188540739}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Maps.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Maps.class", "size": 2100, "crc": 1760279946}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Maps$EntryFunction.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Maps$EntryFunction.class", "size": 1521, "crc": 1846731273}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Maps$EntryFunction$2.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Maps$EntryFunction$2.class", "size": 947, "crc": -672154462}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Maps$EntryFunction$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Maps$EntryFunction$1.class", "size": 945, "crc": -298054845}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Maps$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Maps$1.class", "size": 385, "crc": 852174685}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Lists.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Lists.class", "size": 3730, "crc": 2123540933}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Iterators.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Iterators.class", "size": 3917, "crc": 923003644}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Iterators$9.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Iterators$9.class", "size": 939, "crc": 443627850}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Iterators$6.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Iterators$6.class", "size": 953, "crc": 976416962}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Iterators$5.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Iterators$5.class", "size": 1237, "crc": 296961130}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Iterables.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Iterables.class", "size": 3097, "crc": 298258185}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Iterables$5.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Iterables$5.class", "size": 1151, "crc": 506715722}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Iterables$4.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Iterables$4.class", "size": 1212, "crc": 762656993}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableSet.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableSet.class", "size": 5348, "crc": 1428677072}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableSet$SerializedForm.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableSet$SerializedForm.class", "size": 902, "crc": -1072190173}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableSet$Builder.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableSet$Builder.class", "size": 3065, "crc": -671206121}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableMap.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableMap.class", "size": 4960, "crc": -592375752}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableMap$SerializedForm.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableMap$SerializedForm.class", "size": 2531, "crc": -1303946762}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableMap$Builder.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableMap$Builder.class", "size": 5236, "crc": 1003770359}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableMap$Builder$DuplicateKey.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableMap$Builder$DuplicateKey.class", "size": 1179, "crc": -198798442}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList.class", "size": 6971, "crc": -739994793}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList$SubList.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList$SubList.class", "size": 1460, "crc": 1089932766}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList$SerializedForm.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList$SerializedForm.class", "size": 907, "crc": 584245179}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList$Itr.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList$Itr.class", "size": 746, "crc": -636557534}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList$Builder.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableList$Builder.class", "size": 1371, "crc": 56969652}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableCollection.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableCollection.class", "size": 3506, "crc": -1391438543}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableCollection$Builder.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableCollection$Builder.class", "size": 737, "crc": 1851890177}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableCollection$ArrayBasedBuilder.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ImmutableCollection$ArrayBasedBuilder.class", "size": 2045, "crc": 1523807880}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Hashing.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Hashing.class", "size": 494, "crc": 157551971}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ForwardingObject.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ForwardingObject.class", "size": 404, "crc": 1605083182}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/FluentIterable.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/FluentIterable.class", "size": 1003, "crc": 809630456}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Cut.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Cut.class", "size": 2375, "crc": 711920814}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Cut$BelowValue.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Cut$BelowValue.class", "size": 1619, "crc": -335533928}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Cut$BelowAll.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Cut$BelowAll.class", "size": 1658, "crc": 1869411797}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Cut$AboveValue.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Cut$AboveValue.class", "size": 1524, "crc": -1948776699}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Cut$AboveAll.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Cut$AboveAll.class", "size": 1658, "crc": -484175270}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ComparatorOrdering.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ComparatorOrdering.class", "size": 1274, "crc": 2050597871}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/Collections2.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/Collections2.class", "size": 584, "crc": 464652448}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/CollectPreconditions.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/CollectPreconditions.class", "size": 1046, "crc": -345872968}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/ByFunctionOrdering.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/ByFunctionOrdering.class", "size": 2005, "crc": -174444900}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractSequentialIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractSequentialIterator.class", "size": 922, "crc": 1171588265}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractIterator.class", "size": 2210, "crc": -1130888482}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractIterator$State.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractIterator$State.class", "size": 1138, "crc": -2059543695}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractIterator$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractIterator$1.class", "size": 941, "crc": 1618629125}, {"key": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractIndexedListIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/collect/AbstractIndexedListIterator.class", "size": 1363, "crc": -1021521163}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/Weigher.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/Weigher.class", "size": 207, "crc": -1609679947}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/Striped64.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/Striped64.class", "size": 3877, "crc": 361566019}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/Striped64$Cell.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/Striped64$Cell.class", "size": 1253, "crc": 313869589}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/Striped64$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/Striped64$1.class", "size": 1199, "crc": -141181882}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalNotification.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalNotification.class", "size": 1165, "crc": -2040605182}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalListener.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalListener.class", "size": 266, "crc": -1248093753}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause.class", "size": 1655, "crc": 2000167892}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$5.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$5.class", "size": 621, "crc": -732773186}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$4.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$4.class", "size": 621, "crc": 951439768}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$3.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$3.class", "size": 621, "crc": -745295703}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$2.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$2.class", "size": 621, "crc": -1948972963}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/RemovalCause$1.class", "size": 539, "crc": 1692640302}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/ReferenceEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/ReferenceEntry.class", "size": 1278, "crc": 195990736}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LongAdder.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LongAdder.class", "size": 2657, "crc": -1751277024}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddables.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddables.class", "size": 1177, "crc": -298866219}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddables$PureJavaLongAddable.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddables$PureJavaLongAddable.class", "size": 883, "crc": 1085929519}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddables$2.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddables$2.class", "size": 947, "crc": -1083310974}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddables$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddables$1.class", "size": 730, "crc": 72782803}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddable.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LongAddable.class", "size": 204, "crc": -770437275}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache.class", "size": 20052, "crc": -402456767}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WriteThroughEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WriteThroughEntry.class", "size": 1715, "crc": 1665922154}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WriteQueue.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WriteQueue.class", "size": 3276, "crc": -857443672}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WriteQueue$2.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WriteQueue$2.class", "size": 1510, "crc": 1799608607}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WriteQueue$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WriteQueue$1.class", "size": 1396, "crc": -1390340009}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeightedWeakValueReference.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeightedWeakValueReference.class", "size": 1245, "crc": -422403035}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeightedStrongValueReference.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeightedStrongValueReference.class", "size": 658, "crc": 1227215023}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeightedSoftValueReference.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeightedSoftValueReference.class", "size": 1245, "crc": 191302584}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakWriteEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakWriteEntry.class", "size": 1385, "crc": 750431675}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakValueReference.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakValueReference.class", "size": 1449, "crc": -1830993335}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakEntry.class", "size": 2607, "crc": 228331018}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakAccessWriteEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakAccessWriteEntry.class", "size": 1932, "crc": -2113818613}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakAccessEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$WeakAccessEntry.class", "size": 1396, "crc": 1060491709}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Values.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Values.class", "size": 1452, "crc": 304067466}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$ValueReference.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$ValueReference.class", "size": 761, "crc": 118220608}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$ValueIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$ValueIterator.class", "size": 882, "crc": 439162957}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongWriteEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongWriteEntry.class", "size": 1361, "crc": -2089429925}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongValueReference.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongValueReference.class", "size": 1267, "crc": -1127628485}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongEntry.class", "size": 1601, "crc": -1995545096}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongAccessWriteEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongAccessWriteEntry.class", "size": 1908, "crc": -937388316}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongAccessEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$StrongAccessEntry.class", "size": 1372, "crc": 2139514507}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Strength.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Strength.class", "size": 2128, "crc": -703008131}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Strength$3.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Strength$3.class", "size": 2185, "crc": 460878715}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Strength$2.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Strength$2.class", "size": 2185, "crc": 578595184}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Strength$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Strength$1.class", "size": 1838, "crc": -1409141560}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$SoftValueReference.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$SoftValueReference.class", "size": 1449, "crc": -673843828}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Segment.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Segment.class", "size": 28837, "crc": 152317927}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Segment$$ExternalSyntheticLambda0.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$Segment$$ExternalSyntheticLambda0.class", "size": 1288, "crc": 1795658994}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$NullEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$NullEntry.class", "size": 2478, "crc": 590137047}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$ManualSerializationProxy.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$ManualSerializationProxy.class", "size": 5838, "crc": 337420312}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$LocalManualCache.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$LocalManualCache.class", "size": 2319, "crc": -235959745}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$LoadingValueReference.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$LoadingValueReference.class", "size": 4937, "crc": -1903830891}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$LoadingValueReference$$ExternalSyntheticLambda0.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$LoadingValueReference$$ExternalSyntheticLambda0.class", "size": 817, "crc": 1787535171}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$KeySet.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$KeySet.class", "size": 1131, "crc": 1537095929}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$KeyIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$KeyIterator.class", "size": 876, "crc": 1563200}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$HashIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$HashIterator.class", "size": 3809, "crc": 437587226}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntrySet.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntrySet.class", "size": 1707, "crc": -1114464355}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryIterator.class", "size": 969, "crc": -1570111485}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory.class", "size": 4770, "crc": -255853825}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$8.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$8.class", "size": 1976, "crc": 1377445159}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$7.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$7.class", "size": 1926, "crc": 2126380438}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$6.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$6.class", "size": 1929, "crc": -375793203}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$5.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$5.class", "size": 1370, "crc": 1534072392}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$4.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$4.class", "size": 1883, "crc": 1463764547}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$3.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$3.class", "size": 1833, "crc": 175381755}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$2.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$2.class", "size": 1836, "crc": 2088904255}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$EntryFactory$1.class", "size": 1277, "crc": -1315465494}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AccessQueue.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AccessQueue.class", "size": 3288, "crc": -1547568772}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AccessQueue$2.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AccessQueue$2.class", "size": 1516, "crc": -1844979048}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AccessQueue$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AccessQueue$1.class", "size": 1408, "crc": 1863926236}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AbstractReferenceEntry.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AbstractReferenceEntry.class", "size": 2142, "crc": 966994355}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AbstractCacheSet.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$AbstractCacheSet.class", "size": 1113, "crc": 1445865810}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$2.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$2.class", "size": 1031, "crc": 1886349651}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/LocalCache$1.class", "size": 1187, "crc": 1559974367}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/ForwardingCache.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/ForwardingCache.class", "size": 1040, "crc": -1123806715}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/CacheStats.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/CacheStats.class", "size": 2004, "crc": 1907871606}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/CacheLoader.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/CacheLoader.class", "size": 564, "crc": -391212945}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/CacheLoader$InvalidCacheLoadException.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/CacheLoader$InvalidCacheLoadException.class", "size": 439, "crc": -999628222}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder.class", "size": 12244, "crc": -17270691}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$OneWeigher.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$OneWeigher.class", "size": 1125, "crc": -1814094648}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$NullListener.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$NullListener.class", "size": 1185, "crc": -1316955359}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$2.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$2.class", "size": 481, "crc": 1211408041}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$1.class", "size": 769, "crc": 1973422682}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$$ExternalSyntheticLambda0.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/CacheBuilder$$ExternalSyntheticLambda0.class", "size": 745, "crc": 185754979}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/Cache.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/Cache.class", "size": 378, "crc": 1809027927}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/AbstractCache$StatsCounter.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/AbstractCache$StatsCounter.class", "size": 321, "crc": -441567398}, {"key": "androidx/test/espresso/core/internal/deps/guava/cache/AbstractCache$SimpleStatsCounter.class", "name": "androidx/test/espresso/core/internal/deps/guava/cache/AbstractCache$SimpleStatsCounter.class", "size": 1369, "crc": 399472111}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Ticker.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Ticker.class", "size": 646, "crc": 1169183264}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Ticker$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Ticker$1.class", "size": 492, "crc": -694905337}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Throwables.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Throwables.class", "size": 2317, "crc": 1880007155}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Suppliers.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Suppliers.class", "size": 507, "crc": 1185597480}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Suppliers$SupplierOfInstance.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Suppliers$SupplierOfInstance.class", "size": 1378, "crc": -32322198}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Supplier.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Supplier.class", "size": 250, "crc": 1648241094}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Strings.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Strings.class", "size": 3004, "crc": -1882064338}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Stopwatch.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Stopwatch.class", "size": 2741, "crc": 1776593588}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Stopwatch$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Stopwatch$1.class", "size": 988, "crc": -307666695}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Present.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Present.class", "size": 2350, "crc": -1007004411}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Predicate.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Predicate.class", "size": 272, "crc": -475767761}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Preconditions.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Preconditions.class", "size": 3950, "crc": 203668280}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Platform.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Platform.class", "size": 1931, "crc": 1170464087}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Platform$JdkPatternCompiler.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Platform$JdkPatternCompiler.class", "size": 653, "crc": 868802904}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Platform$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Platform$1.class", "size": 278, "crc": -219547042}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/PatternCompiler.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/PatternCompiler.class", "size": 164, "crc": -2084820489}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Optional.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Optional.class", "size": 2168, "crc": 1431082950}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Optional$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Optional$1.class", "size": 849, "crc": 988711713}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Optional$1$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Optional$1$1.class", "size": 1292, "crc": 2082930209}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Objects.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Objects.class", "size": 559, "crc": -502745507}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/NullnessCasts.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/NullnessCasts.class", "size": 294, "crc": -1966842945}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects.class", "size": 1083, "crc": -1430486859}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects$ToStringHelper.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects$ToStringHelper.class", "size": 4969, "crc": -1565819118}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects$ToStringHelper$ValueHolder.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects$ToStringHelper$ValueHolder.class", "size": 887, "crc": 1349725766}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects$ToStringHelper$UnconditionalValueHolder.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects$ToStringHelper$UnconditionalValueHolder.class", "size": 832, "crc": -452508315}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/MoreObjects$1.class", "size": 287, "crc": -1490017172}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Joiner.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Joiner.class", "size": 1983, "crc": 53507381}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Function.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Function.class", "size": 350, "crc": -134290037}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/ExtraObjectsMethodsForWeb.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/ExtraObjectsMethodsForWeb.class", "size": 184, "crc": 1288908128}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Equivalence.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Equivalence.class", "size": 1155, "crc": 855471083}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Equivalence$Identity.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Equivalence$Identity.class", "size": 917, "crc": 1332608550}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Equivalence$Equals.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Equivalence$Equals.class", "size": 956, "crc": 1784972405}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Ascii.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Ascii.class", "size": 709, "crc": -1644578753}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/AbstractIterator.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/AbstractIterator.class", "size": 1887, "crc": 1642894061}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/AbstractIterator$State.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/AbstractIterator$State.class", "size": 1123, "crc": -679807389}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/AbstractIterator$1.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/AbstractIterator$1.class", "size": 923, "crc": 810751824}, {"key": "androidx/test/espresso/core/internal/deps/guava/base/Absent.class", "name": "androidx/test/espresso/core/internal/deps/guava/base/Absent.class", "size": 2384, "crc": 437324715}, {"key": "androidx/test/espresso/core/internal/deps/dagger/internal/Preconditions.class", "name": "androidx/test/espresso/core/internal/deps/dagger/internal/Preconditions.class", "size": 590, "crc": -1831205629}, {"key": "androidx/test/espresso/core/internal/deps/dagger/internal/DoubleCheck.class", "name": "androidx/test/espresso/core/internal/deps/dagger/internal/DoubleCheck.class", "size": 2155, "crc": 1993677069}, {"key": "androidx/test/espresso/core/internal/deps/aidl/TransactionInterceptor.class", "name": "androidx/test/espresso/core/internal/deps/aidl/TransactionInterceptor.class", "size": 415, "crc": -1081490242}, {"key": "androidx/test/espresso/core/internal/deps/aidl/Codecs.class", "name": "androidx/test/espresso/core/internal/deps/aidl/Codecs.class", "size": 1428, "crc": -1718389296}, {"key": "androidx/test/espresso/core/internal/deps/aidl/BaseStub.class", "name": "androidx/test/espresso/core/internal/deps/aidl/BaseStub.class", "size": 2439, "crc": -1936025637}, {"key": "androidx/test/espresso/core/internal/deps/aidl/BaseProxy.class", "name": "androidx/test/espresso/core/internal/deps/aidl/BaseProxy.class", "size": 1969, "crc": 1252228691}, {"key": "androidx/test/espresso/base/WindowManagerEventInjectionStrategy.class", "name": "androidx/test/espresso/base/WindowManagerEventInjectionStrategy.class", "size": 4295, "crc": -1363435998}, {"key": "androidx/test/espresso/base/ViewHierarchyExceptionHandler.class", "name": "androidx/test/espresso/base/ViewHierarchyExceptionHandler.class", "size": 5638, "crc": 669668369}, {"key": "androidx/test/espresso/base/ViewHierarchyExceptionHandler$Truncater.class", "name": "androidx/test/espresso/base/ViewHierarchyExceptionHandler$Truncater.class", "size": 500, "crc": 685152693}, {"key": "androidx/test/espresso/base/ViewHierarchyExceptionHandler$$ExternalSyntheticBackport0.class", "name": "androidx/test/espresso/base/ViewHierarchyExceptionHandler$$ExternalSyntheticBackport0.class", "size": 655, "crc": 1490533503}, {"key": "androidx/test/espresso/base/ViewFinderImpl_Factory.class", "name": "androidx/test/espresso/base/ViewFinderImpl_Factory.class", "size": 2069, "crc": 1157737398}, {"key": "androidx/test/espresso/base/ViewFinderImpl.class", "name": "androidx/test/espresso/base/ViewFinderImpl.class", "size": 5344, "crc": 204769872}, {"key": "androidx/test/espresso/base/ViewFinderImpl$MatcherPredicateAdapter.class", "name": "androidx/test/espresso/base/ViewFinderImpl$MatcherPredicateAdapter.class", "size": 1367, "crc": 1221058839}, {"key": "androidx/test/espresso/base/ViewFinderImpl$MatcherPredicateAdapter-IA.class", "name": "androidx/test/espresso/base/ViewFinderImpl$MatcherPredicateAdapter-IA.class", "size": 163, "crc": 1500561533}, {"key": "androidx/test/espresso/base/UiControllerModule_ProvideUiControllerFactory.class", "name": "androidx/test/espresso/base/UiControllerModule_ProvideUiControllerFactory.class", "size": 2379, "crc": 4556327}, {"key": "androidx/test/espresso/base/UiControllerModule.class", "name": "androidx/test/espresso/base/UiControllerModule.class", "size": 1096, "crc": -1867009642}, {"key": "androidx/test/espresso/base/UiControllerModule$EspressoUiControllerAdapter.class", "name": "androidx/test/espresso/base/UiControllerModule$EspressoUiControllerAdapter.class", "size": 2397, "crc": 1702889865}, {"key": "androidx/test/espresso/base/UiControllerModule$EspressoUiControllerAdapter-IA.class", "name": "androidx/test/espresso/base/UiControllerModule$EspressoUiControllerAdapter-IA.class", "size": 171, "crc": 520922366}, {"key": "androidx/test/espresso/base/UiControllerImpl_Factory.class", "name": "androidx/test/espresso/base/UiControllerImpl_Factory.class", "size": 4725, "crc": 821588664}, {"key": "androidx/test/espresso/base/UiControllerImpl.class", "name": "androidx/test/espresso/base/UiControllerImpl.class", "size": 18992, "crc": 818274683}, {"key": "androidx/test/espresso/base/UiControllerImpl$SignalingTask.class", "name": "androidx/test/espresso/base/UiControllerImpl$SignalingTask.class", "size": 1745, "crc": -946097152}, {"key": "androidx/test/espresso/base/UiControllerImpl$MainThreadInterrogation.class", "name": "androidx/test/espresso/base/UiControllerImpl$MainThreadInterrogation.class", "size": 4332, "crc": -1571266729}, {"key": "androidx/test/espresso/base/UiControllerImpl$InterrogationStatus.class", "name": "androidx/test/espresso/base/UiControllerImpl$InterrogationStatus.class", "size": 1444, "crc": 34923279}, {"key": "androidx/test/espresso/base/UiControllerImpl$IdleCondition.class", "name": "androidx/test/espresso/base/UiControllerImpl$IdleCondition.class", "size": 3537, "crc": -476597702}, {"key": "androidx/test/espresso/base/UiControllerImpl$7.class", "name": "androidx/test/espresso/base/UiControllerImpl$7.class", "size": 944, "crc": 1707203607}, {"key": "androidx/test/espresso/base/UiControllerImpl$6.class", "name": "androidx/test/espresso/base/UiControllerImpl$6.class", "size": 1441, "crc": -50436352}, {"key": "androidx/test/espresso/base/UiControllerImpl$5.class", "name": "androidx/test/espresso/base/UiControllerImpl$5.class", "size": 2056, "crc": -1232246926}, {"key": "androidx/test/espresso/base/UiControllerImpl$4.class", "name": "androidx/test/espresso/base/UiControllerImpl$4.class", "size": 1884, "crc": 531078802}, {"key": "androidx/test/espresso/base/UiControllerImpl$3.class", "name": "androidx/test/espresso/base/UiControllerImpl$3.class", "size": 1379, "crc": -1545498714}, {"key": "androidx/test/espresso/base/UiControllerImpl$2.class", "name": "androidx/test/espresso/base/UiControllerImpl$2.class", "size": 1367, "crc": -30173832}, {"key": "androidx/test/espresso/base/UiControllerImpl$1.class", "name": "androidx/test/espresso/base/UiControllerImpl$1.class", "size": 779, "crc": -1776871164}, {"key": "androidx/test/espresso/base/ThrowableHandler.class", "name": "androidx/test/espresso/base/ThrowableHandler.class", "size": 796, "crc": -717144085}, {"key": "androidx/test/espresso/base/ThreadPoolExecutorExtractor_Factory.class", "name": "androidx/test/espresso/base/ThreadPoolExecutorExtractor_Factory.class", "size": 1631, "crc": -72148456}, {"key": "androidx/test/espresso/base/ThreadPoolExecutorExtractor.class", "name": "androidx/test/espresso/base/ThreadPoolExecutorExtractor.class", "size": 4318, "crc": 1272887153}, {"key": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$5.class", "name": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$5.class", "size": 1797, "crc": -717187311}, {"key": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$4.class", "name": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$4.class", "size": 1838, "crc": -463748901}, {"key": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$3.class", "name": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$3.class", "size": 982, "crc": 1023667990}, {"key": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$2.class", "name": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$2.class", "size": 1977, "crc": -1708705742}, {"key": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$1.class", "name": "androidx/test/espresso/base/ThreadPoolExecutorExtractor$1.class", "size": 1189, "crc": 459636126}, {"key": "androidx/test/espresso/base/SdkAsyncTask.class", "name": "androidx/test/espresso/base/SdkAsyncTask.class", "size": 313, "crc": -51541080}, {"key": "androidx/test/espresso/base/RootsOracle_Factory.class", "name": "androidx/test/espresso/base/RootsOracle_Factory.class", "size": 1495, "crc": 868153908}, {"key": "androidx/test/espresso/base/RootsOracle.class", "name": "androidx/test/espresso/base/RootsOracle.class", "size": 5518, "crc": 796806753}, {"key": "androidx/test/espresso/base/RootViewPicker_RootResultFetcher_Factory.class", "name": "androidx/test/espresso/base/RootViewPicker_RootResultFetcher_Factory.class", "size": 2937, "crc": 9081759}, {"key": "androidx/test/espresso/base/RootViewPicker_Factory.class", "name": "androidx/test/espresso/base/RootViewPicker_Factory.class", "size": 4978, "crc": 1667468137}, {"key": "androidx/test/espresso/base/RootViewPickerScope.class", "name": "androidx/test/espresso/base/RootViewPickerScope.class", "size": 186, "crc": 1697648536}, {"key": "androidx/test/espresso/base/RootViewPicker.class", "name": "androidx/test/espresso/base/RootViewPicker.class", "size": 10097, "crc": -431465044}, {"key": "androidx/test/espresso/base/RootViewPicker$RootViewWithoutFocusException.class", "name": "androidx/test/espresso/base/RootViewPicker$RootViewWithoutFocusException.class", "size": 772, "crc": -1611339655}, {"key": "androidx/test/espresso/base/RootViewPicker$RootViewWithoutFocusException-IA.class", "name": "androidx/test/espresso/base/RootViewPicker$RootViewWithoutFocusException-IA.class", "size": 169, "crc": -2061020691}, {"key": "androidx/test/espresso/base/RootViewPicker$RootResults.class", "name": "androidx/test/espresso/base/RootViewPicker$RootResults.class", "size": 3582, "crc": -1063735556}, {"key": "androidx/test/espresso/base/RootViewPicker$RootResults-IA.class", "name": "androidx/test/espresso/base/RootViewPicker$RootResults-IA.class", "size": 151, "crc": -1229466540}, {"key": "androidx/test/espresso/base/RootViewPicker$RootResults$State.class", "name": "androidx/test/espresso/base/RootViewPicker$RootResults$State.class", "size": 1498, "crc": 578744656}, {"key": "androidx/test/espresso/base/RootViewPicker$RootResultFetcher.class", "name": "androidx/test/espresso/base/RootViewPicker$RootResultFetcher.class", "size": 2109, "crc": -1001624503}, {"key": "androidx/test/espresso/base/RootViewPicker$RootReadyBackoff.class", "name": "androidx/test/espresso/base/RootViewPicker$RootReadyBackoff.class", "size": 1994, "crc": -1306558965}, {"key": "androidx/test/espresso/base/RootViewPicker$NoMatchingRootBackoff.class", "name": "androidx/test/espresso/base/RootViewPicker$NoMatchingRootBackoff.class", "size": 1981, "crc": 626581786}, {"key": "androidx/test/espresso/base/RootViewPicker$NoActiveRootsBackoff.class", "name": "androidx/test/espresso/base/RootViewPicker$NoActiveRootsBackoff.class", "size": 1905, "crc": 814181705}, {"key": "androidx/test/espresso/base/RootViewPicker$BackOff.class", "name": "androidx/test/espresso/base/RootViewPicker$BackOff.class", "size": 1271, "crc": -2131469147}, {"key": "androidx/test/espresso/base/RootViewPicker$1.class", "name": "androidx/test/espresso/base/RootViewPicker$1.class", "size": 994, "crc": 454636875}, {"key": "androidx/test/espresso/base/PlatformTestStorageModule_ProvideTestStorageFactory.class", "name": "androidx/test/espresso/base/PlatformTestStorageModule_ProvideTestStorageFactory.class", "size": 1679, "crc": -1119310942}, {"key": "androidx/test/espresso/base/PlatformTestStorageModule.class", "name": "androidx/test/espresso/base/PlatformTestStorageModule.class", "size": 554, "crc": **********}, {"key": "androidx/test/espresso/base/PerformExceptionHandler.class", "name": "androidx/test/espresso/base/PerformExceptionHandler.class", "size": 4784, "crc": -**********}, {"key": "androidx/test/espresso/base/NoopRunnableIdleNotifier.class", "name": "androidx/test/espresso/base/NoopRunnableIdleNotifier.class", "size": 910, "crc": -936432938}, {"key": "androidx/test/espresso/base/NoopIdleNotificationCallbackIdleNotifierProvider.class", "name": "androidx/test/espresso/base/NoopIdleNotificationCallbackIdleNotifierProvider.class", "size": 1421, "crc": -**********}, {"key": "androidx/test/espresso/base/NoopIdleNotificationCallbackIdleNotifierProvider$NoopIdleNotificationCallbackIdleNotifier.class", "name": "androidx/test/espresso/base/NoopIdleNotificationCallbackIdleNotifierProvider$NoopIdleNotificationCallbackIdleNotifier.class", "size": 1735, "crc": -**********}, {"key": "androidx/test/espresso/base/NoopIdleNotificationCallbackIdleNotifierProvider$NoopIdleNotificationCallbackIdleNotifier-IA.class", "name": "androidx/test/espresso/base/NoopIdleNotificationCallbackIdleNotifierProvider$NoopIdleNotificationCallbackIdleNotifier-IA.class", "size": 214, "crc": -656625000}, {"key": "androidx/test/espresso/base/MainThread.class", "name": "androidx/test/espresso/base/MainThread.class", "size": 309, "crc": -**********}, {"key": "androidx/test/espresso/base/LooperIdlingResourceInterrogationHandler.class", "name": "androidx/test/espresso/base/LooperIdlingResourceInterrogationHandler.class", "size": 4925, "crc": -**********}, {"key": "androidx/test/espresso/base/LooperIdlingResourceInterrogationHandler$2.class", "name": "androidx/test/espresso/base/LooperIdlingResourceInterrogationHandler$2.class", "size": 1551, "crc": -**********}, {"key": "androidx/test/espresso/base/LooperIdlingResourceInterrogationHandler$1.class", "name": "androidx/test/espresso/base/LooperIdlingResourceInterrogationHandler$1.class", "size": 1540, "crc": -**********}, {"key": "androidx/test/espresso/base/InterruptableUiController.class", "name": "androidx/test/espresso/base/InterruptableUiController.class", "size": 241, "crc": 2101808466}, {"key": "androidx/test/espresso/base/Interrogator.class", "name": "androidx/test/espresso/base/Interrogator.class", "size": 7576, "crc": -1826685836}, {"key": "androidx/test/espresso/base/Interrogator$QueueInterrogationHandler.class", "name": "androidx/test/espresso/base/Interrogator$QueueInterrogationHandler.class", "size": 471, "crc": 1598542843}, {"key": "androidx/test/espresso/base/Interrogator$InterrogationHandler.class", "name": "androidx/test/espresso/base/Interrogator$InterrogationHandler.class", "size": 651, "crc": -2131580931}, {"key": "androidx/test/espresso/base/Interrogator$1.class", "name": "androidx/test/espresso/base/Interrogator$1.class", "size": 736, "crc": -749476852}, {"key": "androidx/test/espresso/base/InputManagerEventInjectionStrategy.class", "name": "androidx/test/espresso/base/InputManagerEventInjectionStrategy.class", "size": 5634, "crc": 1073411719}, {"key": "androidx/test/espresso/base/IdlingUiController.class", "name": "androidx/test/espresso/base/IdlingUiController.class", "size": 281, "crc": 1708583177}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry_Factory.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry_Factory.class", "size": 2102, "crc": -2122087637}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry.class", "size": 13032, "crc": 236397433}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$IdlingState.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$IdlingState.class", "size": 3685, "crc": -792423029}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$IdlingState-IA.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$IdlingState-IA.class", "size": 159, "crc": 982983157}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$IdleNotificationCallback.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$IdleNotificationCallback.class", "size": 485, "crc": 1928881724}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$Dispatcher.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$Dispatcher.class", "size": 5657, "crc": 1852567739}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$Dispatcher-IA.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$Dispatcher-IA.class", "size": 158, "crc": -2066050361}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$6.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$6.class", "size": 1664, "crc": -1168068311}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$5.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$5.class", "size": 1118, "crc": -1686324334}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$4.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$4.class", "size": 1202, "crc": -1475186331}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$3.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$3.class", "size": 1200, "crc": -1643363548}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$2.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$2.class", "size": 1205, "crc": 2015019242}, {"key": "androidx/test/espresso/base/IdlingResourceRegistry$1.class", "name": "androidx/test/espresso/base/IdlingResourceRegistry$1.class", "size": 864, "crc": 422806645}, {"key": "androidx/test/espresso/base/IdleNotifier.class", "name": "androidx/test/espresso/base/IdleNotifier.class", "size": 335, "crc": 893846049}, {"key": "androidx/test/espresso/base/EventInjector.class", "name": "androidx/test/espresso/base/EventInjector.class", "size": 2299, "crc": 926289607}, {"key": "androidx/test/espresso/base/EventInjectionStrategy.class", "name": "androidx/test/espresso/base/EventInjectionStrategy.class", "size": 358, "crc": -1199489775}, {"key": "androidx/test/espresso/base/EspressoExceptionHandler.class", "name": "androidx/test/espresso/base/EspressoExceptionHandler.class", "size": 1598, "crc": 1444048003}, {"key": "androidx/test/espresso/base/DefaultFailureHandler_Factory.class", "name": "androidx/test/espresso/base/DefaultFailureHandler_Factory.class", "size": 2202, "crc": -702435246}, {"key": "androidx/test/espresso/base/DefaultFailureHandler.class", "name": "androidx/test/espresso/base/DefaultFailureHandler.class", "size": 7530, "crc": 971706901}, {"key": "androidx/test/espresso/base/DefaultFailureHandler$TypedFailureHandler.class", "name": "androidx/test/espresso/base/DefaultFailureHandler$TypedFailureHandler.class", "size": 1818, "crc": -152501789}, {"key": "androidx/test/espresso/base/DefaultFailureHandler$$ExternalSyntheticLambda1.class", "name": "androidx/test/espresso/base/DefaultFailureHandler$$ExternalSyntheticLambda1.class", "size": 900, "crc": 1557808818}, {"key": "androidx/test/espresso/base/DefaultFailureHandler$$ExternalSyntheticLambda0.class", "name": "androidx/test/espresso/base/DefaultFailureHandler$$ExternalSyntheticLambda0.class", "size": 918, "crc": 712026559}, {"key": "androidx/test/espresso/base/Default.class", "name": "androidx/test/espresso/base/Default.class", "size": 303, "crc": -536487979}, {"key": "androidx/test/espresso/base/ConfigurationSynchronizationUtils.class", "name": "androidx/test/espresso/base/ConfigurationSynchronizationUtils.class", "size": 3178, "crc": 487927144}, {"key": "androidx/test/espresso/base/CompatAsyncTask.class", "name": "androidx/test/espresso/base/CompatAsyncTask.class", "size": 319, "crc": -236214644}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvidesTracingFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvidesTracingFactory.class", "size": 1546, "crc": **********}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideTargetContextFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideTargetContextFactory.class", "size": 1511, "crc": -**********}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideSdkAsyncTaskMonitorFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideSdkAsyncTaskMonitorFactory.class", "size": 2690, "crc": -124513073}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideRemoteExecutorFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideRemoteExecutorFactory.class", "size": 1776, "crc": **********}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideMainThreadExecutorFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideMainThreadExecutorFactory.class", "size": 2204, "crc": -**********}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideMainLooperFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideMainLooperFactory.class", "size": 1472, "crc": -313452607}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideLifecycleMonitorFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideLifecycleMonitorFactory.class", "size": 1654, "crc": **********}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideFailureHandlerFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideFailureHandlerFactory.class", "size": 2595, "crc": -**********}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideFailureHanderFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideFailureHanderFactory.class", "size": 2426, "crc": -148744210}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideEventInjectorFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideEventInjectorFactory.class", "size": 1583, "crc": -1199232020}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideDynamicNotiferFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideDynamicNotiferFactory.class", "size": 3059, "crc": 817686652}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideDefaultFailureHanderFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideDefaultFailureHanderFactory.class", "size": 2927, "crc": -2057064014}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideControlledLooperFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideControlledLooperFactory.class", "size": 1638, "crc": 1645727584}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideCompatAsyncTaskMonitorFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideCompatAsyncTaskMonitorFactory.class", "size": 2708, "crc": -92740650}, {"key": "androidx/test/espresso/base/BaseLayerModule_ProvideActiveRootListerFactory.class", "name": "androidx/test/espresso/base/BaseLayerModule_ProvideActiveRootListerFactory.class", "size": 2377, "crc": -6625224}, {"key": "androidx/test/espresso/base/BaseLayerModule_FailureHandlerHolder_Factory.class", "name": "androidx/test/espresso/base/BaseLayerModule_FailureHandlerHolder_Factory.class", "size": 1971, "crc": 1497099122}, {"key": "androidx/test/espresso/base/BaseLayerModule.class", "name": "androidx/test/espresso/base/BaseLayerModule.class", "size": 8370, "crc": 637627424}, {"key": "androidx/test/espresso/base/BaseLayerModule$FailureHandlerHolder.class", "name": "androidx/test/espresso/base/BaseLayerModule$FailureHandlerHolder.class", "size": 1271, "crc": -539808562}, {"key": "androidx/test/espresso/base/BaseLayerModule$1.class", "name": "androidx/test/espresso/base/BaseLayerModule$1.class", "size": 931, "crc": 1329590143}, {"key": "androidx/test/espresso/base/BaseLayerModule$$ExternalSyntheticLambda0.class", "name": "androidx/test/espresso/base/BaseLayerModule$$ExternalSyntheticLambda0.class", "size": 712, "crc": -571700261}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor.class", "size": 3635, "crc": -861119000}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor.class", "size": 3756, "crc": 1639572670}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor-IA.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor-IA.class", "size": 157, "crc": 1125101614}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor$2.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor$2.class", "size": 2378, "crc": -302307910}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor$1.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor$1.class", "size": 2029, "crc": -546621120}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor$1$$ExternalSyntheticBackportWithForwarding0.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor$1$$ExternalSyntheticBackportWithForwarding0.class", "size": 540, "crc": -475516105}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor$$ExternalSyntheticBackportWithForwarding0.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor$IdleMonitor$$ExternalSyntheticBackportWithForwarding0.class", "size": 538, "crc": -1865721632}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor$BarrierRestarter.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor$BarrierRestarter.class", "size": 1066, "crc": 647571471}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor$1.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor$1.class", "size": 1351, "crc": -86856743}, {"key": "androidx/test/espresso/base/AsyncTaskPoolMonitor$$ExternalSyntheticBackportWithForwarding0.class", "name": "androidx/test/espresso/base/AsyncTaskPoolMonitor$$ExternalSyntheticBackportWithForwarding0.class", "size": 526, "crc": -503128830}, {"key": "androidx/test/espresso/base/AssertionErrorHandler.class", "name": "androidx/test/espresso/base/AssertionErrorHandler.class", "size": 1578, "crc": 917637428}, {"key": "androidx/test/espresso/base/AssertionErrorHandler$AssertionFailedWithCauseError.class", "name": "androidx/test/espresso/base/AssertionErrorHandler$AssertionFailedWithCauseError.class", "size": 772, "crc": -2062954121}, {"key": "androidx/test/espresso/base/ActiveRootLister.class", "name": "androidx/test/espresso/base/ActiveRootLister.class", "size": 260, "crc": -2040754349}, {"key": "androidx/test/espresso/assertion/ViewAssertions.class", "name": "androidx/test/espresso/assertion/ViewAssertions.class", "size": 2261, "crc": -710759056}, {"key": "androidx/test/espresso/assertion/ViewAssertions$SelectedDescendantsMatchViewAssertion.class", "name": "androidx/test/espresso/assertion/ViewAssertions$SelectedDescendantsMatchViewAssertion.class", "size": 3679, "crc": -186141536}, {"key": "androidx/test/espresso/assertion/ViewAssertions$SelectedDescendantsMatchViewAssertion-IA.class", "name": "androidx/test/espresso/assertion/ViewAssertions$SelectedDescendantsMatchViewAssertion-IA.class", "size": 182, "crc": -60606247}, {"key": "androidx/test/espresso/assertion/ViewAssertions$SelectedDescendantsMatchViewAssertion$1.class", "name": "androidx/test/espresso/assertion/ViewAssertions$SelectedDescendantsMatchViewAssertion$1.class", "size": 1629, "crc": 629179085}, {"key": "androidx/test/espresso/assertion/ViewAssertions$MatchesViewAssertion.class", "name": "androidx/test/espresso/assertion/ViewAssertions$MatchesViewAssertion.class", "size": 2232, "crc": 1829425324}, {"key": "androidx/test/espresso/assertion/ViewAssertions$MatchesViewAssertion-IA.class", "name": "androidx/test/espresso/assertion/ViewAssertions$MatchesViewAssertion-IA.class", "size": 165, "crc": -537983958}, {"key": "androidx/test/espresso/assertion/ViewAssertions$DoesNotExistViewAssertion.class", "name": "androidx/test/espresso/assertion/ViewAssertions$DoesNotExistViewAssertion.class", "size": 1719, "crc": 69328113}, {"key": "androidx/test/espresso/assertion/ViewAssertions$DoesNotExistViewAssertion-IA.class", "name": "androidx/test/espresso/assertion/ViewAssertions$DoesNotExistViewAssertion-IA.class", "size": 170, "crc": 939277730}, {"key": "androidx/test/espresso/assertion/PositionAssertions.class", "name": "androidx/test/espresso/assertion/PositionAssertions.class", "size": 7587, "crc": 1868240811}, {"key": "androidx/test/espresso/assertion/PositionAssertions$Position.class", "name": "androidx/test/espresso/assertion/PositionAssertions$Position.class", "size": 2559, "crc": -74443644}, {"key": "androidx/test/espresso/assertion/PositionAssertions$3.class", "name": "androidx/test/espresso/assertion/PositionAssertions$3.class", "size": 1454, "crc": 2057150558}, {"key": "androidx/test/espresso/assertion/PositionAssertions$2.class", "name": "androidx/test/espresso/assertion/PositionAssertions$2.class", "size": 1147, "crc": -522934977}, {"key": "androidx/test/espresso/assertion/PositionAssertions$1.class", "name": "androidx/test/espresso/assertion/PositionAssertions$1.class", "size": 2988, "crc": -509032529}, {"key": "androidx/test/espresso/assertion/LayoutAssertions.class", "name": "androidx/test/espresso/assertion/LayoutAssertions.class", "size": 2922, "crc": 1752228970}, {"key": "androidx/test/espresso/assertion/LayoutAssertions$NoOverlapsViewAssertion.class", "name": "androidx/test/espresso/assertion/LayoutAssertions$NoOverlapsViewAssertion.class", "size": 3827, "crc": -1175623681}, {"key": "androidx/test/espresso/assertion/LayoutAssertions$NoOverlapsViewAssertion-IA.class", "name": "androidx/test/espresso/assertion/LayoutAssertions$NoOverlapsViewAssertion-IA.class", "size": 170, "crc": 2125883359}, {"key": "androidx/test/espresso/assertion/LayoutAssertions$NoOverlapsViewAssertion$1.class", "name": "androidx/test/espresso/assertion/LayoutAssertions$NoOverlapsViewAssertion$1.class", "size": 1547, "crc": -128392979}, {"key": "androidx/test/espresso/action/ViewActions.class", "name": "androidx/test/espresso/action/ViewActions.class", "size": 9116, "crc": -186618568}, {"key": "androidx/test/espresso/action/ViewActions$1.class", "name": "androidx/test/espresso/action/ViewActions$1.class", "size": 2474, "crc": 748944505}, {"key": "androidx/test/espresso/action/TypeTextAction.class", "name": "androidx/test/espresso/action/TypeTextAction.class", "size": 5287, "crc": 832580703}, {"key": "androidx/test/espresso/action/TranslatedCoordinatesProvider.class", "name": "androidx/test/espresso/action/TranslatedCoordinatesProvider.class", "size": 978, "crc": -8244875}, {"key": "androidx/test/espresso/action/Tapper.class", "name": "androidx/test/espresso/action/Tapper.class", "size": 506, "crc": **********}, {"key": "androidx/test/espresso/action/Tapper$Status.class", "name": "androidx/test/espresso/action/Tapper$Status.class", "size": 1279, "crc": 1906094}, {"key": "androidx/test/espresso/action/Tap.class", "name": "androidx/test/espresso/action/Tap.class", "size": 4602, "crc": -**********}, {"key": "androidx/test/espresso/action/Tap-IA.class", "name": "androidx/test/espresso/action/Tap-IA.class", "size": 130, "crc": **********}, {"key": "androidx/test/espresso/action/Tap$3.class", "name": "androidx/test/espresso/action/Tap$3.class", "size": 1847, "crc": 804925563}, {"key": "androidx/test/espresso/action/Tap$3-IA.class", "name": "androidx/test/espresso/action/Tap$3-IA.class", "size": 132, "crc": -355539222}, {"key": "androidx/test/espresso/action/Tap$2.class", "name": "androidx/test/espresso/action/Tap$2.class", "size": 2375, "crc": -11784988}, {"key": "androidx/test/espresso/action/Tap$2-IA.class", "name": "androidx/test/espresso/action/Tap$2-IA.class", "size": 132, "crc": -514901397}, {"key": "androidx/test/espresso/action/Tap$1.class", "name": "androidx/test/espresso/action/Tap$1.class", "size": 1553, "crc": -154864116}, {"key": "androidx/test/espresso/action/Tap$1-IA.class", "name": "androidx/test/espresso/action/Tap$1-IA.class", "size": 132, "crc": -36872216}, {"key": "androidx/test/espresso/action/Swiper.class", "name": "androidx/test/espresso/action/Swiper.class", "size": 325, "crc": -1639801242}, {"key": "androidx/test/espresso/action/Swiper$Status.class", "name": "androidx/test/espresso/action/Swiper$Status.class", "size": 1223, "crc": -1741670608}, {"key": "androidx/test/espresso/action/Swipe.class", "name": "androidx/test/espresso/action/Swipe.class", "size": 4151, "crc": 969173231}, {"key": "androidx/test/espresso/action/Swipe-IA.class", "name": "androidx/test/espresso/action/Swipe-IA.class", "size": 132, "crc": 626276716}, {"key": "androidx/test/espresso/action/Swipe$2.class", "name": "androidx/test/espresso/action/Swipe$2.class", "size": 1148, "crc": 919125620}, {"key": "androidx/test/espresso/action/Swipe$2-IA.class", "name": "androidx/test/espresso/action/Swipe$2-IA.class", "size": 134, "crc": -2111212606}, {"key": "androidx/test/espresso/action/Swipe$1.class", "name": "androidx/test/espresso/action/Swipe$1.class", "size": 1148, "crc": 122705370}, {"key": "androidx/test/espresso/action/Swipe$1-IA.class", "name": "androidx/test/espresso/action/Swipe$1-IA.class", "size": 134, "crc": -1632954815}, {"key": "androidx/test/espresso/action/ScrollToAction.class", "name": "androidx/test/espresso/action/ScrollToAction.class", "size": 4015, "crc": -732086766}, {"key": "androidx/test/espresso/action/ScrollToAction$1.class", "name": "androidx/test/espresso/action/ScrollToAction$1.class", "size": 754, "crc": -1198166645}, {"key": "androidx/test/espresso/action/ReplaceTextAction.class", "name": "androidx/test/espresso/action/ReplaceTextAction.class", "size": 1837, "crc": 383678310}, {"key": "androidx/test/espresso/action/RepeatActionUntilViewState.class", "name": "androidx/test/espresso/action/RepeatActionUntilViewState.class", "size": 3226, "crc": 755184491}, {"key": "androidx/test/espresso/action/PressBackAction.class", "name": "androidx/test/espresso/action/PressBackAction.class", "size": 1768, "crc": 1302370014}, {"key": "androidx/test/espresso/action/Press.class", "name": "androidx/test/espresso/action/Press.class", "size": 1778, "crc": 1501564255}, {"key": "androidx/test/espresso/action/Press-IA.class", "name": "androidx/test/espresso/action/Press-IA.class", "size": 132, "crc": 450672981}, {"key": "androidx/test/espresso/action/Press$3.class", "name": "androidx/test/espresso/action/Press$3.class", "size": 687, "crc": 518430625}, {"key": "androidx/test/espresso/action/Press$3-IA.class", "name": "androidx/test/espresso/action/Press$3-IA.class", "size": 134, "crc": 1788124753}, {"key": "androidx/test/espresso/action/Press$2.class", "name": "androidx/test/espresso/action/Press$2.class", "size": 687, "crc": -1694327114}, {"key": "androidx/test/espresso/action/Press$2-IA.class", "name": "androidx/test/espresso/action/Press$2-IA.class", "size": 134, "crc": 1628783312}, {"key": "androidx/test/espresso/action/Press$1.class", "name": "androidx/test/espresso/action/Press$1.class", "size": 680, "crc": 1537103045}, {"key": "androidx/test/espresso/action/Press$1-IA.class", "name": "androidx/test/espresso/action/Press$1-IA.class", "size": 134, "crc": 2107058003}, {"key": "androidx/test/espresso/action/PrecisionDescriber.class", "name": "androidx/test/espresso/action/PrecisionDescriber.class", "size": 182, "crc": 1897713179}, {"key": "androidx/test/espresso/action/OpenLinkAction.class", "name": "androidx/test/espresso/action/OpenLinkAction.class", "size": 4538, "crc": 1406718255}, {"key": "androidx/test/espresso/action/MotionEvents.class", "name": "androidx/test/espresso/action/MotionEvents.class", "size": 8730, "crc": 1377646194}, {"key": "androidx/test/espresso/action/MotionEvents$DownResultHolder.class", "name": "androidx/test/espresso/action/MotionEvents$DownResultHolder.class", "size": 616, "crc": 366381613}, {"key": "androidx/test/espresso/action/KeyEventActionBase.class", "name": "androidx/test/espresso/action/KeyEventActionBase.class", "size": 6492, "crc": -38052376}, {"key": "androidx/test/espresso/action/KeyEventAction.class", "name": "androidx/test/espresso/action/KeyEventAction.class", "size": 1371, "crc": 2021940755}, {"key": "androidx/test/espresso/action/GeneralSwipeAction.class", "name": "androidx/test/espresso/action/GeneralSwipeAction.class", "size": 4268, "crc": 2099351756}, {"key": "androidx/test/espresso/action/GeneralLocation.class", "name": "androidx/test/espresso/action/GeneralLocation.class", "size": 5179, "crc": -757054955}, {"key": "androidx/test/espresso/action/GeneralLocation-IA.class", "name": "androidx/test/espresso/action/GeneralLocation-IA.class", "size": 142, "crc": 1355361718}, {"key": "androidx/test/espresso/action/GeneralLocation$Position.class", "name": "androidx/test/espresso/action/GeneralLocation$Position.class", "size": 2024, "crc": 881862064}, {"key": "androidx/test/espresso/action/GeneralLocation$Position-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$Position-IA.class", "size": 151, "crc": 814898076}, {"key": "androidx/test/espresso/action/GeneralLocation$Position$3.class", "name": "androidx/test/espresso/action/GeneralLocation$Position$3.class", "size": 894, "crc": -141418019}, {"key": "androidx/test/espresso/action/GeneralLocation$Position$3-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$Position$3-IA.class", "size": 153, "crc": -1548979691}, {"key": "androidx/test/espresso/action/GeneralLocation$Position$2.class", "name": "androidx/test/espresso/action/GeneralLocation$Position$2.class", "size": 897, "crc": -1588568367}, {"key": "androidx/test/espresso/action/GeneralLocation$Position$2-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$Position$2-IA.class", "size": 153, "crc": -1473401196}, {"key": "androidx/test/espresso/action/GeneralLocation$Position$1.class", "name": "androidx/test/espresso/action/GeneralLocation$Position$1.class", "size": 867, "crc": -363379304}, {"key": "androidx/test/espresso/action/GeneralLocation$Position$1-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$Position$1-IA.class", "size": 153, "crc": -1263547625}, {"key": "androidx/test/espresso/action/GeneralLocation$9.class", "name": "androidx/test/espresso/action/GeneralLocation$9.class", "size": 1131, "crc": -1967918513}, {"key": "androidx/test/espresso/action/GeneralLocation$9-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$9-IA.class", "size": 144, "crc": -506281492}, {"key": "androidx/test/espresso/action/GeneralLocation$8.class", "name": "androidx/test/espresso/action/GeneralLocation$8.class", "size": 1150, "crc": 63339469}, {"key": "androidx/test/espresso/action/GeneralLocation$8-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$8-IA.class", "size": 144, "crc": -363651731}, {"key": "androidx/test/espresso/action/GeneralLocation$7.class", "name": "androidx/test/espresso/action/GeneralLocation$7.class", "size": 1149, "crc": 607145266}, {"key": "androidx/test/espresso/action/GeneralLocation$7-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$7-IA.class", "size": 144, "crc": -2066157854}, {"key": "androidx/test/espresso/action/GeneralLocation$6.class", "name": "androidx/test/espresso/action/GeneralLocation$6.class", "size": 1150, "crc": -828448929}, {"key": "androidx/test/espresso/action/GeneralLocation$6-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$6-IA.class", "size": 144, "crc": -1889979805}, {"key": "androidx/test/espresso/action/GeneralLocation$5.class", "name": "androidx/test/espresso/action/GeneralLocation$5.class", "size": 1134, "crc": 1599723002}, {"key": "androidx/test/espresso/action/GeneralLocation$5-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$5-IA.class", "size": 144, "crc": -1814341664}, {"key": "androidx/test/espresso/action/GeneralLocation$4.class", "name": "androidx/test/espresso/action/GeneralLocation$4.class", "size": 1152, "crc": -309214765}, {"key": "androidx/test/espresso/action/GeneralLocation$4-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$4-IA.class", "size": 144, "crc": -1738896543}, {"key": "androidx/test/espresso/action/GeneralLocation$3.class", "name": "androidx/test/espresso/action/GeneralLocation$3.class", "size": 1149, "crc": 2033342301}, {"key": "androidx/test/espresso/action/GeneralLocation$3-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$3-IA.class", "size": 144, "crc": -1428185882}, {"key": "androidx/test/espresso/action/GeneralLocation$2.class", "name": "androidx/test/espresso/action/GeneralLocation$2.class", "size": 1152, "crc": 1331241710}, {"key": "androidx/test/espresso/action/GeneralLocation$2-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$2-IA.class", "size": 144, "crc": -1587658649}, {"key": "androidx/test/espresso/action/GeneralLocation$10.class", "name": "androidx/test/espresso/action/GeneralLocation$10.class", "size": 1150, "crc": -1910182870}, {"key": "androidx/test/espresso/action/GeneralLocation$10-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$10-IA.class", "size": 145, "crc": -751485592}, {"key": "androidx/test/espresso/action/GeneralLocation$1.class", "name": "androidx/test/espresso/action/GeneralLocation$1.class", "size": 1133, "crc": -1150296250}, {"key": "androidx/test/espresso/action/GeneralLocation$1-IA.class", "name": "androidx/test/espresso/action/GeneralLocation$1-IA.class", "size": 144, "crc": -1109645852}, {"key": "androidx/test/espresso/action/GeneralClickAction.class", "name": "androidx/test/espresso/action/GeneralClickAction.class", "size": 6777, "crc": 437344579}, {"key": "androidx/test/espresso/action/EspressoKey.class", "name": "androidx/test/espresso/action/EspressoKey.class", "size": 1813, "crc": -1207440564}, {"key": "androidx/test/espresso/action/EspressoKey-IA.class", "name": "androidx/test/espresso/action/EspressoKey-IA.class", "size": 138, "crc": -821992445}, {"key": "androidx/test/espresso/action/EspressoKey$Builder.class", "name": "androidx/test/espresso/action/EspressoKey$Builder.class", "size": 2149, "crc": -792045986}, {"key": "androidx/test/espresso/action/EditorAction.class", "name": "androidx/test/espresso/action/EditorAction.class", "size": 2788, "crc": -**********}, {"key": "androidx/test/espresso/action/CoordinatesProvider.class", "name": "androidx/test/espresso/action/CoordinatesProvider.class", "size": 206, "crc": -**********}, {"key": "androidx/test/espresso/action/CloseKeyboardAction.class", "name": "androidx/test/espresso/action/CloseKeyboardAction.class", "size": 6244, "crc": -**********}, {"key": "androidx/test/espresso/action/CloseKeyboardAction$CloseKeyboardIdlingResult.class", "name": "androidx/test/espresso/action/CloseKeyboardAction$CloseKeyboardIdlingResult.class", "size": 3345, "crc": 241090904}, {"key": "androidx/test/espresso/action/CloseKeyboardAction$CloseKeyboardIdlingResult-IA.class", "name": "androidx/test/espresso/action/CloseKeyboardAction$CloseKeyboardIdlingResult-IA.class", "size": 172, "crc": **********}, {"key": "androidx/test/espresso/action/CloseKeyboardAction$CloseKeyboardIdlingResult$2.class", "name": "androidx/test/espresso/action/CloseKeyboardAction$CloseKeyboardIdlingResult$2.class", "size": 1475, "crc": **********}, {"key": "androidx/test/espresso/action/CloseKeyboardAction$CloseKeyboardIdlingResult$1.class", "name": "androidx/test/espresso/action/CloseKeyboardAction$CloseKeyboardIdlingResult$1.class", "size": 1616, "crc": -488895368}, {"key": "androidx/test/espresso/action/AdapterViewProtocols.class", "name": "androidx/test/espresso/action/AdapterViewProtocols.class", "size": 978, "crc": -842729144}, {"key": "androidx/test/espresso/action/AdapterViewProtocols$StandardAdapterViewProtocol.class", "name": "androidx/test/espresso/action/AdapterViewProtocols$StandardAdapterViewProtocol.class", "size": 5965, "crc": -384915671}, {"key": "androidx/test/espresso/action/AdapterViewProtocols$StandardAdapterViewProtocol$StandardDataFunction.class", "name": "androidx/test/espresso/action/AdapterViewProtocols$StandardAdapterViewProtocol$StandardDataFunction.class", "size": 2051, "crc": -1512422992}, {"key": "androidx/test/espresso/action/AdapterViewProtocols$StandardAdapterViewProtocol$StandardDataFunction-IA.class", "name": "androidx/test/espresso/action/AdapterViewProtocols$StandardAdapterViewProtocol$StandardDataFunction-IA.class", "size": 196, "crc": 1777603468}, {"key": "androidx/test/espresso/action/AdapterViewProtocol.class", "name": "androidx/test/espresso/action/AdapterViewProtocol.class", "size": 1651, "crc": 364045819}, {"key": "androidx/test/espresso/action/AdapterViewProtocol$DataFunction.class", "name": "androidx/test/espresso/action/AdapterViewProtocol$DataFunction.class", "size": 304, "crc": 457209788}, {"key": "androidx/test/espresso/action/AdapterViewProtocol$AdaptedData.class", "name": "androidx/test/espresso/action/AdapterViewProtocol$AdaptedData.class", "size": 2262, "crc": -491936599}, {"key": "androidx/test/espresso/action/AdapterViewProtocol$AdaptedData-IA.class", "name": "androidx/test/espresso/action/AdapterViewProtocol$AdaptedData-IA.class", "size": 158, "crc": 1280929218}, {"key": "androidx/test/espresso/action/AdapterViewProtocol$AdaptedData$Builder.class", "name": "androidx/test/espresso/action/AdapterViewProtocol$AdaptedData$Builder.class", "size": 2124, "crc": -1269629492}, {"key": "androidx/test/espresso/action/AdapterViewProtocol$AdaptedData$Builder$1.class", "name": "androidx/test/espresso/action/AdapterViewProtocol$AdaptedData$Builder$1.class", "size": 1269, "crc": -1906652254}, {"key": "androidx/test/espresso/action/AdapterDataLoaderAction.class", "name": "androidx/test/espresso/action/AdapterDataLoaderAction.class", "size": 6759, "crc": 292383584}, {"key": "androidx/test/espresso/ViewInteraction_Factory.class", "name": "androidx/test/espresso/ViewInteraction_Factory.class", "size": 8847, "crc": 628167125}, {"key": "androidx/test/espresso/ViewInteractionModule_ProvideViewMatcherFactory.class", "name": "androidx/test/espresso/ViewInteractionModule_ProvideViewMatcherFactory.class", "size": 1682, "crc": -**********}, {"key": "androidx/test/espresso/ViewInteractionModule_ProvideViewFinderFactory.class", "name": "androidx/test/espresso/ViewInteractionModule_ProvideViewFinderFactory.class", "size": 2356, "crc": 643459885}, {"key": "androidx/test/espresso/ViewInteractionModule_ProvideTestFlowVisualizerFactory.class", "name": "androidx/test/espresso/ViewInteractionModule_ProvideTestFlowVisualizerFactory.class", "size": 2565, "crc": **********}, {"key": "androidx/test/espresso/ViewInteractionModule_ProvideRootViewFactory.class", "name": "androidx/test/espresso/ViewInteractionModule_ProvideRootViewFactory.class", "size": 2284, "crc": -**********}, {"key": "androidx/test/espresso/ViewInteractionModule_ProvideRootMatcherFactory.class", "name": "androidx/test/espresso/ViewInteractionModule_ProvideRootMatcherFactory.class", "size": 1922, "crc": -764080518}, {"key": "androidx/test/espresso/ViewInteractionModule_ProvideRemoteInteractionFactory.class", "name": "androidx/test/espresso/ViewInteractionModule_ProvideRemoteInteractionFactory.class", "size": 1641, "crc": -782983629}, {"key": "androidx/test/espresso/ViewInteractionModule_ProvideNeedsActivityFactory.class", "name": "androidx/test/espresso/ViewInteractionModule_ProvideNeedsActivityFactory.class", "size": 1830, "crc": 590349500}, {"key": "androidx/test/espresso/ViewInteractionModule.class", "name": "androidx/test/espresso/ViewInteractionModule.class", "size": 2935, "crc": -**********}, {"key": "androidx/test/espresso/ViewInteractionComponent.class", "name": "androidx/test/espresso/ViewInteractionComponent.class", "size": 317, "crc": 3566087}, {"key": "androidx/test/espresso/ViewInteraction.class", "name": "androidx/test/espresso/ViewInteraction.class", "size": 15961, "crc": -462143814}, {"key": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAssertion.class", "name": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAssertion.class", "size": 3122, "crc": **********}, {"key": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAssertion-IA.class", "name": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAssertion-IA.class", "size": 164, "crc": -838280492}, {"key": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAssertion$1.class", "name": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAssertion$1.class", "size": 1112, "crc": -1512806218}, {"key": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAction.class", "name": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAction.class", "size": 4236, "crc": 1023902071}, {"key": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAction-IA.class", "name": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAction-IA.class", "size": 161, "crc": -256558462}, {"key": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAction$1.class", "name": "androidx/test/espresso/ViewInteraction$SingleExecutionViewAction$1.class", "size": 1097, "crc": -2127399096}, {"key": "androidx/test/espresso/ViewInteraction$2.class", "name": "androidx/test/espresso/ViewInteraction$2.class", "size": 3684, "crc": 1367649713}, {"key": "androidx/test/espresso/ViewInteraction$2$$ExternalSyntheticBackport0.class", "name": "androidx/test/espresso/ViewInteraction$2$$ExternalSyntheticBackport0.class", "size": 638, "crc": -735817841}, {"key": "androidx/test/espresso/ViewInteraction$1.class", "name": "androidx/test/espresso/ViewInteraction$1.class", "size": 2842, "crc": 1063757683}, {"key": "androidx/test/espresso/ViewInteraction$1$$ExternalSyntheticBackport0.class", "name": "androidx/test/espresso/ViewInteraction$1$$ExternalSyntheticBackport0.class", "size": 638, "crc": 515994373}, {"key": "androidx/test/espresso/ViewFinder.class", "name": "androidx/test/espresso/ViewFinder.class", "size": 301, "crc": 109407020}, {"key": "androidx/test/espresso/ViewAssertion.class", "name": "androidx/test/espresso/ViewAssertion.class", "size": 219, "crc": 308491311}, {"key": "androidx/test/espresso/ViewAction.class", "name": "androidx/test/espresso/ViewAction.class", "size": 372, "crc": 19046283}, {"key": "androidx/test/espresso/UiController.class", "name": "androidx/test/espresso/UiController.class", "size": 617, "crc": 495000897}, {"key": "androidx/test/espresso/UiController$-CC.class", "name": "androidx/test/espresso/UiController$-CC.class", "size": 1374, "crc": -1536588367}, {"key": "androidx/test/espresso/RootViewException.class", "name": "androidx/test/espresso/RootViewException.class", "size": 232, "crc": -1114765755}, {"key": "androidx/test/espresso/Root.class", "name": "androidx/test/espresso/Root.class", "size": 3461, "crc": 1055217149}, {"key": "androidx/test/espresso/Root-IA.class", "name": "androidx/test/espresso/Root-IA.class", "size": 124, "crc": -2069876235}, {"key": "androidx/test/espresso/Root$Builder.class", "name": "androidx/test/espresso/Root$Builder.class", "size": 1412, "crc": -1846577762}, {"key": "androidx/test/espresso/PerformException.class", "name": "androidx/test/espresso/PerformException.class", "size": 2010, "crc": -582027725}, {"key": "androidx/test/espresso/PerformException-IA.class", "name": "androidx/test/espresso/PerformException-IA.class", "size": 136, "crc": 52593521}, {"key": "androidx/test/espresso/PerformException$Builder.class", "name": "androidx/test/espresso/PerformException$Builder.class", "size": 1950, "crc": -973187943}, {"key": "androidx/test/espresso/NoMatchingViewException.class", "name": "androidx/test/espresso/NoMatchingViewException.class", "size": 4887, "crc": -1520143806}, {"key": "androidx/test/espresso/NoMatchingViewException-IA.class", "name": "androidx/test/espresso/NoMatchingViewException-IA.class", "size": 143, "crc": -101542950}, {"key": "androidx/test/espresso/NoMatchingViewException$Builder.class", "name": "androidx/test/espresso/NoMatchingViewException$Builder.class", "size": 5365, "crc": -847271024}, {"key": "androidx/test/espresso/NoMatchingRootException.class", "name": "androidx/test/espresso/NoMatchingRootException.class", "size": 1485, "crc": -781281094}, {"key": "androidx/test/espresso/NoActivityResumedException.class", "name": "androidx/test/espresso/NoActivityResumedException.class", "size": 649, "crc": 668476669}, {"key": "androidx/test/espresso/InteractionResultsHandler.class", "name": "androidx/test/espresso/InteractionResultsHandler.class", "size": 5872, "crc": -1960053803}, {"key": "androidx/test/espresso/InteractionResultsHandler$ExecutionResult.class", "name": "androidx/test/espresso/InteractionResultsHandler$ExecutionResult.class", "size": 3182, "crc": -1128985897}, {"key": "androidx/test/espresso/InteractionResultsHandler$1.class", "name": "androidx/test/espresso/InteractionResultsHandler$1.class", "size": 1407, "crc": -1952925383}, {"key": "androidx/test/espresso/InjectEventSecurityException.class", "name": "androidx/test/espresso/InjectEventSecurityException.class", "size": 1033, "crc": -1719050014}, {"key": "androidx/test/espresso/IdlingResourceTimeoutException.class", "name": "androidx/test/espresso/IdlingResourceTimeoutException.class", "size": 1127, "crc": 1679390176}, {"key": "androidx/test/espresso/IdlingPolicy.class", "name": "androidx/test/espresso/IdlingPolicy.class", "size": 4225, "crc": 1822358480}, {"key": "androidx/test/espresso/IdlingPolicy-IA.class", "name": "androidx/test/espresso/IdlingPolicy-IA.class", "size": 132, "crc": -942757796}, {"key": "androidx/test/espresso/IdlingPolicy$ResponseAction.class", "name": "androidx/test/espresso/IdlingPolicy$ResponseAction.class", "size": 1358, "crc": 554961247}, {"key": "androidx/test/espresso/IdlingPolicy$Builder.class", "name": "androidx/test/espresso/IdlingPolicy$Builder.class", "size": 3248, "crc": -604049097}, {"key": "androidx/test/espresso/IdlingPolicy$Builder-IA.class", "name": "androidx/test/espresso/IdlingPolicy$Builder-IA.class", "size": 140, "crc": -1950203126}, {"key": "androidx/test/espresso/IdlingPolicy$1.class", "name": "androidx/test/espresso/IdlingPolicy$1.class", "size": 867, "crc": 1654255493}, {"key": "androidx/test/espresso/IdlingPolicies.class", "name": "androidx/test/espresso/IdlingPolicies.class", "size": 2487, "crc": 2118160744}, {"key": "androidx/test/espresso/GraphHolder.class", "name": "androidx/test/espresso/GraphHolder.class", "size": 2671, "crc": 918426401}, {"key": "androidx/test/espresso/GraphHolder$$ExternalSyntheticBackportWithForwarding0.class", "name": "androidx/test/espresso/GraphHolder$$ExternalSyntheticBackportWithForwarding0.class", "size": 512, "crc": -780679818}, {"key": "androidx/test/espresso/FailureHandler.class", "name": "androidx/test/espresso/FailureHandler.class", "size": 288, "crc": -74102999}, {"key": "androidx/test/espresso/EspressoException.class", "name": "androidx/test/espresso/EspressoException.class", "size": 138, "crc": 1463714929}, {"key": "androidx/test/espresso/Espresso.class", "name": "androidx/test/espresso/Espresso.class", "size": 12097, "crc": 831060041}, {"key": "androidx/test/espresso/Espresso$TransitionBridgingViewAction.class", "name": "androidx/test/espresso/Espresso$TransitionBridgingViewAction.class", "size": 2059, "crc": -2108069466}, {"key": "androidx/test/espresso/Espresso$TransitionBridgingViewAction-IA.class", "name": "androidx/test/espresso/Espresso$TransitionBridgingViewAction-IA.class", "size": 157, "crc": 254835089}, {"key": "androidx/test/espresso/Espresso$2.class", "name": "androidx/test/espresso/Espresso$2.class", "size": 756, "crc": 1731206876}, {"key": "androidx/test/espresso/Espresso$1.class", "name": "androidx/test/espresso/Espresso$1.class", "size": 820, "crc": -2061119111}, {"key": "androidx/test/espresso/Espresso$$ExternalSyntheticLambda2.class", "name": "androidx/test/espresso/Espresso$$ExternalSyntheticLambda2.class", "size": 673, "crc": -1699112041}, {"key": "androidx/test/espresso/Espresso$$ExternalSyntheticLambda1.class", "name": "androidx/test/espresso/Espresso$$ExternalSyntheticLambda1.class", "size": 595, "crc": 808768505}, {"key": "androidx/test/espresso/Espresso$$ExternalSyntheticBackport0.class", "name": "androidx/test/espresso/Espresso$$ExternalSyntheticBackport0.class", "size": 629, "crc": 717548001}, {"key": "androidx/test/espresso/DataInteraction.class", "name": "androidx/test/espresso/DataInteraction.class", "size": 4588, "crc": -1452760901}, {"key": "androidx/test/espresso/DataInteraction$DisplayDataMatcher.class", "name": "androidx/test/espresso/DataInteraction$DisplayDataMatcher.class", "size": 6795, "crc": 1435998301}, {"key": "androidx/test/espresso/DataInteraction$DisplayDataMatcher$1.class", "name": "androidx/test/espresso/DataInteraction$DisplayDataMatcher$1.class", "size": 1969, "crc": 763354782}, {"key": "androidx/test/espresso/DaggerBaseLayerComponent.class", "name": "androidx/test/espresso/DaggerBaseLayerComponent.class", "size": 1003, "crc": -1088105954}, {"key": "androidx/test/espresso/DaggerBaseLayerComponent$ViewInteractionComponentImpl.class", "name": "androidx/test/espresso/DaggerBaseLayerComponent$ViewInteractionComponentImpl.class", "size": 7466, "crc": 1433326145}, {"key": "androidx/test/espresso/DaggerBaseLayerComponent$ViewInteractionComponentImpl-IA.class", "name": "androidx/test/espresso/DaggerBaseLayerComponent$ViewInteractionComponentImpl-IA.class", "size": 173, "crc": -1778244200}, {"key": "androidx/test/espresso/DaggerBaseLayerComponent$Builder.class", "name": "androidx/test/espresso/DaggerBaseLayerComponent$Builder.class", "size": 2350, "crc": -946769512}, {"key": "androidx/test/espresso/DaggerBaseLayerComponent$Builder-IA.class", "name": "androidx/test/espresso/DaggerBaseLayerComponent$Builder-IA.class", "size": 152, "crc": -1690928525}, {"key": "androidx/test/espresso/DaggerBaseLayerComponent$BaseLayerComponentImpl.class", "name": "androidx/test/espresso/DaggerBaseLayerComponent$BaseLayerComponentImpl.class", "size": 13003, "crc": 1950238963}, {"key": "androidx/test/espresso/DaggerBaseLayerComponent$BaseLayerComponentImpl-IA.class", "name": "androidx/test/espresso/DaggerBaseLayerComponent$BaseLayerComponentImpl-IA.class", "size": 167, "crc": 92627935}, {"key": "androidx/test/espresso/BaseLayerComponent.class", "name": "androidx/test/espresso/BaseLayerComponent.class", "size": 1194, "crc": -1128763803}, {"key": "androidx/test/espresso/AppNotIdleException.class", "name": "androidx/test/espresso/AppNotIdleException.class", "size": 2335, "crc": 806756739}, {"key": "androidx/test/espresso/AmbiguousViewMatcherException.class", "name": "androidx/test/espresso/AmbiguousViewMatcherException.class", "size": 5161, "crc": -96019268}, {"key": "androidx/test/espresso/AmbiguousViewMatcherException-IA.class", "name": "androidx/test/espresso/AmbiguousViewMatcherException-IA.class", "size": 149, "crc": -531672576}, {"key": "androidx/test/espresso/AmbiguousViewMatcherException$Builder.class", "name": "androidx/test/espresso/AmbiguousViewMatcherException$Builder.class", "size": 4275, "crc": 582657139}, {"key": "META-INF/androidx.test.espresso.screenshot.kotlin_module", "name": "META-INF/androidx.test.espresso.screenshot.kotlin_module", "size": 81, "crc": 270635956}]