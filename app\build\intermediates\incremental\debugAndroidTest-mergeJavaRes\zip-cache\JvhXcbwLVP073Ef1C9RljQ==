[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 264, "crc": -72173064}, {"key": "LICENSE.txt", "name": "LICENSE.txt", "size": 1509, "crc": -**********}, {"key": "org/hamcrest/EasyMock2Matchers.class", "name": "org/hamcrest/EasyMock2Matchers.class", "size": 662, "crc": **********}, {"key": "org/hamcrest/JMock1Matchers.class", "name": "org/hamcrest/JMock1Matchers.class", "size": 649, "crc": 431039910}, {"key": "org/hamcrest/JavaLangMatcherAssert.class", "name": "org/hamcrest/JavaLangMatcherAssert.class", "size": 747, "crc": -639634901}, {"key": "org/hamcrest/integration/EasyMock2Adapter.class", "name": "org/hamcrest/integration/EasyMock2Adapter.class", "size": 1446, "crc": 465720824}, {"key": "org/hamcrest/integration/JMock1Adapter.class", "name": "org/hamcrest/integration/JMock1Adapter.class", "size": 1305, "crc": -**********}]