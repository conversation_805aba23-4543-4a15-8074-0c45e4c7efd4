package com.falaileh.nisso.data.api;

/**
 * Network module for creating Retrofit instance and API service
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\t\u001a\u00020\nH\u0002J\b\u0010\u000b\u001a\u00020\fH\u0002R\u001b\u0010\u0003\u001a\u00020\u00048FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\r"}, d2 = {"Lcom/falaileh/nisso/data/api/NetworkModule;", "", "()V", "apiService", "Lcom/falaileh/nisso/data/api/LoveMessageApiService;", "getApiService", "()Lcom/falaileh/nisso/data/api/LoveMessageApiService;", "apiService$delegate", "Lkotlin/Lazy;", "createOkHttpClient", "Lokhttp3/OkHttpClient;", "createRetrofit", "Lretrofit2/Retrofit;", "app_release"})
public final class NetworkModule {
    @org.jetbrains.annotations.NotNull()
    private static final kotlin.Lazy apiService$delegate = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.falaileh.nisso.data.api.NetworkModule INSTANCE = null;
    
    private NetworkModule() {
        super();
    }
    
    private final okhttp3.OkHttpClient createOkHttpClient() {
        return null;
    }
    
    private final retrofit2.Retrofit createRetrofit() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.falaileh.nisso.data.api.LoveMessageApiService getApiService() {
        return null;
    }
}