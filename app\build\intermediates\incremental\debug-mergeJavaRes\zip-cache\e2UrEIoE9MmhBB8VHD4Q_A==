[{"key": "androidx/room/CoroutinesRoom$Companion$createFlow$1$1$1.class", "name": "androidx/room/CoroutinesRoom$Companion$createFlow$1$1$1.class", "size": 5756, "crc": 1404310028}, {"key": "androidx/room/CoroutinesRoom$Companion$createFlow$1$1$observer$1.class", "name": "androidx/room/CoroutinesRoom$Companion$createFlow$1$1$observer$1.class", "size": 1899, "crc": 914627472}, {"key": "androidx/room/CoroutinesRoom$Companion$createFlow$1$1.class", "name": "androidx/room/CoroutinesRoom$Companion$createFlow$1$1.class", "size": 6604, "crc": -280155105}, {"key": "androidx/room/CoroutinesRoom$Companion$createFlow$1.class", "name": "androidx/room/CoroutinesRoom$Companion$createFlow$1.class", "size": 4389, "crc": 465666857}, {"key": "androidx/room/CoroutinesRoom$Companion$execute$2.class", "name": "androidx/room/CoroutinesRoom$Companion$execute$2.class", "size": 3494, "crc": -488313945}, {"key": "androidx/room/CoroutinesRoom$Companion$execute$4$1.class", "name": "androidx/room/CoroutinesRoom$Companion$execute$4$1.class", "size": 2280, "crc": -236274811}, {"key": "androidx/room/CoroutinesRoom$Companion$execute$4$job$1.class", "name": "androidx/room/CoroutinesRoom$Companion$execute$4$job$1.class", "size": 4181, "crc": 2133073538}, {"key": "androidx/room/CoroutinesRoom$Companion.class", "name": "androidx/room/CoroutinesRoom$Companion.class", "size": 7704, "crc": 952469260}, {"key": "androidx/room/CoroutinesRoom.class", "name": "androidx/room/CoroutinesRoom.class", "size": 2790, "crc": -808948829}, {"key": "androidx/room/CoroutinesRoomKt.class", "name": "androidx/room/CoroutinesRoomKt.class", "size": 3253, "crc": -513238377}, {"key": "androidx/room/RoomDatabaseKt$invalidationTrackerFlow$1$1.class", "name": "androidx/room/RoomDatabaseKt$invalidationTrackerFlow$1$1.class", "size": 1424, "crc": -2054727999}, {"key": "androidx/room/RoomDatabaseKt$invalidationTrackerFlow$1$job$1.class", "name": "androidx/room/RoomDatabaseKt$invalidationTrackerFlow$1$job$1.class", "size": 5244, "crc": 913444507}, {"key": "androidx/room/RoomDatabaseKt$invalidationTrackerFlow$1$observer$1.class", "name": "androidx/room/RoomDatabaseKt$invalidationTrackerFlow$1$observer$1.class", "size": 2058, "crc": -1994132933}, {"key": "androidx/room/RoomDatabaseKt$invalidationTrackerFlow$1.class", "name": "androidx/room/RoomDatabaseKt$invalidationTrackerFlow$1.class", "size": 6212, "crc": -1489414821}, {"key": "androidx/room/RoomDatabaseKt$startTransactionCoroutine$2$1$1.class", "name": "androidx/room/RoomDatabaseKt$startTransactionCoroutine$2$1$1.class", "size": 5396, "crc": 79805338}, {"key": "androidx/room/RoomDatabaseKt$startTransactionCoroutine$2$1.class", "name": "androidx/room/RoomDatabaseKt$startTransactionCoroutine$2$1.class", "size": 2961, "crc": -1774895453}, {"key": "androidx/room/RoomDatabaseKt$withTransaction$transactionBlock$1.class", "name": "androidx/room/RoomDatabaseKt$withTransaction$transactionBlock$1.class", "size": 4927, "crc": 1189485613}, {"key": "androidx/room/RoomDatabaseKt.class", "name": "androidx/room/RoomDatabaseKt.class", "size": 8596, "crc": -1684231332}, {"key": "androidx/room/TransactionElement$Key.class", "name": "androidx/room/TransactionElement$Key.class", "size": 1091, "crc": 82124387}, {"key": "androidx/room/TransactionElement.class", "name": "androidx/room/TransactionElement.class", "size": 4565, "crc": -1912789940}, {"key": "androidx/room/migration/MigrationImpl.class", "name": "androidx/room/migration/MigrationImpl.class", "size": 1814, "crc": -589595372}, {"key": "androidx/room/migration/MigrationKt.class", "name": "androidx/room/migration/MigrationKt.class", "size": 1305, "crc": -1728261205}, {"key": "META-INF/androidx.room_room-ktx.version", "name": "META-INF/androidx.room_room-ktx.version", "size": 6, "crc": 1201321051}, {"key": "META-INF/room-ktx_release.kotlin_module", "name": "META-INF/room-ktx_release.kotlin_module", "size": 115, "crc": -254606838}]