1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.falaileh.nisso"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permission for API calls -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\Nisso\app\src\main\AndroidManifest.xml:6:5-67
12-->E:\Nisso\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\Nisso\app\src\main\AndroidManifest.xml:7:5-79
13-->E:\Nisso\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- TV-specific features -->
16    <uses-feature
16-->E:\Nisso\app\src\main\AndroidManifest.xml:10:5-12:36
17        android:name="android.hardware.touchscreen"
17-->E:\Nisso\app\src\main\AndroidManifest.xml:11:9-52
18        android:required="false" />
18-->E:\Nisso\app\src\main\AndroidManifest.xml:12:9-33
19    <uses-feature
19-->E:\Nisso\app\src\main\AndroidManifest.xml:13:5-15:35
20        android:name="android.software.leanback"
20-->E:\Nisso\app\src\main\AndroidManifest.xml:14:9-49
21        android:required="true" />
21-->E:\Nisso\app\src\main\AndroidManifest.xml:15:9-32
22    <uses-feature
22-->E:\Nisso\app\src\main\AndroidManifest.xml:16:5-18:35
23        android:name="android.hardware.wifi"
23-->E:\Nisso\app\src\main\AndroidManifest.xml:17:9-45
24        android:required="true" />
24-->E:\Nisso\app\src\main\AndroidManifest.xml:18:9-32
25
26    <permission
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
27        android:name="com.falaileh.nisso.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.falaileh.nisso.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
31
32    <application
32-->E:\Nisso\app\src\main\AndroidManifest.xml:20:5-39:19
33        android:allowBackup="true"
33-->E:\Nisso\app\src\main\AndroidManifest.xml:21:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\910260a50c4cc0fe03b922548d59c7fb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
35        android:banner="@mipmap/ic_launcher"
35-->E:\Nisso\app\src\main\AndroidManifest.xml:22:9-45
36        android:extractNativeLibs="true"
37        android:icon="@mipmap/ic_launcher"
37-->E:\Nisso\app\src\main\AndroidManifest.xml:23:9-43
38        android:label="@string/app_name"
38-->E:\Nisso\app\src\main\AndroidManifest.xml:24:9-41
39        android:supportsRtl="true"
39-->E:\Nisso\app\src\main\AndroidManifest.xml:25:9-35
40        android:theme="@style/Theme.Nisso" >
40-->E:\Nisso\app\src\main\AndroidManifest.xml:26:9-43
41        <activity
41-->E:\Nisso\app\src\main\AndroidManifest.xml:27:9-38:20
42            android:name="com.falaileh.nisso.MainActivity"
42-->E:\Nisso\app\src\main\AndroidManifest.xml:28:13-41
43            android:exported="true"
43-->E:\Nisso\app\src\main\AndroidManifest.xml:29:13-36
44            android:screenOrientation="landscape"
44-->E:\Nisso\app\src\main\AndroidManifest.xml:30:13-50
45            android:theme="@style/Theme.Nisso.Fullscreen" >
45-->E:\Nisso\app\src\main\AndroidManifest.xml:31:13-58
46            <intent-filter>
46-->E:\Nisso\app\src\main\AndroidManifest.xml:32:13-37:29
47                <action android:name="android.intent.action.MAIN" />
47-->E:\Nisso\app\src\main\AndroidManifest.xml:33:17-69
47-->E:\Nisso\app\src\main\AndroidManifest.xml:33:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->E:\Nisso\app\src\main\AndroidManifest.xml:35:17-77
49-->E:\Nisso\app\src\main\AndroidManifest.xml:35:27-74
50                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
50-->E:\Nisso\app\src\main\AndroidManifest.xml:36:17-86
50-->E:\Nisso\app\src\main\AndroidManifest.xml:36:27-83
51            </intent-filter>
52        </activity>
53
54        <provider
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
56            android:authorities="com.falaileh.nisso.androidx-startup"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f9c802d0c3ba221842c25562b4b1fb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2c80a62fc08692b7b62b79d3d158127\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <service
69-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
70            android:name="androidx.room.MultiInstanceInvalidationService"
70-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
71            android:directBootAware="true"
71-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
72            android:exported="false" />
72-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cfd752e961e50e01c7e09007866b85f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
73
74        <receiver
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
75            android:name="androidx.profileinstaller.ProfileInstallReceiver"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
76            android:directBootAware="false"
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
77            android:enabled="true"
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
78            android:exported="true"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
79            android:permission="android.permission.DUMP" >
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
81                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
84                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
87                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
90                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\79f79c5e995193cda1964b29776b3cb6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
91            </intent-filter>
92        </receiver>
93    </application>
94
95</manifest>
