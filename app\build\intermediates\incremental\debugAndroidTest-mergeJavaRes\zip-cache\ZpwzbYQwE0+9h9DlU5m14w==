[{"key": "androidx/compose/ui/test/AbstractMainTestClock$advanceDispatcher$1.class", "name": "androidx/compose/ui/test/AbstractMainTestClock$advanceDispatcher$1.class", "size": 1564, "crc": -1276121391}, {"key": "androidx/compose/ui/test/AbstractMainTestClock$advanceTimeUntil$1.class", "name": "androidx/compose/ui/test/AbstractMainTestClock$advanceTimeUntil$1.class", "size": 2423, "crc": 1045337086}, {"key": "androidx/compose/ui/test/AbstractMainTestClock.class", "name": "androidx/compose/ui/test/AbstractMainTestClock.class", "size": 4226, "crc": -944249231}, {"key": "androidx/compose/ui/test/ActionsKt$performCustomAccessibilityActionWithLabel$1.class", "name": "androidx/compose/ui/test/ActionsKt$performCustomAccessibilityActionWithLabel$1.class", "size": 1743, "crc": -15561855}, {"key": "androidx/compose/ui/test/ActionsKt$performScrollToKey$1.class", "name": "androidx/compose/ui/test/ActionsKt$performScrollToKey$1.class", "size": 1591, "crc": 873597196}, {"key": "androidx/compose/ui/test/ActionsKt$performScrollToKey$3.class", "name": "androidx/compose/ui/test/ActionsKt$performScrollToKey$3.class", "size": 2287, "crc": 2108422315}, {"key": "androidx/compose/ui/test/ActionsKt$performScrollToNode$1.class", "name": "androidx/compose/ui/test/ActionsKt$performScrollToNode$1.class", "size": 2326, "crc": 709790691}, {"key": "androidx/compose/ui/test/ActionsKt$performSemanticsAction$1.class", "name": "androidx/compose/ui/test/ActionsKt$performSemanticsAction$1.class", "size": 1999, "crc": 476043095}, {"key": "androidx/compose/ui/test/ActionsKt$performSemanticsAction$2.class", "name": "androidx/compose/ui/test/ActionsKt$performSemanticsAction$2.class", "size": 2758, "crc": 1408435929}, {"key": "androidx/compose/ui/test/ActionsKt$performSemanticsAction$3.class", "name": "androidx/compose/ui/test/ActionsKt$performSemanticsAction$3.class", "size": 1762, "crc": 1495903672}, {"key": "androidx/compose/ui/test/ActionsKt$requireSemantics$msg$1.class", "name": "androidx/compose/ui/test/ActionsKt$requireSemantics$msg$1.class", "size": 1904, "crc": 296155718}, {"key": "androidx/compose/ui/test/ActionsKt$scrollToIndex$1.class", "name": "androidx/compose/ui/test/ActionsKt$scrollToIndex$1.class", "size": 1434, "crc": 1351329232}, {"key": "androidx/compose/ui/test/ActionsKt$scrollToIndex$2.class", "name": "androidx/compose/ui/test/ActionsKt$scrollToIndex$2.class", "size": 2276, "crc": 195314892}, {"key": "androidx/compose/ui/test/ActionsKt$scrollToNode$1.class", "name": "androidx/compose/ui/test/ActionsKt$scrollToNode$1.class", "size": 2464, "crc": -533629542}, {"key": "androidx/compose/ui/test/ActionsKt$scrollToNode$scrollableNode$1.class", "name": "androidx/compose/ui/test/ActionsKt$scrollToNode$scrollableNode$1.class", "size": 1917, "crc": -844727589}, {"key": "androidx/compose/ui/test/ActionsKt.class", "name": "androidx/compose/ui/test/ActionsKt.class", "size": 30323, "crc": -2095202633}, {"key": "androidx/compose/ui/test/Actions_androidKt$performClickImpl$1.class", "name": "androidx/compose/ui/test/Actions_androidKt$performClickImpl$1.class", "size": 1797, "crc": -1605426476}, {"key": "androidx/compose/ui/test/Actions_androidKt.class", "name": "androidx/compose/ui/test/Actions_androidKt.class", "size": 1225, "crc": -742399764}, {"key": "androidx/compose/ui/test/AndroidAssertions_androidKt$checkIsDisplayed$1.class", "name": "androidx/compose/ui/test/AndroidAssertions_androidKt$checkIsDisplayed$1.class", "size": 1947, "crc": -2113335030}, {"key": "androidx/compose/ui/test/AndroidAssertions_androidKt.class", "name": "androidx/compose/ui/test/AndroidAssertions_androidKt.class", "size": 6750, "crc": 1063249709}, {"key": "androidx/compose/ui/test/AndroidComposeUiTest.class", "name": "androidx/compose/ui/test/AndroidComposeUiTest.class", "size": 1060, "crc": 1703008067}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$1.class", "size": 3318, "crc": 80389774}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$2.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$2.class", "size": 4542, "crc": -324238053}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$awaitIdle$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$awaitIdle$1.class", "size": 2245, "crc": -181468991}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$density$2.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$density$2.class", "size": 1829, "crc": 1785303174}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$setContent$3$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$setContent$3$1.class", "size": 2910, "crc": -1874060753}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$setContent$3.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$setContent$3.class", "size": 3576, "crc": 307468434}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$withDisposableContent$1$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl$withDisposableContent$1$1.class", "size": 1759, "crc": 83367302}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidComposeUiTestImpl.class", "size": 12498, "crc": -169129530}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidTestOwner.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$AndroidTestOwner.class", "size": 2661, "crc": 1406871197}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1$1$1$1$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1$1$1$1$1.class", "size": 2443, "crc": 779402891}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1$1$1$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1$1$1$1.class", "size": 2272, "crc": 304094700}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1$1$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1$1$1.class", "size": 2175, "crc": -472240134}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1$1.class", "size": 2083, "crc": 1206723839}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1$1.class", "size": 2117, "crc": -1613114499}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$runTest$1.class", "size": 2118, "crc": -644022281}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$withTestCoroutines$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$withTestCoroutines$1.class", "size": 3337, "crc": -1933679728}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$withWindowRecomposer$2$1.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment$withWindowRecomposer$2$1.class", "size": 4145, "crc": -280418435}, {"key": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment.class", "name": "androidx/compose/ui/test/AndroidComposeUiTestEnvironment.class", "size": 18881, "crc": -206208407}, {"key": "androidx/compose/ui/test/AndroidImageHelpers_androidKt$captureToImage$dialogParentNodeMaybe$1.class", "name": "androidx/compose/ui/test/AndroidImageHelpers_androidKt$captureToImage$dialogParentNodeMaybe$1.class", "size": 2218, "crc": 840564522}, {"key": "androidx/compose/ui/test/AndroidImageHelpers_androidKt$captureToImage$popupParentMaybe$1.class", "name": "androidx/compose/ui/test/AndroidImageHelpers_androidKt$captureToImage$popupParentMaybe$1.class", "size": 2207, "crc": 917414205}, {"key": "androidx/compose/ui/test/AndroidImageHelpers_androidKt.class", "name": "androidx/compose/ui/test/AndroidImageHelpers_androidKt.class", "size": 7787, "crc": 1146039257}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueKeyEvent$1$1.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueKeyEvent$1$1.class", "size": 1967, "crc": 1307508513}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueMouseEvent$2$1.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueMouseEvent$2$1.class", "size": 2380, "crc": -1748763526}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueMoves$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueMoves$$inlined$sortedBy$1.class", "size": 2403, "crc": -1256565230}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueRotaryScrollEvent$1$1.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueRotaryScrollEvent$1$1.class", "size": 1657, "crc": 728037375}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueTouchEvent$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueTouchEvent$$inlined$sortedBy$1.class", "size": 2393, "crc": -1619661257}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueTouchEvent$5$1.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$enqueueTouchEvent$5$1.class", "size": 2464, "crc": 1380977954}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$flush$1$events$1$1.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$flush$1$events$1$1.class", "size": 1244, "crc": -766538728}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$flush$1.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$flush$1.class", "size": 4186, "crc": 166160248}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$horizontalScrollFactor$2.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$horizontalScrollFactor$2.class", "size": 2179, "crc": -861580170}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher$verticalScrollFactor$2.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher$verticalScrollFactor$2.class", "size": 2173, "crc": -278603057}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher.class", "size": 29293, "crc": 410733279}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher_androidKt$createInputDispatcher$2.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher_androidKt$createInputDispatcher$2.class", "size": 2711, "crc": -1308668071}, {"key": "androidx/compose/ui/test/AndroidInputDispatcher_androidKt.class", "name": "androidx/compose/ui/test/AndroidInputDispatcher_androidKt.class", "size": 2662, "crc": -466422290}, {"key": "androidx/compose/ui/test/AndroidOutput_androidKt.class", "name": "androidx/compose/ui/test/AndroidOutput_androidKt.class", "size": 778, "crc": 1225656127}, {"key": "androidx/compose/ui/test/AndroidSynchronization_androidKt.class", "name": "androidx/compose/ui/test/AndroidSynchronization_androidKt.class", "size": 2551, "crc": 818916415}, {"key": "androidx/compose/ui/test/ApplyingContinuationInterceptor$Key$1.class", "name": "androidx/compose/ui/test/ApplyingContinuationInterceptor$Key$1.class", "size": 1889, "crc": 1820149732}, {"key": "androidx/compose/ui/test/ApplyingContinuationInterceptor$Key.class", "name": "androidx/compose/ui/test/ApplyingContinuationInterceptor$Key.class", "size": 1824, "crc": 1687830772}, {"key": "androidx/compose/ui/test/ApplyingContinuationInterceptor$SendApplyContinuation.class", "name": "androidx/compose/ui/test/ApplyingContinuationInterceptor$SendApplyContinuation.class", "size": 2032, "crc": 1275267115}, {"key": "androidx/compose/ui/test/ApplyingContinuationInterceptor.class", "name": "androidx/compose/ui/test/ApplyingContinuationInterceptor.class", "size": 2559, "crc": -554946791}, {"key": "androidx/compose/ui/test/AssertionsKt.class", "name": "androidx/compose/ui/test/AssertionsKt.class", "size": 13411, "crc": -2008408746}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$assertHeightIsAtLeast$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$assertHeightIsAtLeast$1.class", "size": 2908, "crc": 1420878702}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$assertHeightIsEqualTo$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$assertHeightIsEqualTo$1.class", "size": 2901, "crc": -1575493309}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$assertLeftPositionInRootIsEqualTo$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$assertLeftPositionInRootIsEqualTo$1.class", "size": 1761, "crc": -608152036}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$assertPositionInRootIsEqualTo$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$assertPositionInRootIsEqualTo$1.class", "size": 1855, "crc": 740998631}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$assertTopPositionInRootIsEqualTo$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$assertTopPositionInRootIsEqualTo$1.class", "size": 1755, "crc": -237084230}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$assertTouchHeightIsEqualTo$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$assertTouchHeightIsEqualTo$1.class", "size": 2936, "crc": 1902705189}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$assertTouchWidthIsEqualTo$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$assertTouchWidthIsEqualTo$1.class", "size": 2925, "crc": 760238299}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$assertWidthIsAtLeast$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$assertWidthIsAtLeast$1.class", "size": 2893, "crc": 32038541}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$assertWidthIsEqualTo$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$assertWidthIsEqualTo$1.class", "size": 2890, "crc": -1212309546}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$getAlignmentLinePosition$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$getAlignmentLinePosition$1.class", "size": 2372, "crc": -587086326}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt$getUnclippedBoundsInRoot$1.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt$getUnclippedBoundsInRoot$1.class", "size": 1900, "crc": -999331921}, {"key": "androidx/compose/ui/test/BoundsAssertionsKt.class", "name": "androidx/compose/ui/test/BoundsAssertionsKt.class", "size": 13297, "crc": -778960432}, {"key": "androidx/compose/ui/test/ComposeIdlingResource.class", "name": "androidx/compose/ui/test/ComposeIdlingResource.class", "size": 7003, "crc": -219905049}, {"key": "androidx/compose/ui/test/ComposeIdlingResource_androidKt.class", "name": "androidx/compose/ui/test/ComposeIdlingResource_androidKt.class", "size": 2305, "crc": 855716691}, {"key": "androidx/compose/ui/test/ComposeRootRegistry$OnRegistrationChangedListener.class", "name": "androidx/compose/ui/test/ComposeRootRegistry$OnRegistrationChangedListener.class", "size": 911, "crc": 2051087320}, {"key": "androidx/compose/ui/test/ComposeRootRegistry$StateChangeHandler$onViewAttachedToWindow$1.class", "name": "androidx/compose/ui/test/ComposeRootRegistry$StateChangeHandler$onViewAttachedToWindow$1.class", "size": 1717, "crc": -211586721}, {"key": "androidx/compose/ui/test/ComposeRootRegistry$StateChangeHandler.class", "name": "androidx/compose/ui/test/ComposeRootRegistry$StateChangeHandler.class", "size": 5684, "crc": 849644883}, {"key": "androidx/compose/ui/test/ComposeRootRegistry$isSetUp$1.class", "name": "androidx/compose/ui/test/ComposeRootRegistry$isSetUp$1.class", "size": 1730, "crc": -1620174390}, {"key": "androidx/compose/ui/test/ComposeRootRegistry$setupRegistry$1.class", "name": "androidx/compose/ui/test/ComposeRootRegistry$setupRegistry$1.class", "size": 1748, "crc": -1755032685}, {"key": "androidx/compose/ui/test/ComposeRootRegistry.class", "name": "androidx/compose/ui/test/ComposeRootRegistry.class", "size": 9515, "crc": -1480844725}, {"key": "androidx/compose/ui/test/ComposeRootRegistry_androidKt$awaitComposeRoots$2$1.class", "name": "androidx/compose/ui/test/ComposeRootRegistry_androidKt$awaitComposeRoots$2$1.class", "size": 2298, "crc": -257195843}, {"key": "androidx/compose/ui/test/ComposeRootRegistry_androidKt$awaitComposeRoots$2$listener$1.class", "name": "androidx/compose/ui/test/ComposeRootRegistry_androidKt$awaitComposeRoots$2$listener$1.class", "size": 2623, "crc": 450737057}, {"key": "androidx/compose/ui/test/ComposeRootRegistry_androidKt$waitForComposeRoots$listener$1.class", "name": "androidx/compose/ui/test/ComposeRootRegistry_androidKt$waitForComposeRoots$listener$1.class", "size": 1961, "crc": -1102836061}, {"key": "androidx/compose/ui/test/ComposeRootRegistry_androidKt.class", "name": "androidx/compose/ui/test/ComposeRootRegistry_androidKt.class", "size": 7748, "crc": 1812988254}, {"key": "androidx/compose/ui/test/ComposeTimeoutException.class", "name": "androidx/compose/ui/test/ComposeTimeoutException.class", "size": 904, "crc": 1279587986}, {"key": "androidx/compose/ui/test/ComposeUiTest.class", "name": "androidx/compose/ui/test/ComposeUiTest.class", "size": 3111, "crc": 1743557004}, {"key": "androidx/compose/ui/test/ComposeUiTestKt$waitUntilAtLeastOneExists$1.class", "name": "androidx/compose/ui/test/ComposeUiTestKt$waitUntilAtLeastOneExists$1.class", "size": 2219, "crc": 720822708}, {"key": "androidx/compose/ui/test/ComposeUiTestKt$waitUntilNodeCount$1.class", "name": "androidx/compose/ui/test/ComposeUiTestKt$waitUntilNodeCount$1.class", "size": 2227, "crc": 156617682}, {"key": "androidx/compose/ui/test/ComposeUiTestKt.class", "name": "androidx/compose/ui/test/ComposeUiTestKt.class", "size": 4448, "crc": 162236078}, {"key": "androidx/compose/ui/test/ComposeUiTest_androidKt$AndroidComposeUiTestEnvironment$1.class", "name": "androidx/compose/ui/test/ComposeUiTest_androidKt$AndroidComposeUiTestEnvironment$1.class", "size": 2337, "crc": 128870835}, {"key": "androidx/compose/ui/test/ComposeUiTest_androidKt$runAndroidComposeUiTest$$inlined$AndroidComposeUiTestEnvironment$1.class", "name": "androidx/compose/ui/test/ComposeUiTest_androidKt$runAndroidComposeUiTest$$inlined$AndroidComposeUiTestEnvironment$1.class", "size": 3433, "crc": -386926845}, {"key": "androidx/compose/ui/test/ComposeUiTest_androidKt$runAndroidComposeUiTest$1.class", "name": "androidx/compose/ui/test/ComposeUiTest_androidKt$runAndroidComposeUiTest$1.class", "size": 2768, "crc": 1471392550}, {"key": "androidx/compose/ui/test/ComposeUiTest_androidKt$runEmptyComposeUiTest$$inlined$AndroidComposeUiTestEnvironment$default$1.class", "name": "androidx/compose/ui/test/ComposeUiTest_androidKt$runEmptyComposeUiTest$$inlined$AndroidComposeUiTestEnvironment$default$1.class", "size": 2829, "crc": 1559895030}, {"key": "androidx/compose/ui/test/ComposeUiTest_androidKt.class", "name": "androidx/compose/ui/test/ComposeUiTest_androidKt.class", "size": 8233, "crc": -1930898105}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$3.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$3.class", "size": 3427, "crc": 2048246555}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$measurables$1$1$1$1.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$measurables$1$1$1$1.class", "size": 3846, "crc": -1173277347}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$measurables$1$1$1.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$measurables$1$1$1.class", "size": 4469, "crc": -1457600343}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$measurables$1$1.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$measurables$1$1.class", "size": 8438, "crc": -611924547}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$measurables$1.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1$measurables$1.class", "size": 7377, "crc": 352741234}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$1$1.class", "size": 6115, "crc": 780470375}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$2.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt$DensityForcedSize$2.class", "size": 2259, "crc": -816900212}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt$size$1$1.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt$size$1$1.class", "size": 1953, "crc": -76605700}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt$size$1.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt$size$1.class", "size": 3174, "crc": -390700441}, {"key": "androidx/compose/ui/test/DensityForcedSizeKt.class", "name": "androidx/compose/ui/test/DensityForcedSizeKt.class", "size": 6039, "crc": 125221399}, {"key": "androidx/compose/ui/test/DensityForcedSize_androidKt.class", "name": "androidx/compose/ui/test/DensityForcedSize_androidKt.class", "size": 581, "crc": 742370177}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride$Companion.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride$Companion.class", "size": 777, "crc": 953421063}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride.class", "size": 1326, "crc": -46797329}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$DeviceConfigurationOverride$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$DeviceConfigurationOverride$1.class", "size": 2310, "crc": -1902448320}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$then$1$Override$1$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$then$1$Override$1$1.class", "size": 2744, "crc": -1591720395}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$then$1$Override$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$then$1$Override$1.class", "size": 3308, "crc": -1100410761}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$then$1$Override$2.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$then$1$Override$2.class", "size": 2265, "crc": -609680857}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$then$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverrideKt$then$1.class", "size": 3805, "crc": -802967892}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverrideKt.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverrideKt.class", "size": 3635, "crc": -258816312}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$DarkMode$1$Override$2.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$DarkMode$1$Override$2.class", "size": 2345, "crc": -922374998}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$DarkMode$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$DarkMode$1.class", "size": 5661, "crc": 1237041489}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$FontScale$1$Override$2.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$FontScale$1$Override$2.class", "size": 2351, "crc": 1541603105}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$FontScale$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$FontScale$1.class", "size": 5544, "crc": 1646606074}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$FontWeightAdjustment$1$Override$2.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$FontWeightAdjustment$1$Override$2.class", "size": 2417, "crc": 1759124964}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$FontWeightAdjustment$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$FontWeightAdjustment$1.class", "size": 5681, "crc": 1079099110}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$ForcedSize$1$Override$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$ForcedSize$1$Override$1.class", "size": 5537, "crc": -403976829}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$ForcedSize$1$Override$2.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$ForcedSize$1$Override$2.class", "size": 2357, "crc": -1278812629}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$ForcedSize$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$ForcedSize$1.class", "size": 3906, "crc": 1580278434}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$LayoutDirection$1$Override$2.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$LayoutDirection$1$Override$2.class", "size": 2387, "crc": -1636030828}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$LayoutDirection$1$WhenMappings.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$LayoutDirection$1$WhenMappings.class", "size": 916, "crc": 1558991579}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$LayoutDirection$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$LayoutDirection$1.class", "size": 6276, "crc": -699796729}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$Locales$1$Override$1$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$Locales$1$Override$1$1.class", "size": 1753, "crc": 1280188675}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$Locales$1$Override$2.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$Locales$1$Override$2.class", "size": 2339, "crc": 2109626017}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$Locales$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$Locales$1.class", "size": 6587, "crc": 1912219823}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$OverriddenConfiguration$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$OverriddenConfiguration$1.class", "size": 2297, "crc": 1618993075}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$RoundScreen$1$Override$2.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$RoundScreen$1$Override$2.class", "size": 2363, "crc": 1238646866}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$RoundScreen$1.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt$RoundScreen$1.class", "size": 5797, "crc": -1923622398}, {"key": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt.class", "name": "androidx/compose/ui/test/DeviceConfigurationOverride_androidKt.class", "size": 9988, "crc": 313883160}, {"key": "androidx/compose/ui/test/ErrorMessagesKt.class", "name": "androidx/compose/ui/test/ErrorMessagesKt.class", "size": 7714, "crc": 475037122}, {"key": "androidx/compose/ui/test/EspressoLink$awaitIdle$2.class", "name": "androidx/compose/ui/test/EspressoLink$awaitIdle$2.class", "size": 3334, "crc": -1133217542}, {"key": "androidx/compose/ui/test/EspressoLink$registerIdleTransitionCallback$1.class", "name": "androidx/compose/ui/test/EspressoLink$registerIdleTransitionCallback$1.class", "size": 1462, "crc": -1686842081}, {"key": "androidx/compose/ui/test/EspressoLink.class", "name": "androidx/compose/ui/test/EspressoLink.class", "size": 4983, "crc": -118119506}, {"key": "androidx/compose/ui/test/EspressoLink_androidKt.class", "name": "androidx/compose/ui/test/EspressoLink_androidKt.class", "size": 5005, "crc": 2026378887}, {"key": "androidx/compose/ui/test/Expect_jvmKt.class", "name": "androidx/compose/ui/test/Expect_jvmKt.class", "size": 663, "crc": -1327443738}, {"key": "androidx/compose/ui/test/ExperimentalTestApi.class", "name": "androidx/compose/ui/test/ExperimentalTestApi.class", "size": 798, "crc": -1332479354}, {"key": "androidx/compose/ui/test/FiltersKt$ancestors$1$iterator$1.class", "name": "androidx/compose/ui/test/FiltersKt$ancestors$1$iterator$1.class", "size": 2790, "crc": -513497744}, {"key": "androidx/compose/ui/test/FiltersKt$ancestors$1.class", "name": "androidx/compose/ui/test/FiltersKt$ancestors$1.class", "size": 1519, "crc": -305717525}, {"key": "androidx/compose/ui/test/FiltersKt$hasAnyAncestor$1.class", "name": "androidx/compose/ui/test/FiltersKt$hasAnyAncestor$1.class", "size": 1904, "crc": 410749194}, {"key": "androidx/compose/ui/test/FiltersKt$hasAnyChild$1.class", "name": "androidx/compose/ui/test/FiltersKt$hasAnyChild$1.class", "size": 1872, "crc": 1832409599}, {"key": "androidx/compose/ui/test/FiltersKt$hasAnyDescendant$1.class", "name": "androidx/compose/ui/test/FiltersKt$hasAnyDescendant$1.class", "size": 1868, "crc": -692789099}, {"key": "androidx/compose/ui/test/FiltersKt$hasAnySibling$1.class", "name": "androidx/compose/ui/test/FiltersKt$hasAnySibling$1.class", "size": 3789, "crc": -1710335403}, {"key": "androidx/compose/ui/test/FiltersKt$hasContentDescription$1.class", "name": "androidx/compose/ui/test/FiltersKt$hasContentDescription$1.class", "size": 3772, "crc": -1158559351}, {"key": "androidx/compose/ui/test/FiltersKt$hasContentDescription$2.class", "name": "androidx/compose/ui/test/FiltersKt$hasContentDescription$2.class", "size": 3724, "crc": 2123442292}, {"key": "androidx/compose/ui/test/FiltersKt$hasContentDescriptionExactly$1.class", "name": "androidx/compose/ui/test/FiltersKt$hasContentDescriptionExactly$1.class", "size": 2810, "crc": 1825623602}, {"key": "androidx/compose/ui/test/FiltersKt$hasParent$1.class", "name": "androidx/compose/ui/test/FiltersKt$hasParent$1.class", "size": 2472, "crc": -326884053}, {"key": "androidx/compose/ui/test/FiltersKt$hasText$1.class", "name": "androidx/compose/ui/test/FiltersKt$hasText$1.class", "size": 4025, "crc": -1382424559}, {"key": "androidx/compose/ui/test/FiltersKt$hasText$2.class", "name": "androidx/compose/ui/test/FiltersKt$hasText$2.class", "size": 3971, "crc": 1092650611}, {"key": "androidx/compose/ui/test/FiltersKt$hasTextExactly$1.class", "name": "androidx/compose/ui/test/FiltersKt$hasTextExactly$1.class", "size": 4632, "crc": -515991874}, {"key": "androidx/compose/ui/test/FiltersKt$isEnabled$1.class", "name": "androidx/compose/ui/test/FiltersKt$isEnabled$1.class", "size": 2064, "crc": -507918213}, {"key": "androidx/compose/ui/test/FiltersKt$isNotEnabled$1.class", "name": "androidx/compose/ui/test/FiltersKt$isNotEnabled$1.class", "size": 2038, "crc": 1022638481}, {"key": "androidx/compose/ui/test/FiltersKt$isRoot$1.class", "name": "androidx/compose/ui/test/FiltersKt$isRoot$1.class", "size": 1609, "crc": -2103524246}, {"key": "androidx/compose/ui/test/FiltersKt.class", "name": "androidx/compose/ui/test/FiltersKt.class", "size": 14348, "crc": 1014149645}, {"key": "androidx/compose/ui/test/FindersKt.class", "name": "androidx/compose/ui/test/FindersKt.class", "size": 5418, "crc": -107312192}, {"key": "androidx/compose/ui/test/FrameDeferringContinuationInterceptor$FrameDeferredContinuation.class", "name": "androidx/compose/ui/test/FrameDeferringContinuationInterceptor$FrameDeferredContinuation.class", "size": 3033, "crc": 1989725685}, {"key": "androidx/compose/ui/test/FrameDeferringContinuationInterceptor$TrampolinedTask.class", "name": "androidx/compose/ui/test/FrameDeferringContinuationInterceptor$TrampolinedTask.class", "size": 2135, "crc": -1793247242}, {"key": "androidx/compose/ui/test/FrameDeferringContinuationInterceptor.class", "name": "androidx/compose/ui/test/FrameDeferringContinuationInterceptor.class", "size": 8865, "crc": -2122085489}, {"key": "androidx/compose/ui/test/GestureScope.class", "name": "androidx/compose/ui/test/GestureScope.class", "size": 2268, "crc": -1020482390}, {"key": "androidx/compose/ui/test/GestureScopeKt$advanceEventTime$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$advanceEventTime$1.class", "size": 1543, "crc": 804363116}, {"key": "androidx/compose/ui/test/GestureScopeKt$cancel$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$cancel$1.class", "size": 1618, "crc": 1162652941}, {"key": "androidx/compose/ui/test/GestureScopeKt$click$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$click$1.class", "size": 1615, "crc": 1164269074}, {"key": "androidx/compose/ui/test/GestureScopeKt$doubleClick$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$doubleClick$1.class", "size": 1688, "crc": 766500177}, {"key": "androidx/compose/ui/test/GestureScopeKt$down$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$down$1.class", "size": 1580, "crc": -442731987}, {"key": "androidx/compose/ui/test/GestureScopeKt$down$2.class", "name": "androidx/compose/ui/test/GestureScopeKt$down$2.class", "size": 1524, "crc": 2074039124}, {"key": "androidx/compose/ui/test/GestureScopeKt$longClick$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$longClick$1.class", "size": 1685, "crc": -316754492}, {"key": "androidx/compose/ui/test/GestureScopeKt$move$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$move$1.class", "size": 1610, "crc": -195375441}, {"key": "androidx/compose/ui/test/GestureScopeKt$moveBy$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$moveBy$1.class", "size": 1668, "crc": 344943828}, {"key": "androidx/compose/ui/test/GestureScopeKt$moveBy$2.class", "name": "androidx/compose/ui/test/GestureScopeKt$moveBy$2.class", "size": 1611, "crc": 909070910}, {"key": "androidx/compose/ui/test/GestureScopeKt$movePointerBy$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$movePointerBy$1.class", "size": 1615, "crc": 1775598218}, {"key": "androidx/compose/ui/test/GestureScopeKt$movePointerTo$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$movePointerTo$1.class", "size": 1618, "crc": -1073006558}, {"key": "androidx/compose/ui/test/GestureScopeKt$moveTo$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$moveTo$1.class", "size": 1671, "crc": -1155193901}, {"key": "androidx/compose/ui/test/GestureScopeKt$moveTo$2.class", "name": "androidx/compose/ui/test/GestureScopeKt$moveTo$2.class", "size": 1614, "crc": -1171058904}, {"key": "androidx/compose/ui/test/GestureScopeKt$pinch$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$pinch$1.class", "size": 1820, "crc": 843066303}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipe$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipe$1.class", "size": 1718, "crc": 1294824339}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipeDown$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipeDown$1.class", "size": 1687, "crc": 1870697161}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipeDown$2.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipeDown$2.class", "size": 1727, "crc": 1277694115}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipeLeft$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipeLeft$1.class", "size": 1687, "crc": -51344547}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipeLeft$2.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipeLeft$2.class", "size": 1727, "crc": 466568025}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipeRight$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipeRight$1.class", "size": 1691, "crc": 1592590080}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipeRight$2.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipeRight$2.class", "size": 1730, "crc": 1064810793}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipeUp$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipeUp$1.class", "size": 1679, "crc": -2065504159}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipeUp$2.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipeUp$2.class", "size": 1721, "crc": 1689773236}, {"key": "androidx/compose/ui/test/GestureScopeKt$swipeWithVelocity$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$swipeWithVelocity$1.class", "size": 1814, "crc": -1010786473}, {"key": "androidx/compose/ui/test/GestureScopeKt$up$1.class", "name": "androidx/compose/ui/test/GestureScopeKt$up$1.class", "size": 1489, "crc": -1470520973}, {"key": "androidx/compose/ui/test/GestureScopeKt.class", "name": "androidx/compose/ui/test/GestureScopeKt.class", "size": 20017, "crc": 188381804}, {"key": "androidx/compose/ui/test/GlobalAssertions.class", "name": "androidx/compose/ui/test/GlobalAssertions.class", "size": 2269, "crc": 678668887}, {"key": "androidx/compose/ui/test/GlobalAssertionsCollection.class", "name": "androidx/compose/ui/test/GlobalAssertionsCollection.class", "size": 3741, "crc": -1249601585}, {"key": "androidx/compose/ui/test/IdlingResource$DefaultImpls.class", "name": "androidx/compose/ui/test/IdlingResource$DefaultImpls.class", "size": 887, "crc": 731139206}, {"key": "androidx/compose/ui/test/IdlingResource.class", "name": "androidx/compose/ui/test/IdlingResource.class", "size": 1017, "crc": 881855814}, {"key": "androidx/compose/ui/test/IdlingResourceRegistry$getDiagnosticMessageIfBusy$2.class", "name": "androidx/compose/ui/test/IdlingResourceRegistry$getDiagnosticMessageIfBusy$2.class", "size": 1934, "crc": 1499848754}, {"key": "androidx/compose/ui/test/IdlingResourceRegistry$getDiagnosticMessageIfBusy$3.class", "name": "androidx/compose/ui/test/IdlingResourceRegistry$getDiagnosticMessageIfBusy$3.class", "size": 1845, "crc": -1086270181}, {"key": "androidx/compose/ui/test/IdlingResourceRegistry$isIdleOrEnsurePolling$1$1$1.class", "name": "androidx/compose/ui/test/IdlingResourceRegistry$isIdleOrEnsurePolling$1$1$1.class", "size": 3918, "crc": -1303321196}, {"key": "androidx/compose/ui/test/IdlingResourceRegistry.class", "name": "androidx/compose/ui/test/IdlingResourceRegistry.class", "size": 11976, "crc": -1355340073}, {"key": "androidx/compose/ui/test/IdlingStrategy.class", "name": "androidx/compose/ui/test/IdlingStrategy.class", "size": 1922, "crc": 97731838}, {"key": "androidx/compose/ui/test/ImpulseVelocityPathFinder$FittingResult.class", "name": "androidx/compose/ui/test/ImpulseVelocityPathFinder$FittingResult.class", "size": 2694, "crc": -1611656458}, {"key": "androidx/compose/ui/test/ImpulseVelocityPathFinder.class", "name": "androidx/compose/ui/test/ImpulseVelocityPathFinder.class", "size": 6918, "crc": 1841236908}, {"key": "androidx/compose/ui/test/InjectionScope$DefaultImpls.class", "name": "androidx/compose/ui/test/InjectionScope$DefaultImpls.class", "size": 6732, "crc": -1689783344}, {"key": "androidx/compose/ui/test/InjectionScope.class", "name": "androidx/compose/ui/test/InjectionScope.class", "size": 9847, "crc": -1821006720}, {"key": "androidx/compose/ui/test/InputDispatcher$Companion.class", "name": "androidx/compose/ui/test/InputDispatcher$Companion.class", "size": 1317, "crc": 2119874727}, {"key": "androidx/compose/ui/test/InputDispatcher.class", "name": "androidx/compose/ui/test/InputDispatcher.class", "size": 22034, "crc": 1686492163}, {"key": "androidx/compose/ui/test/InputDispatcherState.class", "name": "androidx/compose/ui/test/InputDispatcherState.class", "size": 4037, "crc": -1413159440}, {"key": "androidx/compose/ui/test/InternalTestApi.class", "name": "androidx/compose/ui/test/InternalTestApi.class", "size": 798, "crc": -490461130}, {"key": "androidx/compose/ui/test/KeyInjectionScope$DefaultImpls.class", "name": "androidx/compose/ui/test/KeyInjectionScope$DefaultImpls.class", "size": 6542, "crc": 1094869141}, {"key": "androidx/compose/ui/test/KeyInjectionScope.class", "name": "androidx/compose/ui/test/KeyInjectionScope.class", "size": 5870, "crc": 1852016420}, {"key": "androidx/compose/ui/test/KeyInjectionScopeImpl.class", "name": "androidx/compose/ui/test/KeyInjectionScopeImpl.class", "size": 8586, "crc": 548313796}, {"key": "androidx/compose/ui/test/KeyInjectionScopeKt.class", "name": "androidx/compose/ui/test/KeyInjectionScopeKt.class", "size": 7931, "crc": -636878884}, {"key": "androidx/compose/ui/test/KeyInputHelpersKt$performKeyPress$2.class", "name": "androidx/compose/ui/test/KeyInputHelpersKt$performKeyPress$2.class", "size": 1599, "crc": -1973722679}, {"key": "androidx/compose/ui/test/KeyInputHelpersKt.class", "name": "androidx/compose/ui/test/KeyInputHelpersKt.class", "size": 3286, "crc": -428148195}, {"key": "androidx/compose/ui/test/KeyInputState.class", "name": "androidx/compose/ui/test/KeyInputState.class", "size": 4835, "crc": -1271013242}, {"key": "androidx/compose/ui/test/LockKeyState$WhenMappings.class", "name": "androidx/compose/ui/test/LockKeyState$WhenMappings.class", "size": 871, "crc": 393910696}, {"key": "androidx/compose/ui/test/LockKeyState.class", "name": "androidx/compose/ui/test/LockKeyState.class", "size": 2567, "crc": -139637601}, {"key": "androidx/compose/ui/test/LsqVelocityPathFinder.class", "name": "androidx/compose/ui/test/LsqVelocityPathFinder.class", "size": 4362, "crc": 1436865698}, {"key": "androidx/compose/ui/test/MainTestClock$DefaultImpls.class", "name": "androidx/compose/ui/test/MainTestClock$DefaultImpls.class", "size": 715, "crc": -570254701}, {"key": "androidx/compose/ui/test/MainTestClock.class", "name": "androidx/compose/ui/test/MainTestClock.class", "size": 1949, "crc": 1831915212}, {"key": "androidx/compose/ui/test/MainTestClockImpl$1.class", "name": "androidx/compose/ui/test/MainTestClockImpl$1.class", "size": 1805, "crc": -206383906}, {"key": "androidx/compose/ui/test/MainTestClockImpl.class", "name": "androidx/compose/ui/test/MainTestClockImpl.class", "size": 1925, "crc": -773468184}, {"key": "androidx/compose/ui/test/MouseButton$Companion.class", "name": "androidx/compose/ui/test/MouseButton$Companion.class", "size": 1485, "crc": 434600932}, {"key": "androidx/compose/ui/test/MouseButton.class", "name": "androidx/compose/ui/test/MouseButton.class", "size": 2886, "crc": -1818525131}, {"key": "androidx/compose/ui/test/MouseInjectionScope.class", "name": "androidx/compose/ui/test/MouseInjectionScope.class", "size": 4675, "crc": -429529268}, {"key": "androidx/compose/ui/test/MouseInjectionScopeImpl.class", "name": "androidx/compose/ui/test/MouseInjectionScopeImpl.class", "size": 9962, "crc": 1144554573}, {"key": "androidx/compose/ui/test/MouseInjectionScopeKt$animateTo$1.class", "name": "androidx/compose/ui/test/MouseInjectionScopeKt$animateTo$1.class", "size": 1662, "crc": -1999712415}, {"key": "androidx/compose/ui/test/MouseInjectionScopeKt.class", "name": "androidx/compose/ui/test/MouseInjectionScopeKt.class", "size": 11246, "crc": 1713366386}, {"key": "androidx/compose/ui/test/MouseInputState.class", "name": "androidx/compose/ui/test/MouseInputState.class", "size": 3492, "crc": 331689730}, {"key": "androidx/compose/ui/test/MultiModalInjectionScope.class", "name": "androidx/compose/ui/test/MultiModalInjectionScope.class", "size": 1855, "crc": -52866381}, {"key": "androidx/compose/ui/test/MultiModalInjectionScopeImpl$boundsInRoot$2.class", "name": "androidx/compose/ui/test/MultiModalInjectionScopeImpl$boundsInRoot$2.class", "size": 1672, "crc": -167002112}, {"key": "androidx/compose/ui/test/MultiModalInjectionScopeImpl$visibleSize$2.class", "name": "androidx/compose/ui/test/MultiModalInjectionScopeImpl$visibleSize$2.class", "size": 1820, "crc": 1755914218}, {"key": "androidx/compose/ui/test/MultiModalInjectionScopeImpl.class", "name": "androidx/compose/ui/test/MultiModalInjectionScopeImpl.class", "size": 12342, "crc": 1404866536}, {"key": "androidx/compose/ui/test/OutputKt.class", "name": "androidx/compose/ui/test/OutputKt.class", "size": 12672, "crc": 1185400317}, {"key": "androidx/compose/ui/test/PartialGesture.class", "name": "androidx/compose/ui/test/PartialGesture.class", "size": 2392, "crc": -422986625}, {"key": "androidx/compose/ui/test/PlatformTextInputMethodOverrideKt$PlatformTextInputMethodTestOverride$1$1$interceptStartInputMethod$1.class", "name": "androidx/compose/ui/test/PlatformTextInputMethodOverrideKt$PlatformTextInputMethodTestOverride$1$1$interceptStartInputMethod$1.class", "size": 2340, "crc": 1550992950}, {"key": "androidx/compose/ui/test/PlatformTextInputMethodOverrideKt$PlatformTextInputMethodTestOverride$1$1.class", "name": "androidx/compose/ui/test/PlatformTextInputMethodOverrideKt$PlatformTextInputMethodTestOverride$1$1.class", "size": 3278, "crc": 344796671}, {"key": "androidx/compose/ui/test/PlatformTextInputMethodOverrideKt$PlatformTextInputMethodTestOverride$2.class", "name": "androidx/compose/ui/test/PlatformTextInputMethodOverrideKt$PlatformTextInputMethodTestOverride$2.class", "size": 2360, "crc": 1492702792}, {"key": "androidx/compose/ui/test/PlatformTextInputMethodOverrideKt.class", "name": "androidx/compose/ui/test/PlatformTextInputMethodOverrideKt.class", "size": 5444, "crc": 1987876617}, {"key": "androidx/compose/ui/test/ProxyAssertionError.class", "name": "androidx/compose/ui/test/ProxyAssertionError.class", "size": 1586, "crc": -634991245}, {"key": "androidx/compose/ui/test/RobolectricIdlingStrategy$awaitIdle$2.class", "name": "androidx/compose/ui/test/RobolectricIdlingStrategy$awaitIdle$2.class", "size": 3451, "crc": -1277085911}, {"key": "androidx/compose/ui/test/RobolectricIdlingStrategy$runUntilIdle$1.class", "name": "androidx/compose/ui/test/RobolectricIdlingStrategy$runUntilIdle$1.class", "size": 3103, "crc": 264177668}, {"key": "androidx/compose/ui/test/RobolectricIdlingStrategy.class", "name": "androidx/compose/ui/test/RobolectricIdlingStrategy.class", "size": 6915, "crc": 1659220059}, {"key": "androidx/compose/ui/test/RotaryInjectionScope.class", "name": "androidx/compose/ui/test/RotaryInjectionScope.class", "size": 792, "crc": -259394803}, {"key": "androidx/compose/ui/test/RotaryInjectionScopeImpl.class", "name": "androidx/compose/ui/test/RotaryInjectionScopeImpl.class", "size": 8068, "crc": 1265375088}, {"key": "androidx/compose/ui/test/RotaryInputState.class", "name": "androidx/compose/ui/test/RotaryInputState.class", "size": 711, "crc": -779999644}, {"key": "androidx/compose/ui/test/ScrollWheel$Companion.class", "name": "androidx/compose/ui/test/ScrollWheel$Companion.class", "size": 1319, "crc": 295002874}, {"key": "androidx/compose/ui/test/ScrollWheel.class", "name": "androidx/compose/ui/test/ScrollWheel.class", "size": 2769, "crc": 1860112046}, {"key": "androidx/compose/ui/test/SelectionResult.class", "name": "androidx/compose/ui/test/SelectionResult.class", "size": 1929, "crc": -155508004}, {"key": "androidx/compose/ui/test/SelectorsKt$onAncestors$1.class", "name": "androidx/compose/ui/test/SelectorsKt$onAncestors$1.class", "size": 2011, "crc": 357995281}, {"key": "androidx/compose/ui/test/SelectorsKt$onChild$1.class", "name": "androidx/compose/ui/test/SelectorsKt$onChild$1.class", "size": 1798, "crc": -2145789607}, {"key": "androidx/compose/ui/test/SelectorsKt$onChildren$1.class", "name": "androidx/compose/ui/test/SelectorsKt$onChildren$1.class", "size": 1817, "crc": 1382258793}, {"key": "androidx/compose/ui/test/SelectorsKt$onParent$1.class", "name": "androidx/compose/ui/test/SelectorsKt$onParent$1.class", "size": 1934, "crc": 1172092165}, {"key": "androidx/compose/ui/test/SelectorsKt$onSibling$1.class", "name": "androidx/compose/ui/test/SelectorsKt$onSibling$1.class", "size": 1783, "crc": 182671981}, {"key": "androidx/compose/ui/test/SelectorsKt$onSiblings$1.class", "name": "androidx/compose/ui/test/SelectorsKt$onSiblings$1.class", "size": 1796, "crc": -1415673891}, {"key": "androidx/compose/ui/test/SelectorsKt.class", "name": "androidx/compose/ui/test/SelectorsKt.class", "size": 7767, "crc": 2078942178}, {"key": "androidx/compose/ui/test/SemanticsMatcher$Companion$expectValue$1$1.class", "name": "androidx/compose/ui/test/SemanticsMatcher$Companion$expectValue$1$1.class", "size": 1327, "crc": -677265760}, {"key": "androidx/compose/ui/test/SemanticsMatcher$Companion$expectValue$1.class", "name": "androidx/compose/ui/test/SemanticsMatcher$Companion$expectValue$1.class", "size": 2709, "crc": 254995169}, {"key": "androidx/compose/ui/test/SemanticsMatcher$Companion$keyIsDefined$1.class", "name": "androidx/compose/ui/test/SemanticsMatcher$Companion$keyIsDefined$1.class", "size": 2229, "crc": -347832437}, {"key": "androidx/compose/ui/test/SemanticsMatcher$Companion$keyNotDefined$1.class", "name": "androidx/compose/ui/test/SemanticsMatcher$Companion$keyNotDefined$1.class", "size": 2267, "crc": 1138937116}, {"key": "androidx/compose/ui/test/SemanticsMatcher$Companion.class", "name": "androidx/compose/ui/test/SemanticsMatcher$Companion.class", "size": 3078, "crc": -1027904702}, {"key": "androidx/compose/ui/test/SemanticsMatcher$and$1.class", "name": "androidx/compose/ui/test/SemanticsMatcher$and$1.class", "size": 2083, "crc": -40653494}, {"key": "androidx/compose/ui/test/SemanticsMatcher$not$1.class", "name": "androidx/compose/ui/test/SemanticsMatcher$not$1.class", "size": 1873, "crc": 2028600673}, {"key": "androidx/compose/ui/test/SemanticsMatcher$or$1.class", "name": "androidx/compose/ui/test/SemanticsMatcher$or$1.class", "size": 2081, "crc": -1170487068}, {"key": "androidx/compose/ui/test/SemanticsMatcher.class", "name": "androidx/compose/ui/test/SemanticsMatcher.class", "size": 5032, "crc": -428008227}, {"key": "androidx/compose/ui/test/SemanticsNodeInteraction.class", "name": "androidx/compose/ui/test/SemanticsNodeInteraction.class", "size": 8458, "crc": -**********}, {"key": "androidx/compose/ui/test/SemanticsNodeInteractionCollection.class", "name": "androidx/compose/ui/test/SemanticsNodeInteractionCollection.class", "size": 7115, "crc": **********}, {"key": "androidx/compose/ui/test/SemanticsNodeInteractionsProvider$DefaultImpls.class", "name": "androidx/compose/ui/test/SemanticsNodeInteractionsProvider$DefaultImpls.class", "size": 971, "crc": 499208975}, {"key": "androidx/compose/ui/test/SemanticsNodeInteractionsProvider.class", "name": "androidx/compose/ui/test/SemanticsNodeInteractionsProvider.class", "size": 2195, "crc": 238088835}, {"key": "androidx/compose/ui/test/SemanticsSelector.class", "name": "androidx/compose/ui/test/SemanticsSelector.class", "size": 3919, "crc": **********}, {"key": "androidx/compose/ui/test/SemanticsSelectorKt$SemanticsSelector$1.class", "name": "androidx/compose/ui/test/SemanticsSelectorKt$SemanticsSelector$1.class", "size": 3807, "crc": -362437038}, {"key": "androidx/compose/ui/test/SemanticsSelectorKt$addIndexSelector$1.class", "name": "androidx/compose/ui/test/SemanticsSelectorKt$addIndexSelector$1.class", "size": 2830, "crc": 261423360}, {"key": "androidx/compose/ui/test/SemanticsSelectorKt$addLastNodeSelector$1.class", "name": "androidx/compose/ui/test/SemanticsSelectorKt$addLastNodeSelector$1.class", "size": 2179, "crc": -591331765}, {"key": "androidx/compose/ui/test/SemanticsSelectorKt$addSelectionFromSingleNode$1.class", "name": "androidx/compose/ui/test/SemanticsSelectorKt$addSelectionFromSingleNode$1.class", "size": 2566, "crc": -302606853}, {"key": "androidx/compose/ui/test/SemanticsSelectorKt$addSelectorViaMatcher$1.class", "name": "androidx/compose/ui/test/SemanticsSelectorKt$addSelectorViaMatcher$1.class", "size": 3905, "crc": 1237804388}, {"key": "androidx/compose/ui/test/SemanticsSelectorKt.class", "name": "androidx/compose/ui/test/SemanticsSelectorKt.class", "size": 4143, "crc": 1317928649}, {"key": "androidx/compose/ui/test/StateRestorationTester$InjectRestorationRegistry$1.class", "name": "androidx/compose/ui/test/StateRestorationTester$InjectRestorationRegistry$1.class", "size": 3519, "crc": 1254067444}, {"key": "androidx/compose/ui/test/StateRestorationTester$InjectRestorationRegistry$2.class", "name": "androidx/compose/ui/test/StateRestorationTester$InjectRestorationRegistry$2.class", "size": 2615, "crc": -242754280}, {"key": "androidx/compose/ui/test/StateRestorationTester$RestorationRegistry$emitChildrenWithRestoredState$1.class", "name": "androidx/compose/ui/test/StateRestorationTester$RestorationRegistry$emitChildrenWithRestoredState$1.class", "size": 2053, "crc": -756902175}, {"key": "androidx/compose/ui/test/StateRestorationTester$RestorationRegistry.class", "name": "androidx/compose/ui/test/StateRestorationTester$RestorationRegistry.class", "size": 5910, "crc": 1747011821}, {"key": "androidx/compose/ui/test/StateRestorationTester$emulateSaveAndRestore$1.class", "name": "androidx/compose/ui/test/StateRestorationTester$emulateSaveAndRestore$1.class", "size": 1436, "crc": -767701531}, {"key": "androidx/compose/ui/test/StateRestorationTester$emulateSaveAndRestore$2.class", "name": "androidx/compose/ui/test/StateRestorationTester$emulateSaveAndRestore$2.class", "size": 1438, "crc": 1733931484}, {"key": "androidx/compose/ui/test/StateRestorationTester$emulateSaveAndRestore$3.class", "name": "androidx/compose/ui/test/StateRestorationTester$emulateSaveAndRestore$3.class", "size": 1166, "crc": -174487285}, {"key": "androidx/compose/ui/test/StateRestorationTester$setContent$1$1.class", "name": "androidx/compose/ui/test/StateRestorationTester$setContent$1$1.class", "size": 3539, "crc": -247189368}, {"key": "androidx/compose/ui/test/StateRestorationTester$setContent$1.class", "name": "androidx/compose/ui/test/StateRestorationTester$setContent$1.class", "size": 3299, "crc": 830247803}, {"key": "androidx/compose/ui/test/StateRestorationTester.class", "name": "androidx/compose/ui/test/StateRestorationTester.class", "size": 9586, "crc": 1972832190}, {"key": "androidx/compose/ui/test/TestContext$getAllSemanticsNodes$1.class", "name": "androidx/compose/ui/test/TestContext$getAllSemanticsNodes$1.class", "size": 3791, "crc": 1991246807}, {"key": "androidx/compose/ui/test/TestContext.class", "name": "androidx/compose/ui/test/TestContext.class", "size": 3647, "crc": 30324979}, {"key": "androidx/compose/ui/test/TestMonotonicFrameClock$1.class", "name": "androidx/compose/ui/test/TestMonotonicFrameClock$1.class", "size": 1401, "crc": -1330357338}, {"key": "androidx/compose/ui/test/TestMonotonicFrameClock$performFrame$1.class", "name": "androidx/compose/ui/test/TestMonotonicFrameClock$performFrame$1.class", "size": 4789, "crc": 900272083}, {"key": "androidx/compose/ui/test/TestMonotonicFrameClock$withFrameNanos$2$1$1.class", "name": "androidx/compose/ui/test/TestMonotonicFrameClock$withFrameNanos$2$1$1.class", "size": 3340, "crc": 194103400}, {"key": "androidx/compose/ui/test/TestMonotonicFrameClock$withFrameNanos$2$1$2.class", "name": "androidx/compose/ui/test/TestMonotonicFrameClock$withFrameNanos$2$1$2.class", "size": 3914, "crc": -400399981}, {"key": "androidx/compose/ui/test/TestMonotonicFrameClock.class", "name": "androidx/compose/ui/test/TestMonotonicFrameClock.class", "size": 12752, "crc": 1374108316}, {"key": "androidx/compose/ui/test/TestMonotonicFrameClock_jvmKt.class", "name": "androidx/compose/ui/test/TestMonotonicFrameClock_jvmKt.class", "size": 1239, "crc": 1909017546}, {"key": "androidx/compose/ui/test/TestOwner.class", "name": "androidx/compose/ui/test/TestOwner.class", "size": 1242, "crc": -1199325030}, {"key": "androidx/compose/ui/test/TestOwnerKt.class", "name": "androidx/compose/ui/test/TestOwnerKt.class", "size": 931, "crc": 1824150809}, {"key": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$1.class", "name": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$1.class", "size": 1296, "crc": 1504991831}, {"key": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$2.class", "name": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$2.class", "size": 1296, "crc": 1112152587}, {"key": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$3.class", "name": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$3.class", "size": 1296, "crc": -921597679}, {"key": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$4.class", "name": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$4.class", "size": 1296, "crc": 10956886}, {"key": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$5.class", "name": "androidx/compose/ui/test/TextActionsKt$getNodeAndFocus$5.class", "size": 1296, "crc": -18019927}, {"key": "androidx/compose/ui/test/TextActionsKt$performImeAction$1.class", "name": "androidx/compose/ui/test/TextActionsKt$performImeAction$1.class", "size": 1236, "crc": 1395540687}, {"key": "androidx/compose/ui/test/TextActionsKt$performImeAction$2.class", "name": "androidx/compose/ui/test/TextActionsKt$performImeAction$2.class", "size": 1236, "crc": -1726728740}, {"key": "androidx/compose/ui/test/TextActionsKt$performImeAction$3$1.class", "name": "androidx/compose/ui/test/TextActionsKt$performImeAction$3$1.class", "size": 2703, "crc": 2144582441}, {"key": "androidx/compose/ui/test/TextActionsKt$performTextInput$1.class", "name": "androidx/compose/ui/test/TextActionsKt$performTextInput$1.class", "size": 1944, "crc": -1936302186}, {"key": "androidx/compose/ui/test/TextActionsKt$performTextInputSelection$1.class", "name": "androidx/compose/ui/test/TextActionsKt$performTextInputSelection$1.class", "size": 2117, "crc": -1701417396}, {"key": "androidx/compose/ui/test/TextActionsKt$performTextReplacement$1.class", "name": "androidx/compose/ui/test/TextActionsKt$performTextReplacement$1.class", "size": 1958, "crc": 2072847608}, {"key": "androidx/compose/ui/test/TextActionsKt.class", "name": "androidx/compose/ui/test/TextActionsKt.class", "size": 8384, "crc": 1734001488}, {"key": "androidx/compose/ui/test/TouchInjectionScope$DefaultImpls.class", "name": "androidx/compose/ui/test/TouchInjectionScope$DefaultImpls.class", "size": 9854, "crc": 824882092}, {"key": "androidx/compose/ui/test/TouchInjectionScope.class", "name": "androidx/compose/ui/test/TouchInjectionScope.class", "size": 12397, "crc": 1850493213}, {"key": "androidx/compose/ui/test/TouchInjectionScopeImpl.class", "name": "androidx/compose/ui/test/TouchInjectionScopeImpl.class", "size": 11201, "crc": -1054948525}, {"key": "androidx/compose/ui/test/TouchInjectionScopeKt$multiTouchSwipe$4.class", "name": "androidx/compose/ui/test/TouchInjectionScopeKt$multiTouchSwipe$4.class", "size": 1655, "crc": 641142998}, {"key": "androidx/compose/ui/test/TouchInjectionScopeKt$pinch$1.class", "name": "androidx/compose/ui/test/TouchInjectionScopeKt$pinch$1.class", "size": 1650, "crc": -920226411}, {"key": "androidx/compose/ui/test/TouchInjectionScopeKt$pinch$2.class", "name": "androidx/compose/ui/test/TouchInjectionScopeKt$pinch$2.class", "size": 1650, "crc": 2082287802}, {"key": "androidx/compose/ui/test/TouchInjectionScopeKt$swipe$1.class", "name": "androidx/compose/ui/test/TouchInjectionScopeKt$swipe$1.class", "size": 1646, "crc": -2006078093}, {"key": "androidx/compose/ui/test/TouchInjectionScopeKt$swipeWithVelocity$swipeFunction$1.class", "name": "androidx/compose/ui/test/TouchInjectionScopeKt$swipeWithVelocity$swipeFunction$1.class", "size": 1716, "crc": 244369095}, {"key": "androidx/compose/ui/test/TouchInjectionScopeKt.class", "name": "androidx/compose/ui/test/TouchInjectionScopeKt.class", "size": 17762, "crc": -1766134262}, {"key": "androidx/compose/ui/test/UncaughtExceptionHandler.class", "name": "androidx/compose/ui/test/UncaughtExceptionHandler.class", "size": 2531, "crc": -2098792700}, {"key": "androidx/compose/ui/test/UtilsKt.class", "name": "androidx/compose/ui/test/UtilsKt.class", "size": 1875, "crc": -1438214920}, {"key": "androidx/compose/ui/test/VelocityPathFinder$Companion.class", "name": "androidx/compose/ui/test/VelocityPathFinder$Companion.class", "size": 3422, "crc": 956128556}, {"key": "androidx/compose/ui/test/VelocityPathFinder.class", "name": "androidx/compose/ui/test/VelocityPathFinder.class", "size": 1230, "crc": -1645521636}, {"key": "androidx/compose/ui/test/VelocityPathFinderKt.class", "name": "androidx/compose/ui/test/VelocityPathFinderKt.class", "size": 1532, "crc": 1245091172}, {"key": "androidx/compose/ui/test/android/FrameCommitCallbackHelper.class", "name": "androidx/compose/ui/test/android/FrameCommitCallbackHelper.class", "size": 1308, "crc": 1001932751}, {"key": "androidx/compose/ui/test/android/PixelCopyException.class", "name": "androidx/compose/ui/test/android/PixelCopyException.class", "size": 1744, "crc": -1212596404}, {"key": "androidx/compose/ui/test/android/PixelCopyHelper.class", "name": "androidx/compose/ui/test/android/PixelCopyHelper.class", "size": 1722, "crc": -116019018}, {"key": "androidx/compose/ui/test/android/WindowCapture_androidKt$captureRegionToImage$1$1.class", "name": "androidx/compose/ui/test/android/WindowCapture_androidKt$captureRegionToImage$1$1.class", "size": 2142, "crc": 1917163909}, {"key": "androidx/compose/ui/test/android/WindowCapture_androidKt$captureRegionToImage$1.class", "name": "androidx/compose/ui/test/android/WindowCapture_androidKt$captureRegionToImage$1.class", "size": 2432, "crc": 1109354988}, {"key": "androidx/compose/ui/test/android/WindowCapture_androidKt$forceRedraw$1$2.class", "name": "androidx/compose/ui/test/android/WindowCapture_androidKt$forceRedraw$1$2.class", "size": 2799, "crc": -1286274371}, {"key": "androidx/compose/ui/test/android/WindowCapture_androidKt$forceRedraw$2.class", "name": "androidx/compose/ui/test/android/WindowCapture_androidKt$forceRedraw$2.class", "size": 1520, "crc": -107564823}, {"key": "androidx/compose/ui/test/android/WindowCapture_androidKt.class", "name": "androidx/compose/ui/test/android/WindowCapture_androidKt.class", "size": 10104, "crc": -1299243971}, {"key": "androidx/compose/ui/test/internal/DelayPropagatingContinuationInterceptorWrapper.class", "name": "androidx/compose/ui/test/internal/DelayPropagatingContinuationInterceptorWrapper.class", "size": 4902, "crc": 1241564847}, {"key": "androidx/compose/ui/test/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/ui/test/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 560, "crc": 882605627}, {"key": "androidx/compose/ui/test/junit4/android/ComposeNotIdleException.class", "name": "androidx/compose/ui/test/junit4/android/ComposeNotIdleException.class", "size": 1101, "crc": 1313124654}, {"key": "META-INF/androidx.compose.ui_ui-test.version", "name": "META-INF/androidx.compose.ui_ui-test.version", "size": 6, "crc": 1621725393}, {"key": "META-INF/ui-test_release.kotlin_module", "name": "META-INF/ui-test_release.kotlin_module", "size": 995, "crc": -10458151}]