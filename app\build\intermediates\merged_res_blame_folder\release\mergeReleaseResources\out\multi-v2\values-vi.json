{"logs": [{"outputFile": "com.falaileh.nisso.app-mergeReleaseResources-54:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9360fea9cc06fc380f90ebb846dd3e6a\\transformed\\foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,86", "endOffsets": "138,225"}, "to": {"startLines": "65,66", "startColumns": "4,4", "startOffsets": "5978,6066", "endColumns": "87,86", "endOffsets": "6061,6148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8dca2d17bb97bd73368ea919040cc370\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1028,1119,1191,1267,1344,1420,1497,1563", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1023,1114,1186,1262,1339,1415,1492,1558,1672"}, "to": {"startLines": "36,37,38,39,40,50,51,52,53,54,55,57,58,59,60,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3528,3624,3710,3816,3916,4700,4785,4878,4972,5053,5143,5319,5391,5467,5544,5721,5798,5864", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,75,76,75,76,65,113", "endOffsets": "3619,3705,3811,3911,4003,4780,4873,4967,5048,5138,5229,5386,5462,5539,5615,5793,5859,5973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85e1e1d8941ac4fc444c1da209e0c205\\transformed\\appcompat-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,5234", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,5314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\54903ff84a4690cb020fc1e9d9860152\\transformed\\media3-exoplayer-1.0.0-beta03\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,259,328,419,489,579,667", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "123,186,254,323,414,484,574,662,742"}, "to": {"startLines": "41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4008,4081,4144,4212,4281,4372,4442,4532,4620", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "4076,4139,4207,4276,4367,4437,4527,4615,4695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\910260a50c4cc0fe03b922548d59c7fb\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "29,30,31,32,33,34,35,61", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2798,2895,2997,3096,3196,3299,3412,5620", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "2890,2992,3091,3191,3294,3407,3523,5716"}}]}]}