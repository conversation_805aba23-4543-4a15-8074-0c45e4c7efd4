{"logs": [{"outputFile": "com.falaileh.nisso.test.app-mergeDebugAndroidTestResources-34:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8dca2d17bb97bd73368ea919040cc370\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,987,1074,1146,1230,1308,1384,1469,1539", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,982,1069,1141,1225,1303,1379,1464,1534,1657"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,929,1013,1107,1210,1296,1376,1465,1553,1635,1718,1805,1877,1961,2039,2216,2301,2371", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "924,1008,1102,1205,1291,1371,1460,1548,1630,1713,1800,1872,1956,2034,2110,2296,2366,2489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\910260a50c4cc0fe03b922548d59c7fb\\transformed\\core-1.13.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,612,715,2115", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "198,300,400,501,607,710,831,2211"}}]}]}