package com.falaileh.nisso

import android.os.Bundle
import android.view.KeyEvent
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.tv.material3.ExperimentalTvMaterial3Api
import androidx.tv.material3.Surface
import com.falaileh.nisso.ui.components.FloatingHeartsOverlay
import com.falaileh.nisso.ui.components.LoveMessageScreen
import com.falaileh.nisso.ui.components.SparkleOverlay
import com.falaileh.nisso.ui.theme.NissoTheme
import com.falaileh.nisso.ui.viewmodel.MessageViewModel

class MainActivity : ComponentActivity() {

    private val messageViewModel: MessageViewModel by viewModels()

    @OptIn(ExperimentalTvMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Enable immersive full-screen mode
        setupFullScreenMode()

        setContent {
            NissoTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    shape = RectangleShape
                ) {
                    LoveMessageApp(viewModel = messageViewModel)
                }
            }
        }
    }

    private fun setupFullScreenMode() {
        // Hide system bars for immersive experience
        WindowCompat.setDecorFitsSystemWindows(window, false)

        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.apply {
            hide(WindowInsetsCompat.Type.systemBars())
            systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }

        // Keep screen on
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_DPAD_LEFT -> {
                val currentState = messageViewModel.uiState.value
                // Only allow previous if not on first message
                if (currentState.hasMessages && currentState.currentMessageIndex > 0) {
                    messageViewModel.previousMessage()
                }
                true
            }
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                messageViewModel.nextMessage()
                true
            }
            KeyEvent.KEYCODE_DPAD_CENTER,
            KeyEvent.KEYCODE_ENTER -> {
                messageViewModel.refreshMessages()
                true
            }
            KeyEvent.KEYCODE_BACK -> {
                finish()
                true
            }
            else -> super.onKeyDown(keyCode, event)
        }
    }
}

@Composable
fun LoveMessageApp(
    viewModel: MessageViewModel,
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsState()

    Box(modifier = modifier.fillMaxSize()) {
        // Main message screen
        LoveMessageScreen(
            uiState = uiState,
            modifier = Modifier.fillMaxSize()
        )

        // Romantic overlay effects
        FloatingHeartsOverlay(
            modifier = Modifier.fillMaxSize()
        )

        SparkleOverlay(
            modifier = Modifier.fillMaxSize()
        )
    }
}