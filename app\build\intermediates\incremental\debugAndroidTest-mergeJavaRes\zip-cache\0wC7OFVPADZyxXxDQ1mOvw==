[{"key": "androidx/test/services/events/run/TestStartedEvent.class", "name": "androidx/test/services/events/run/TestStartedEvent.class", "size": 1148, "crc": -1909870513}, {"key": "androidx/test/services/events/run/TestRunStartedEvent.class", "name": "androidx/test/services/events/run/TestRunStartedEvent.class", "size": 1152, "crc": -2081483092}, {"key": "androidx/test/services/events/run/TestRunFinishedEvent.class", "name": "androidx/test/services/events/run/TestRunFinishedEvent.class", "size": 2726, "crc": 812178440}, {"key": "androidx/test/services/events/run/TestRunEventWithTestCase.class", "name": "androidx/test/services/events/run/TestRunEventWithTestCase.class", "size": 1269, "crc": 1766645166}, {"key": "androidx/test/services/events/run/TestRunEventFactory.class", "name": "androidx/test/services/events/run/TestRunEventFactory.class", "size": 2853, "crc": 890415038}, {"key": "androidx/test/services/events/run/TestRunEventFactory$1.class", "name": "androidx/test/services/events/run/TestRunEventFactory$1.class", "size": 1192, "crc": -531363157}, {"key": "androidx/test/services/events/run/TestRunEvent.class", "name": "androidx/test/services/events/run/TestRunEvent.class", "size": 1303, "crc": 2130936282}, {"key": "androidx/test/services/events/run/TestRunEvent$EventType.class", "name": "androidx/test/services/events/run/TestRunEvent$EventType.class", "size": 1699, "crc": 2116748200}, {"key": "androidx/test/services/events/run/TestIgnoredEvent.class", "name": "androidx/test/services/events/run/TestIgnoredEvent.class", "size": 1148, "crc": 156055622}, {"key": "androidx/test/services/events/run/TestFinishedEvent.class", "name": "androidx/test/services/events/run/TestFinishedEvent.class", "size": 1152, "crc": 1109252855}, {"key": "androidx/test/services/events/run/TestFailureEvent.class", "name": "androidx/test/services/events/run/TestFailureEvent.class", "size": 1806, "crc": -1764163605}, {"key": "androidx/test/services/events/run/TestAssumptionFailureEvent.class", "name": "androidx/test/services/events/run/TestAssumptionFailureEvent.class", "size": 1301, "crc": -1792010731}, {"key": "androidx/test/services/events/run/ITestRunEvent.class", "name": "androidx/test/services/events/run/ITestRunEvent.class", "size": 515, "crc": 852717723}, {"key": "androidx/test/services/events/run/ITestRunEvent$Stub.class", "name": "androidx/test/services/events/run/ITestRunEvent$Stub.class", "size": 2200, "crc": -1070364362}, {"key": "androidx/test/services/events/run/ITestRunEvent$Stub$Proxy.class", "name": "androidx/test/services/events/run/ITestRunEvent$Stub$Proxy.class", "size": 1343, "crc": 861585000}, {"key": "androidx/test/services/events/platform/TestRunStartedEvent.class", "name": "androidx/test/services/events/platform/TestRunStartedEvent.class", "size": 1853, "crc": 1015890594}, {"key": "androidx/test/services/events/platform/TestRunFinishedEvent.class", "name": "androidx/test/services/events/platform/TestRunFinishedEvent.class", "size": 2126, "crc": -1779407901}, {"key": "androidx/test/services/events/platform/TestRunErrorEvent.class", "name": "androidx/test/services/events/platform/TestRunErrorEvent.class", "size": 2169, "crc": -808109761}, {"key": "androidx/test/services/events/platform/TestPlatformEventFactory.class", "name": "androidx/test/services/events/platform/TestPlatformEventFactory.class", "size": 2920, "crc": 2128286989}, {"key": "androidx/test/services/events/platform/TestPlatformEventFactory$1.class", "name": "androidx/test/services/events/platform/TestPlatformEventFactory$1.class", "size": 1235, "crc": 987013065}, {"key": "androidx/test/services/events/platform/TestPlatformEvent.class", "name": "androidx/test/services/events/platform/TestPlatformEvent.class", "size": 1368, "crc": -131918934}, {"key": "androidx/test/services/events/platform/TestPlatformEvent$EventType.class", "name": "androidx/test/services/events/platform/TestPlatformEvent$EventType.class", "size": 1735, "crc": 678623759}, {"key": "androidx/test/services/events/platform/TestCaseStartedEvent.class", "name": "androidx/test/services/events/platform/TestCaseStartedEvent.class", "size": 1916, "crc": 1871644501}, {"key": "androidx/test/services/events/platform/TestCaseFinishedEvent.class", "name": "androidx/test/services/events/platform/TestCaseFinishedEvent.class", "size": 2203, "crc": 850876229}, {"key": "androidx/test/services/events/platform/TestCaseErrorEvent.class", "name": "androidx/test/services/events/platform/TestCaseErrorEvent.class", "size": 2178, "crc": -1123958984}, {"key": "androidx/test/services/events/platform/ITestPlatformEvent.class", "name": "androidx/test/services/events/platform/ITestPlatformEvent.class", "size": 565, "crc": 109315825}, {"key": "androidx/test/services/events/platform/ITestPlatformEvent$Stub.class", "name": "androidx/test/services/events/platform/ITestPlatformEvent$Stub.class", "size": 2300, "crc": 367549323}, {"key": "androidx/test/services/events/platform/ITestPlatformEvent$Stub$Proxy.class", "name": "androidx/test/services/events/platform/ITestPlatformEvent$Stub$Proxy.class", "size": 1423, "crc": -850705245}, {"key": "androidx/test/services/events/internal/Throwables.class", "name": "androidx/test/services/events/internal/Throwables.class", "size": 6741, "crc": -1390188270}, {"key": "androidx/test/services/events/internal/Throwables$State.class", "name": "androidx/test/services/events/internal/Throwables$State.class", "size": 3015, "crc": 100922380}, {"key": "androidx/test/services/events/internal/Throwables$State-IA.class", "name": "androidx/test/services/events/internal/Throwables$State-IA.class", "size": 268, "crc": 580572805}, {"key": "androidx/test/services/events/internal/Throwables$State$4.class", "name": "androidx/test/services/events/internal/Throwables$State$4.class", "size": 1025, "crc": 1412139693}, {"key": "androidx/test/services/events/internal/Throwables$State$4-IA.class", "name": "androidx/test/services/events/internal/Throwables$State$4-IA.class", "size": 270, "crc": -1181469642}, {"key": "androidx/test/services/events/internal/Throwables$State$3.class", "name": "androidx/test/services/events/internal/Throwables$State$3.class", "size": 1318, "crc": -1276047589}, {"key": "androidx/test/services/events/internal/Throwables$State$3-IA.class", "name": "androidx/test/services/events/internal/Throwables$State$3-IA.class", "size": 270, "crc": -854476632}, {"key": "androidx/test/services/events/internal/Throwables$State$2.class", "name": "androidx/test/services/events/internal/Throwables$State$2.class", "size": 1331, "crc": -784152988}, {"key": "androidx/test/services/events/internal/Throwables$State$2-IA.class", "name": "androidx/test/services/events/internal/Throwables$State$2-IA.class", "size": 270, "crc": 329310903}, {"key": "androidx/test/services/events/internal/Throwables$State$1.class", "name": "androidx/test/services/events/internal/Throwables$State$1.class", "size": 1240, "crc": 1422392046}, {"key": "androidx/test/services/events/internal/Throwables$State$1-IA.class", "name": "androidx/test/services/events/internal/Throwables$State$1-IA.class", "size": 270, "crc": -1425929513}, {"key": "androidx/test/services/events/internal/Throwables$1.class", "name": "androidx/test/services/events/internal/Throwables$1.class", "size": 951, "crc": -1740271044}, {"key": "androidx/test/services/events/internal/StackTrimmer.class", "name": "androidx/test/services/events/internal/StackTrimmer.class", "size": 2053, "crc": -1024058764}, {"key": "androidx/test/services/events/discovery/TestFoundEvent.class", "name": "androidx/test/services/events/discovery/TestFoundEvent.class", "size": 1626, "crc": -1822395884}, {"key": "androidx/test/services/events/discovery/TestDiscoveryStartedEvent.class", "name": "androidx/test/services/events/discovery/TestDiscoveryStartedEvent.class", "size": 782, "crc": 208594272}, {"key": "androidx/test/services/events/discovery/TestDiscoveryFinishedEvent.class", "name": "androidx/test/services/events/discovery/TestDiscoveryFinishedEvent.class", "size": 786, "crc": 1732830347}, {"key": "androidx/test/services/events/discovery/TestDiscoveryEventFactory.class", "name": "androidx/test/services/events/discovery/TestDiscoveryEventFactory.class", "size": 2782, "crc": 457859626}, {"key": "androidx/test/services/events/discovery/TestDiscoveryEventFactory$1.class", "name": "androidx/test/services/events/discovery/TestDiscoveryEventFactory$1.class", "size": 1100, "crc": 725163719}, {"key": "androidx/test/services/events/discovery/TestDiscoveryEvent.class", "name": "androidx/test/services/events/discovery/TestDiscoveryEvent.class", "size": 1381, "crc": 2110066530}, {"key": "androidx/test/services/events/discovery/TestDiscoveryEvent$EventType.class", "name": "androidx/test/services/events/discovery/TestDiscoveryEvent$EventType.class", "size": 1588, "crc": 1214473905}, {"key": "androidx/test/services/events/discovery/TestDiscoveryErrorEvent.class", "name": "androidx/test/services/events/discovery/TestDiscoveryErrorEvent.class", "size": 1908, "crc": 1898557664}, {"key": "androidx/test/services/events/discovery/ITestDiscoveryEvent.class", "name": "androidx/test/services/events/discovery/ITestDiscoveryEvent.class", "size": 575, "crc": -1589110955}, {"key": "androidx/test/services/events/discovery/ITestDiscoveryEvent$Stub.class", "name": "androidx/test/services/events/discovery/ITestDiscoveryEvent$Stub.class", "size": 2320, "crc": -2120628218}, {"key": "androidx/test/services/events/discovery/ITestDiscoveryEvent$Stub$Proxy.class", "name": "androidx/test/services/events/discovery/ITestDiscoveryEvent$Stub$Proxy.class", "size": 1439, "crc": 1384630071}, {"key": "androidx/test/services/events/TimeStamp.class", "name": "androidx/test/services/events/TimeStamp.class", "size": 2449, "crc": 523723601}, {"key": "androidx/test/services/events/TimeStamp$1.class", "name": "androidx/test/services/events/TimeStamp$1.class", "size": 1257, "crc": 850004478}, {"key": "androidx/test/services/events/TestStatus.class", "name": "androidx/test/services/events/TestStatus.class", "size": 1956, "crc": -1370886385}, {"key": "androidx/test/services/events/TestStatus$Status.class", "name": "androidx/test/services/events/TestStatus$Status.class", "size": 1540, "crc": -423777307}, {"key": "androidx/test/services/events/TestStatus$1.class", "name": "androidx/test/services/events/TestStatus$1.class", "size": 1264, "crc": 1078556736}, {"key": "androidx/test/services/events/TestRunInfo.class", "name": "androidx/test/services/events/TestRunInfo.class", "size": 2381, "crc": 1362763721}, {"key": "androidx/test/services/events/TestRunInfo$1.class", "name": "androidx/test/services/events/TestRunInfo$1.class", "size": 1271, "crc": -474744399}, {"key": "androidx/test/services/events/TestEventException.class", "name": "androidx/test/services/events/TestEventException.class", "size": 728, "crc": -270871983}, {"key": "androidx/test/services/events/TestCaseInfo.class", "name": "androidx/test/services/events/TestCaseInfo.class", "size": 3168, "crc": -374933402}, {"key": "androidx/test/services/events/TestCaseInfo$1.class", "name": "androidx/test/services/events/TestCaseInfo$1.class", "size": 1278, "crc": -1257426432}, {"key": "androidx/test/services/events/ParcelableConverter.class", "name": "androidx/test/services/events/ParcelableConverter.class", "size": 8221, "crc": -383922882}, {"key": "androidx/test/services/events/FailureInfo.class", "name": "androidx/test/services/events/FailureInfo.class", "size": 2457, "crc": -217111820}, {"key": "androidx/test/services/events/FailureInfo$1.class", "name": "androidx/test/services/events/FailureInfo$1.class", "size": 1271, "crc": -1560390511}, {"key": "androidx/test/services/events/ErrorInfo.class", "name": "androidx/test/services/events/ErrorInfo.class", "size": 2610, "crc": -411365356}, {"key": "androidx/test/services/events/ErrorInfo$1.class", "name": "androidx/test/services/events/ErrorInfo$1.class", "size": 1257, "crc": -1660802877}, {"key": "androidx/test/services/events/AnnotationValue.class", "name": "androidx/test/services/events/AnnotationValue.class", "size": 2406, "crc": 642868121}, {"key": "androidx/test/services/events/AnnotationValue-IA.class", "name": "androidx/test/services/events/AnnotationValue-IA.class", "size": 258, "crc": 1912888190}, {"key": "androidx/test/services/events/AnnotationValue$1.class", "name": "androidx/test/services/events/AnnotationValue$1.class", "size": 1350, "crc": -1078876311}, {"key": "androidx/test/services/events/AnnotationInfo.class", "name": "androidx/test/services/events/AnnotationInfo.class", "size": 2445, "crc": -110871993}, {"key": "androidx/test/services/events/AnnotationInfo-IA.class", "name": "androidx/test/services/events/AnnotationInfo-IA.class", "size": 257, "crc": 1375135321}, {"key": "androidx/test/services/events/AnnotationInfo$1.class", "name": "androidx/test/services/events/AnnotationInfo$1.class", "size": 1342, "crc": -1282293083}, {"key": "androidx/test/runner/suites/PackagePrefixClasspathSuite.class", "name": "androidx/test/runner/suites/PackagePrefixClasspathSuite.class", "size": 3917, "crc": -441492174}, {"key": "androidx/test/runner/suites/AndroidClasspathSuite.class", "name": "androidx/test/runner/suites/AndroidClasspathSuite.class", "size": 836, "crc": 901791669}, {"key": "androidx/test/runner/suites/AndroidClasspathSuite$RunnerSuite.class", "name": "androidx/test/runner/suites/AndroidClasspathSuite$RunnerSuite.class", "size": 2912, "crc": 1017070141}, {"key": "androidx/test/runner/screenshot/UiAutomationWrapper.class", "name": "androidx/test/runner/screenshot/UiAutomationWrapper.class", "size": 867, "crc": -766213426}, {"key": "androidx/test/runner/screenshot/TakeScreenshotCallable.class", "name": "androidx/test/runner/screenshot/TakeScreenshotCallable.class", "size": 2091, "crc": -233734471}, {"key": "androidx/test/runner/screenshot/TakeScreenshotCallable-IA.class", "name": "androidx/test/runner/screenshot/TakeScreenshotCallable-IA.class", "size": 267, "crc": -435383988}, {"key": "androidx/test/runner/screenshot/TakeScreenshotCallable$Factory.class", "name": "androidx/test/runner/screenshot/TakeScreenshotCallable$Factory.class", "size": 951, "crc": -1422045491}, {"key": "androidx/test/runner/screenshot/Screenshot.class", "name": "androidx/test/runner/screenshot/Screenshot.class", "size": 6142, "crc": -1092460518}, {"key": "androidx/test/runner/screenshot/Screenshot$ScreenShotException.class", "name": "androidx/test/runner/screenshot/Screenshot$ScreenShotException.class", "size": 652, "crc": -1241657244}, {"key": "androidx/test/runner/screenshot/ScreenCaptureProcessor.class", "name": "androidx/test/runner/screenshot/ScreenCaptureProcessor.class", "size": 417, "crc": -1005764013}, {"key": "androidx/test/runner/screenshot/ScreenCapture.class", "name": "androidx/test/runner/screenshot/ScreenCapture.class", "size": 4789, "crc": -1616386981}, {"key": "androidx/test/runner/screenshot/BasicScreenCaptureProcessor.class", "name": "androidx/test/runner/screenshot/BasicScreenCaptureProcessor.class", "size": 4415, "crc": 1006326945}, {"key": "androidx/test/runner/permission/UiAutomationShellCommand.class", "name": "androidx/test/runner/permission/UiAutomationShellCommand.class", "size": 4046, "crc": 394741583}, {"key": "androidx/test/runner/permission/UiAutomationShellCommand$PmCommand.class", "name": "androidx/test/runner/permission/UiAutomationShellCommand$PmCommand.class", "size": 1921, "crc": -1830061909}, {"key": "androidx/test/runner/permission/ShellCommand.class", "name": "androidx/test/runner/permission/ShellCommand.class", "size": 1510, "crc": 1703884471}, {"key": "androidx/test/runner/permission/RequestPermissionCallable.class", "name": "androidx/test/runner/permission/RequestPermissionCallable.class", "size": 2937, "crc": 184419026}, {"key": "androidx/test/runner/permission/RequestPermissionCallable$Result.class", "name": "androidx/test/runner/permission/RequestPermissionCallable$Result.class", "size": 1457, "crc": 792658278}, {"key": "androidx/test/runner/permission/RequestPermissionCallable$$ExternalSyntheticBackport0.class", "name": "androidx/test/runner/permission/RequestPermissionCallable$$ExternalSyntheticBackport0.class", "size": 468, "crc": 1833698162}, {"key": "androidx/test/runner/permission/PermissionRequester.class", "name": "androidx/test/runner/permission/PermissionRequester.class", "size": 4791, "crc": 161793047}, {"key": "androidx/test/runner/permission/GrantPermissionCallable.class", "name": "androidx/test/runner/permission/GrantPermissionCallable.class", "size": 2492, "crc": 589424135}, {"key": "androidx/test/runner/internal/deps/aidl/TransactionInterceptor.class", "name": "androidx/test/runner/internal/deps/aidl/TransactionInterceptor.class", "size": 504, "crc": 1684100937}, {"key": "androidx/test/runner/internal/deps/aidl/Codecs.class", "name": "androidx/test/runner/internal/deps/aidl/Codecs.class", "size": 4787, "crc": 1819032204}, {"key": "androidx/test/runner/internal/deps/aidl/BaseStub.class", "name": "androidx/test/runner/internal/deps/aidl/BaseStub.class", "size": 2551, "crc": 1053517731}, {"key": "androidx/test/runner/internal/deps/aidl/BaseProxy.class", "name": "androidx/test/runner/internal/deps/aidl/BaseProxy.class", "size": 2116, "crc": -1445227780}, {"key": "androidx/test/runner/intercepting/SingleActivityFactory.class", "name": "androidx/test/runner/intercepting/SingleActivityFactory.class", "size": 2251, "crc": -373465957}, {"key": "androidx/test/runner/UsageTrackerFacilitator.class", "name": "androidx/test/runner/UsageTrackerFacilitator.class", "size": 2842, "crc": 2067526653}, {"key": "androidx/test/runner/AndroidJUnitRunner.class", "name": "androidx/test/runner/AndroidJUnitRunner.class", "size": 16605, "crc": -1418362454}, {"key": "androidx/test/runner/AndroidJUnitRunner$2.class", "name": "androidx/test/runner/AndroidJUnitRunner$2.class", "size": 982, "crc": -1655864643}, {"key": "androidx/test/runner/AndroidJUnitRunner$1.class", "name": "androidx/test/runner/AndroidJUnitRunner$1.class", "size": 985, "crc": -1288412545}, {"key": "androidx/test/runner/AndroidJUnitRunner$$ExternalSyntheticLambda0.class", "name": "androidx/test/runner/AndroidJUnitRunner$$ExternalSyntheticLambda0.class", "size": 978, "crc": 408803260}, {"key": "androidx/test/runner/AndroidJUnit4.class", "name": "androidx/test/runner/AndroidJUnit4.class", "size": 4986, "crc": 687260689}, {"key": "androidx/test/orchestrator/listeners/result/TestRunResult.class", "name": "androidx/test/orchestrator/listeners/result/TestRunResult.class", "size": 10041, "crc": 1697603316}, {"key": "androidx/test/orchestrator/listeners/result/TestResult.class", "name": "androidx/test/orchestrator/listeners/result/TestResult.class", "size": 2896, "crc": -1692789943}, {"key": "androidx/test/orchestrator/listeners/result/TestResult$TestStatus.class", "name": "androidx/test/orchestrator/listeners/result/TestResult$TestStatus.class", "size": 1624, "crc": 1816167868}, {"key": "androidx/test/orchestrator/listeners/result/TestIdentifier.class", "name": "androidx/test/orchestrator/listeners/result/TestIdentifier.class", "size": 1775, "crc": -519597084}, {"key": "androidx/test/orchestrator/listeners/result/ITestRunListener.class", "name": "androidx/test/orchestrator/listeners/result/ITestRunListener.class", "size": 1118, "crc": 1575942907}, {"key": "androidx/test/orchestrator/listeners/OrchestrationRunListener.class", "name": "androidx/test/orchestrator/listeners/OrchestrationRunListener.class", "size": 2177, "crc": -1233504530}, {"key": "androidx/test/orchestrator/listeners/OrchestrationListenerManager.class", "name": "androidx/test/orchestrator/listeners/OrchestrationListenerManager.class", "size": 5471, "crc": -1280586227}, {"key": "androidx/test/orchestrator/listeners/OrchestrationListenerManager$TestEvent.class", "name": "androidx/test/orchestrator/listeners/OrchestrationListenerManager$TestEvent.class", "size": 1866, "crc": 797322737}, {"key": "androidx/test/orchestrator/listeners/OrchestrationListenerManager$1.class", "name": "androidx/test/orchestrator/listeners/OrchestrationListenerManager$1.class", "size": 1267, "crc": 730189490}, {"key": "androidx/test/orchestrator/junit/ParcelableResult.class", "name": "androidx/test/orchestrator/junit/ParcelableResult.class", "size": 3223, "crc": -1659216646}, {"key": "androidx/test/orchestrator/junit/ParcelableResult-IA.class", "name": "androidx/test/orchestrator/junit/ParcelableResult-IA.class", "size": 262, "crc": 1352131823}, {"key": "androidx/test/orchestrator/junit/ParcelableResult$1.class", "name": "androidx/test/orchestrator/junit/ParcelableResult$1.class", "size": 1375, "crc": 1872255417}, {"key": "androidx/test/orchestrator/junit/ParcelableFailure.class", "name": "androidx/test/orchestrator/junit/ParcelableFailure.class", "size": 4012, "crc": -1111194148}, {"key": "androidx/test/orchestrator/junit/ParcelableFailure-IA.class", "name": "androidx/test/orchestrator/junit/ParcelableFailure-IA.class", "size": 263, "crc": -443350034}, {"key": "androidx/test/orchestrator/junit/ParcelableFailure$1.class", "name": "androidx/test/orchestrator/junit/ParcelableFailure$1.class", "size": 1383, "crc": -1678713306}, {"key": "androidx/test/orchestrator/junit/ParcelableDescription.class", "name": "androidx/test/orchestrator/junit/ParcelableDescription.class", "size": 2757, "crc": -1039792708}, {"key": "androidx/test/orchestrator/junit/ParcelableDescription-IA.class", "name": "androidx/test/orchestrator/junit/ParcelableDescription-IA.class", "size": 267, "crc": 1097699212}, {"key": "androidx/test/orchestrator/junit/ParcelableDescription$1.class", "name": "androidx/test/orchestrator/junit/ParcelableDescription$1.class", "size": 1415, "crc": -1863376057}, {"key": "androidx/test/orchestrator/junit/BundleJUnitUtils.class", "name": "androidx/test/orchestrator/junit/BundleJUnitUtils.class", "size": 2579, "crc": -232323568}, {"key": "androidx/test/orchestrator/callback/OrchestratorV1Connection.class", "name": "androidx/test/orchestrator/callback/OrchestratorV1Connection.class", "size": 4513, "crc": 968521628}, {"key": "androidx/test/orchestrator/callback/OrchestratorV1Connection$$ExternalSyntheticLambda0.class", "name": "androidx/test/orchestrator/callback/OrchestratorV1Connection$$ExternalSyntheticLambda0.class", "size": 878, "crc": -720603200}, {"key": "androidx/test/orchestrator/callback/OrchestratorCallback.class", "name": "androidx/test/orchestrator/callback/OrchestratorCallback.class", "size": 600, "crc": 1758118650}, {"key": "androidx/test/orchestrator/callback/OrchestratorCallback$Stub.class", "name": "androidx/test/orchestrator/callback/OrchestratorCallback$Stub.class", "size": 2395, "crc": 1254210071}, {"key": "androidx/test/orchestrator/callback/OrchestratorCallback$Stub$Proxy.class", "name": "androidx/test/orchestrator/callback/OrchestratorCallback$Stub$Proxy.class", "size": 1582, "crc": 140219424}, {"key": "androidx/test/orchestrator/callback/NoOpOrchestratorConnection.class", "name": "androidx/test/orchestrator/callback/NoOpOrchestratorConnection.class", "size": 1445, "crc": 1592489229}, {"key": "androidx/test/orchestrator/callback/BundleConverter.class", "name": "androidx/test/orchestrator/callback/BundleConverter.class", "size": 5827, "crc": 1551060640}, {"key": "androidx/test/internal/util/AndroidRunnerParams.class", "name": "androidx/test/internal/util/AndroidRunnerParams.class", "size": 1576, "crc": 481812488}, {"key": "androidx/test/internal/util/AndroidRunnerBuilderUtil.class", "name": "androidx/test/internal/util/AndroidRunnerBuilderUtil.class", "size": 2317, "crc": 925971032}, {"key": "androidx/test/internal/runner/tracker/UsageTrackerRegistry.class", "name": "androidx/test/internal/runner/tracker/UsageTrackerRegistry.class", "size": 1278, "crc": -1287395224}, {"key": "androidx/test/internal/runner/tracker/UsageTrackerRegistry$AxtVersions.class", "name": "androidx/test/internal/runner/tracker/UsageTrackerRegistry$AxtVersions.class", "size": 512, "crc": 1870553261}, {"key": "androidx/test/internal/runner/tracker/UsageTracker.class", "name": "androidx/test/internal/runner/tracker/UsageTracker.class", "size": 492, "crc": -1686274574}, {"key": "androidx/test/internal/runner/tracker/UsageTracker$NoOpUsageTracker.class", "name": "androidx/test/internal/runner/tracker/UsageTracker$NoOpUsageTracker.class", "size": 761, "crc": -710968119}, {"key": "androidx/test/internal/runner/tracker/AnalyticsBasedUsageTracker.class", "name": "androidx/test/internal/runner/tracker/AnalyticsBasedUsageTracker.class", "size": 6112, "crc": 1795164775}, {"key": "androidx/test/internal/runner/tracker/AnalyticsBasedUsageTracker-IA.class", "name": "androidx/test/internal/runner/tracker/AnalyticsBasedUsageTracker-IA.class", "size": 277, "crc": 1710671932}, {"key": "androidx/test/internal/runner/tracker/AnalyticsBasedUsageTracker$Builder.class", "name": "androidx/test/internal/runner/tracker/AnalyticsBasedUsageTracker$Builder.class", "size": 6481, "crc": 991028449}, {"key": "androidx/test/internal/runner/listener/TraceRunListener.class", "name": "androidx/test/internal/runner/listener/TraceRunListener.class", "size": 1901, "crc": -1358539245}, {"key": "androidx/test/internal/runner/listener/SuiteAssignmentPrinter.class", "name": "androidx/test/internal/runner/listener/SuiteAssignmentPrinter.class", "size": 2998, "crc": -1011312584}, {"key": "androidx/test/internal/runner/listener/LogRunListener.class", "name": "androidx/test/internal/runner/listener/LogRunListener.class", "size": 2911, "crc": -1190496587}, {"key": "androidx/test/internal/runner/listener/InstrumentationRunListener.class", "name": "androidx/test/internal/runner/listener/InstrumentationRunListener.class", "size": 1582, "crc": -1155134225}, {"key": "androidx/test/internal/runner/listener/InstrumentationResultPrinter.class", "name": "androidx/test/internal/runner/listener/InstrumentationResultPrinter.class", "size": 6014, "crc": 576440417}, {"key": "androidx/test/internal/runner/listener/DelayInjector.class", "name": "androidx/test/internal/runner/listener/DelayInjector.class", "size": 1218, "crc": 451991285}, {"key": "androidx/test/internal/runner/listener/CoverageListener.class", "name": "androidx/test/internal/runner/listener/CoverageListener.class", "size": 2563, "crc": 580246370}, {"key": "androidx/test/internal/runner/listener/ActivityFinisherRunListener.class", "name": "androidx/test/internal/runner/listener/ActivityFinisherRunListener.class", "size": 1898, "crc": -2050257934}, {"key": "androidx/test/internal/runner/junit4/statement/UiThreadStatement.class", "name": "androidx/test/internal/runner/junit4/statement/UiThreadStatement.class", "size": 4880, "crc": 1662321346}, {"key": "androidx/test/internal/runner/junit4/statement/UiThreadStatement$1.class", "name": "androidx/test/internal/runner/junit4/statement/UiThreadStatement$1.class", "size": 1394, "crc": -1115406561}, {"key": "androidx/test/internal/runner/junit4/statement/RunBefores.class", "name": "androidx/test/internal/runner/junit4/statement/RunBefores.class", "size": 2662, "crc": 1939402689}, {"key": "androidx/test/internal/runner/junit4/statement/RunBefores$1.class", "name": "androidx/test/internal/runner/junit4/statement/RunBefores$1.class", "size": 1555, "crc": 1757692471}, {"key": "androidx/test/internal/runner/junit4/statement/RunAfters.class", "name": "androidx/test/internal/runner/junit4/statement/RunAfters.class", "size": 3121, "crc": 427933430}, {"key": "androidx/test/internal/runner/junit4/statement/RunAfters$1.class", "name": "androidx/test/internal/runner/junit4/statement/RunAfters$1.class", "size": 1456, "crc": -156091582}, {"key": "androidx/test/internal/runner/junit4/AndroidJUnit4ClassRunner.class", "name": "androidx/test/internal/runner/junit4/AndroidJUnit4ClassRunner.class", "size": 4561, "crc": -1970374408}, {"key": "androidx/test/internal/runner/junit4/AndroidJUnit4Builder.class", "name": "androidx/test/internal/runner/junit4/AndroidJUnit4Builder.class", "size": 2424, "crc": -661036551}, {"key": "androidx/test/internal/runner/junit4/AndroidAnnotatedBuilder.class", "name": "androidx/test/internal/runner/junit4/AndroidAnnotatedBuilder.class", "size": 2906, "crc": -1631209015}, {"key": "androidx/test/internal/runner/junit3/NonLeakyTestSuite.class", "name": "androidx/test/internal/runner/junit3/NonLeakyTestSuite.class", "size": 929, "crc": 1479725777}, {"key": "androidx/test/internal/runner/junit3/NonLeakyTestSuite$NonLeakyTest.class", "name": "androidx/test/internal/runner/junit3/NonLeakyTestSuite$NonLeakyTest.class", "size": 1526, "crc": -1843331672}, {"key": "androidx/test/internal/runner/junit3/NonExecutingTestSuite.class", "name": "androidx/test/internal/runner/junit3/NonExecutingTestSuite.class", "size": 2609, "crc": -729681519}, {"key": "androidx/test/internal/runner/junit3/NonExecutingTestResult.class", "name": "androidx/test/internal/runner/junit3/NonExecutingTestResult.class", "size": 991, "crc": -649191959}, {"key": "androidx/test/internal/runner/junit3/JUnit38ClassRunner.class", "name": "androidx/test/internal/runner/junit3/JUnit38ClassRunner.class", "size": 5925, "crc": -336121355}, {"key": "androidx/test/internal/runner/junit3/JUnit38ClassRunner$OldTestClassAdaptingListener.class", "name": "androidx/test/internal/runner/junit3/JUnit38ClassRunner$OldTestClassAdaptingListener.class", "size": 3225, "crc": -1326909969}, {"key": "androidx/test/internal/runner/junit3/JUnit38ClassRunner$OldTestClassAdaptingListener-IA.class", "name": "androidx/test/internal/runner/junit3/JUnit38ClassRunner$OldTestClassAdaptingListener-IA.class", "size": 297, "crc": -1383221126}, {"key": "androidx/test/internal/runner/junit3/DelegatingTestSuite.class", "name": "androidx/test/internal/runner/junit3/DelegatingTestSuite.class", "size": 2223, "crc": -1467853665}, {"key": "androidx/test/internal/runner/junit3/DelegatingTestResult.class", "name": "androidx/test/internal/runner/junit3/DelegatingTestResult.class", "size": 2593, "crc": 44485270}, {"key": "androidx/test/internal/runner/junit3/DelegatingFilterableTestSuite.class", "name": "androidx/test/internal/runner/junit3/DelegatingFilterableTestSuite.class", "size": 1858, "crc": -1268177670}, {"key": "androidx/test/internal/runner/junit3/AndroidTestSuite.class", "name": "androidx/test/internal/runner/junit3/AndroidTestSuite.class", "size": 7232, "crc": -1631623292}, {"key": "androidx/test/internal/runner/junit3/AndroidTestSuite$3.class", "name": "androidx/test/internal/runner/junit3/AndroidTestSuite$3.class", "size": 921, "crc": -664891609}, {"key": "androidx/test/internal/runner/junit3/AndroidTestSuite$2.class", "name": "androidx/test/internal/runner/junit3/AndroidTestSuite$2.class", "size": 1195, "crc": -2045169335}, {"key": "androidx/test/internal/runner/junit3/AndroidTestSuite$1.class", "name": "androidx/test/internal/runner/junit3/AndroidTestSuite$1.class", "size": 1218, "crc": 165233164}, {"key": "androidx/test/internal/runner/junit3/AndroidTestResult.class", "name": "androidx/test/internal/runner/junit3/AndroidTestResult.class", "size": 2652, "crc": -601781010}, {"key": "androidx/test/internal/runner/junit3/AndroidSuiteBuilder.class", "name": "androidx/test/internal/runner/junit3/AndroidSuiteBuilder.class", "size": 2437, "crc": 1880177966}, {"key": "androidx/test/internal/runner/junit3/AndroidJUnit3Builder.class", "name": "androidx/test/internal/runner/junit3/AndroidJUnit3Builder.class", "size": 1806, "crc": -217412501}, {"key": "androidx/test/internal/runner/filters/TestsRegExFilter.class", "name": "androidx/test/internal/runner/filters/TestsRegExFilter.class", "size": 1452, "crc": -1712333659}, {"key": "androidx/test/internal/runner/coverage/InstrumentationCoverageReporter.class", "name": "androidx/test/internal/runner/coverage/InstrumentationCoverageReporter.class", "size": 7722, "crc": 1816313561}, {"key": "androidx/test/internal/runner/coverage/InstrumentationCoverageReporter$$ExternalSyntheticBackport0.class", "name": "androidx/test/internal/runner/coverage/InstrumentationCoverageReporter$$ExternalSyntheticBackport0.class", "size": 762, "crc": -888720104}, {"key": "androidx/test/internal/runner/TestSize.class", "name": "androidx/test/internal/runner/TestSize.class", "size": 5961, "crc": 1637627520}, {"key": "androidx/test/internal/runner/TestRequestBuilder.class", "name": "androidx/test/internal/runner/TestRequestBuilder.class", "size": 18530, "crc": -394906476}, {"key": "androidx/test/internal/runner/TestRequestBuilder$SizeFilter.class", "name": "androidx/test/internal/runner/TestRequestBuilder$SizeFilter.class", "size": 1636, "crc": 291953326}, {"key": "androidx/test/internal/runner/TestRequestBuilder$ShardingFilter.class", "name": "androidx/test/internal/runner/TestRequestBuilder$ShardingFilter.class", "size": 1349, "crc": 1478874729}, {"key": "androidx/test/internal/runner/TestRequestBuilder$SdkSuppressFilter.class", "name": "androidx/test/internal/runner/TestRequestBuilder$SdkSuppressFilter.class", "size": 2421, "crc": 1791533053}, {"key": "androidx/test/internal/runner/TestRequestBuilder$SdkSuppressFilter-IA.class", "name": "androidx/test/internal/runner/TestRequestBuilder$SdkSuppressFilter-IA.class", "size": 279, "crc": 1077024707}, {"key": "androidx/test/internal/runner/TestRequestBuilder$RequiresDeviceFilter.class", "name": "androidx/test/internal/runner/TestRequestBuilder$RequiresDeviceFilter.class", "size": 2136, "crc": -1538272939}, {"key": "androidx/test/internal/runner/TestRequestBuilder$MethodFilter.class", "name": "androidx/test/internal/runner/TestRequestBuilder$MethodFilter.class", "size": 2657, "crc": 1151274613}, {"key": "androidx/test/internal/runner/TestRequestBuilder$LenientFilterRequest.class", "name": "androidx/test/internal/runner/TestRequestBuilder$LenientFilterRequest.class", "size": 1400, "crc": 1321862826}, {"key": "androidx/test/internal/runner/TestRequestBuilder$ExtendedSuite.class", "name": "androidx/test/internal/runner/TestRequestBuilder$ExtendedSuite.class", "size": 1729, "crc": 1661253141}, {"key": "androidx/test/internal/runner/TestRequestBuilder$DeviceBuildImpl.class", "name": "androidx/test/internal/runner/TestRequestBuilder$DeviceBuildImpl.class", "size": 1088, "crc": 224430954}, {"key": "androidx/test/internal/runner/TestRequestBuilder$DeviceBuildImpl-IA.class", "name": "androidx/test/internal/runner/TestRequestBuilder$DeviceBuildImpl-IA.class", "size": 277, "crc": 2093987550}, {"key": "androidx/test/internal/runner/TestRequestBuilder$DeviceBuild.class", "name": "androidx/test/internal/runner/TestRequestBuilder$DeviceBuild.class", "size": 358, "crc": 1451622698}, {"key": "androidx/test/internal/runner/TestRequestBuilder$CustomFilters.class", "name": "androidx/test/internal/runner/TestRequestBuilder$CustomFilters.class", "size": 3274, "crc": 678346583}, {"key": "androidx/test/internal/runner/TestRequestBuilder$CustomFilters-IA.class", "name": "androidx/test/internal/runner/TestRequestBuilder$CustomFilters-IA.class", "size": 275, "crc": 1579582471}, {"key": "androidx/test/internal/runner/TestRequestBuilder$ClassAndMethodFilter.class", "name": "androidx/test/internal/runner/TestRequestBuilder$ClassAndMethodFilter.class", "size": 2302, "crc": 550737070}, {"key": "androidx/test/internal/runner/TestRequestBuilder$ClassAndMethodFilter-IA.class", "name": "androidx/test/internal/runner/TestRequestBuilder$ClassAndMethodFilter-IA.class", "size": 282, "crc": 756298288}, {"key": "androidx/test/internal/runner/TestRequestBuilder$BlankRunner.class", "name": "androidx/test/internal/runner/TestRequestBuilder$BlankRunner.class", "size": 1116, "crc": -1480342600}, {"key": "androidx/test/internal/runner/TestRequestBuilder$BlankRunner-IA.class", "name": "androidx/test/internal/runner/TestRequestBuilder$BlankRunner-IA.class", "size": 273, "crc": -1226442094}, {"key": "androidx/test/internal/runner/TestRequestBuilder$AnnotationInclusionFilter.class", "name": "androidx/test/internal/runner/TestRequestBuilder$AnnotationInclusionFilter.class", "size": 1685, "crc": 1048292740}, {"key": "androidx/test/internal/runner/TestRequestBuilder$AnnotationExclusionFilter.class", "name": "androidx/test/internal/runner/TestRequestBuilder$AnnotationExclusionFilter.class", "size": 1690, "crc": -1032555276}, {"key": "androidx/test/internal/runner/TestLoader.class", "name": "androidx/test/internal/runner/TestLoader.class", "size": 1720, "crc": -1767375403}, {"key": "androidx/test/internal/runner/TestLoader$Factory.class", "name": "androidx/test/internal/runner/TestLoader$Factory.class", "size": 1198, "crc": 1382440299}, {"key": "androidx/test/internal/runner/TestExecutor.class", "name": "androidx/test/internal/runner/TestExecutor.class", "size": 5061, "crc": -903375128}, {"key": "androidx/test/internal/runner/TestExecutor-IA.class", "name": "androidx/test/internal/runner/TestExecutor-IA.class", "size": 255, "crc": -2078159251}, {"key": "androidx/test/internal/runner/TestExecutor$Builder.class", "name": "androidx/test/internal/runner/TestExecutor$Builder.class", "size": 1581, "crc": -481998084}, {"key": "androidx/test/internal/runner/TestExecutor$$ExternalSyntheticBackport0.class", "name": "androidx/test/internal/runner/TestExecutor$$ExternalSyntheticBackport0.class", "size": 734, "crc": -337503368}, {"key": "androidx/test/internal/runner/ScanningTestLoader.class", "name": "androidx/test/internal/runner/ScanningTestLoader.class", "size": 2429, "crc": 1242742713}, {"key": "androidx/test/internal/runner/RunnerArgs.class", "name": "androidx/test/internal/runner/RunnerArgs.class", "size": 7418, "crc": -1797046513}, {"key": "androidx/test/internal/runner/RunnerArgs-IA.class", "name": "androidx/test/internal/runner/RunnerArgs-IA.class", "size": 253, "crc": 702219626}, {"key": "androidx/test/internal/runner/RunnerArgs$TestFileArgs.class", "name": "androidx/test/internal/runner/RunnerArgs$TestFileArgs.class", "size": 1058, "crc": 67262090}, {"key": "androidx/test/internal/runner/RunnerArgs$TestFileArgs-IA.class", "name": "androidx/test/internal/runner/RunnerArgs$TestFileArgs-IA.class", "size": 266, "crc": 514947895}, {"key": "androidx/test/internal/runner/RunnerArgs$TestArg.class", "name": "androidx/test/internal/runner/RunnerArgs$TestArg.class", "size": 1159, "crc": -1215125993}, {"key": "androidx/test/internal/runner/RunnerArgs$Builder.class", "name": "androidx/test/internal/runner/RunnerArgs$Builder.class", "size": 21681, "crc": 174302225}, {"key": "androidx/test/internal/runner/RunnerArgs$Builder$$ExternalSyntheticBackport0.class", "name": "androidx/test/internal/runner/RunnerArgs$Builder$$ExternalSyntheticBackport0.class", "size": 740, "crc": 854961240}, {"key": "androidx/test/internal/runner/NonExecutingRunner.class", "name": "androidx/test/internal/runner/NonExecutingRunner.class", "size": 2450, "crc": -238789447}, {"key": "androidx/test/internal/runner/ErrorReportingRunner.class", "name": "androidx/test/internal/runner/ErrorReportingRunner.class", "size": 1817, "crc": -927365167}, {"key": "androidx/test/internal/runner/EmptyTestRunner.class", "name": "androidx/test/internal/runner/EmptyTestRunner.class", "size": 954, "crc": -1393712587}, {"key": "androidx/test/internal/runner/DirectTestLoader.class", "name": "androidx/test/internal/runner/DirectTestLoader.class", "size": 1891, "crc": 1509119776}, {"key": "androidx/test/internal/runner/ClassesArgTokenizer.class", "name": "androidx/test/internal/runner/ClassesArgTokenizer.class", "size": 1798, "crc": -311760051}, {"key": "androidx/test/internal/runner/ClassesArgTokenizer$TokenizerState.class", "name": "androidx/test/internal/runner/ClassesArgTokenizer$TokenizerState.class", "size": 1221, "crc": -1984297653}, {"key": "androidx/test/internal/runner/ClassesArgTokenizer$MethodTokenizerState.class", "name": "androidx/test/internal/runner/ClassesArgTokenizer$MethodTokenizerState.class", "size": 2766, "crc": -1984519310}, {"key": "androidx/test/internal/runner/ClassesArgTokenizer$ClassTokenizerState.class", "name": "androidx/test/internal/runner/ClassesArgTokenizer$ClassTokenizerState.class", "size": 2159, "crc": 1488435095}, {"key": "androidx/test/internal/runner/ClassesArgTokenizer$ClassTokenizerState-IA.class", "name": "androidx/test/internal/runner/ClassesArgTokenizer$ClassTokenizerState-IA.class", "size": 282, "crc": -1170073958}, {"key": "androidx/test/internal/runner/ClassPathScanner.class", "name": "androidx/test/internal/runner/ClassPathScanner.class", "size": 7591, "crc": 558655657}, {"key": "androidx/test/internal/runner/ClassPathScanner$InclusivePackageNamesFilter.class", "name": "androidx/test/internal/runner/ClassPathScanner$InclusivePackageNamesFilter.class", "size": 1738, "crc": 423283491}, {"key": "androidx/test/internal/runner/ClassPathScanner$ExternalClassNameFilter.class", "name": "androidx/test/internal/runner/ClassPathScanner$ExternalClassNameFilter.class", "size": 869, "crc": -1749176789}, {"key": "androidx/test/internal/runner/ClassPathScanner$ExcludePackageNameFilter.class", "name": "androidx/test/internal/runner/ClassPathScanner$ExcludePackageNameFilter.class", "size": 1100, "crc": 410116644}, {"key": "androidx/test/internal/runner/ClassPathScanner$ExcludeClassNamesFilter.class", "name": "androidx/test/internal/runner/ClassPathScanner$ExcludeClassNamesFilter.class", "size": 1120, "crc": 602437506}, {"key": "androidx/test/internal/runner/ClassPathScanner$ClassNameFilter.class", "name": "androidx/test/internal/runner/ClassPathScanner$ClassNameFilter.class", "size": 343, "crc": 936412193}, {"key": "androidx/test/internal/runner/ClassPathScanner$ChainedClassNameFilter.class", "name": "androidx/test/internal/runner/ClassPathScanner$ChainedClassNameFilter.class", "size": 1856, "crc": -2129463508}, {"key": "androidx/test/internal/runner/ClassPathScanner$AcceptAllFilter.class", "name": "androidx/test/internal/runner/ClassPathScanner$AcceptAllFilter.class", "size": 726, "crc": -1369514971}, {"key": "androidx/test/internal/runner/AndroidRunnerBuilder.class", "name": "androidx/test/internal/runner/AndroidRunnerBuilder.class", "size": 6424, "crc": 798496696}, {"key": "androidx/test/internal/runner/AndroidLogOnlyBuilder.class", "name": "androidx/test/internal/runner/AndroidLogOnlyBuilder.class", "size": 3550, "crc": -230853096}, {"key": "androidx/test/internal/package-info.class", "name": "androidx/test/internal/package-info.class", "size": 391, "crc": -1563821352}, {"key": "androidx/test/internal/events/client/package-info.class", "name": "androidx/test/internal/events/client/package-info.class", "size": 405, "crc": -47614615}, {"key": "androidx/test/internal/events/client/TestRunEventServiceConnection.class", "name": "androidx/test/internal/events/client/TestRunEventServiceConnection.class", "size": 2714, "crc": -1931336672}, {"key": "androidx/test/internal/events/client/TestRunEventServiceConnection$$ExternalSyntheticLambda0.class", "name": "androidx/test/internal/events/client/TestRunEventServiceConnection$$ExternalSyntheticLambda0.class", "size": 872, "crc": 361540489}, {"key": "androidx/test/internal/events/client/TestRunEventService.class", "name": "androidx/test/internal/events/client/TestRunEventService.class", "size": 437, "crc": -116522507}, {"key": "androidx/test/internal/events/client/TestPlatformListener.class", "name": "androidx/test/internal/events/client/TestPlatformListener.class", "size": 12188, "crc": -1699965399}, {"key": "androidx/test/internal/events/client/TestPlatformEventServiceConnection.class", "name": "androidx/test/internal/events/client/TestPlatformEventServiceConnection.class", "size": 2819, "crc": 1255108869}, {"key": "androidx/test/internal/events/client/TestPlatformEventServiceConnection$$ExternalSyntheticLambda0.class", "name": "androidx/test/internal/events/client/TestPlatformEventServiceConnection$$ExternalSyntheticLambda0.class", "size": 902, "crc": -626966221}, {"key": "androidx/test/internal/events/client/TestPlatformEventService.class", "name": "androidx/test/internal/events/client/TestPlatformEventService.class", "size": 457, "crc": 1044604915}, {"key": "androidx/test/internal/events/client/TestEventServiceConnectionBase.class", "name": "androidx/test/internal/events/client/TestEventServiceConnectionBase.class", "size": 5024, "crc": -757159749}, {"key": "androidx/test/internal/events/client/TestEventServiceConnectionBase$ServiceFromBinder.class", "name": "androidx/test/internal/events/client/TestEventServiceConnectionBase$ServiceFromBinder.class", "size": 534, "crc": 773497709}, {"key": "androidx/test/internal/events/client/TestEventServiceConnectionBase$1.class", "name": "androidx/test/internal/events/client/TestEventServiceConnectionBase$1.class", "size": 2613, "crc": -1537367076}, {"key": "androidx/test/internal/events/client/TestEventServiceConnection.class", "name": "androidx/test/internal/events/client/TestEventServiceConnection.class", "size": 343, "crc": -1079515410}, {"key": "androidx/test/internal/events/client/TestEventClientException.class", "name": "androidx/test/internal/events/client/TestEventClientException.class", "size": 686, "crc": 80363572}, {"key": "androidx/test/internal/events/client/TestEventClientConnectListener.class", "name": "androidx/test/internal/events/client/TestEventClientConnectListener.class", "size": 219, "crc": 981420791}, {"key": "androidx/test/internal/events/client/TestEventClientArgs.class", "name": "androidx/test/internal/events/client/TestEventClientArgs.class", "size": 2353, "crc": -2050427947}, {"key": "androidx/test/internal/events/client/TestEventClientArgs-IA.class", "name": "androidx/test/internal/events/client/TestEventClientArgs-IA.class", "size": 269, "crc": 866730078}, {"key": "androidx/test/internal/events/client/TestEventClientArgs$ConnectionFactory.class", "name": "androidx/test/internal/events/client/TestEventClientArgs$ConnectionFactory.class", "size": 610, "crc": 400433456}, {"key": "androidx/test/internal/events/client/TestEventClientArgs$Builder.class", "name": "androidx/test/internal/events/client/TestEventClientArgs$Builder.class", "size": 4726, "crc": 99282302}, {"key": "androidx/test/internal/events/client/TestEventClient.class", "name": "androidx/test/internal/events/client/TestEventClient.class", "size": 8281, "crc": -694921018}, {"key": "androidx/test/internal/events/client/TestDiscoveryListener.class", "name": "androidx/test/internal/events/client/TestDiscoveryListener.class", "size": 4982, "crc": -24658513}, {"key": "androidx/test/internal/events/client/TestDiscoveryEventServiceConnection.class", "name": "androidx/test/internal/events/client/TestDiscoveryEventServiceConnection.class", "size": 2812, "crc": -1639744913}, {"key": "androidx/test/internal/events/client/TestDiscoveryEventServiceConnection$$ExternalSyntheticLambda0.class", "name": "androidx/test/internal/events/client/TestDiscoveryEventServiceConnection$$ExternalSyntheticLambda0.class", "size": 908, "crc": -960605125}, {"key": "androidx/test/internal/events/client/TestDiscoveryEventService.class", "name": "androidx/test/internal/events/client/TestDiscoveryEventService.class", "size": 474, "crc": 2063608917}, {"key": "androidx/test/internal/events/client/OrchestratedInstrumentationListener.class", "name": "androidx/test/internal/events/client/OrchestratedInstrumentationListener.class", "size": 8235, "crc": -1025742566}, {"key": "androidx/test/internal/events/client/JUnitValidator.class", "name": "androidx/test/internal/events/client/JUnitValidator.class", "size": 916, "crc": -1362854625}, {"key": "androidx/test/internal/events/client/JUnitDescriptionParser.class", "name": "androidx/test/internal/events/client/JUnitDescriptionParser.class", "size": 1437, "crc": 1622420433}, {"key": "androidx/test/filters/Suppress.class", "name": "androidx/test/filters/Suppress.class", "size": 403, "crc": -1500274815}, {"key": "androidx/test/filters/SmallTest.class", "name": "androidx/test/filters/SmallTest.class", "size": 405, "crc": 110237871}, {"key": "androidx/test/filters/SdkSuppress.class", "name": "androidx/test/filters/SdkSuppress.class", "size": 570, "crc": 1854111372}, {"key": "androidx/test/filters/RequiresDevice.class", "name": "androidx/test/filters/RequiresDevice.class", "size": 415, "crc": -1487881003}, {"key": "androidx/test/filters/MediumTest.class", "name": "androidx/test/filters/MediumTest.class", "size": 407, "crc": 363476004}, {"key": "androidx/test/filters/LargeTest.class", "name": "androidx/test/filters/LargeTest.class", "size": 405, "crc": -1357754535}, {"key": "androidx/test/filters/FlakyTest.class", "name": "androidx/test/filters/FlakyTest.class", "size": 513, "crc": -840494693}, {"key": "androidx/test/filters/CustomFilter.class", "name": "androidx/test/filters/CustomFilter.class", "size": 626, "crc": 2003475486}, {"key": "androidx/test/filters/AbstractFilter.class", "name": "androidx/test/filters/AbstractFilter.class", "size": 2472, "crc": 1236682031}]