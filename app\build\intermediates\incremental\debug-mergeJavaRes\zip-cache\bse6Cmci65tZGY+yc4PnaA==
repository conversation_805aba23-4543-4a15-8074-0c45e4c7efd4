[{"key": "androidx/sqlite/db/framework/FrameworkSQLiteDatabase$Api30Impl.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteDatabase$Api30Impl.class", "size": 1735, "crc": 452411602}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteDatabase$Companion.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteDatabase$Companion.class", "size": 1027, "crc": 1660715098}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteDatabase$query$cursorFactory$1.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteDatabase$query$cursorFactory$1.class", "size": 3071, "crc": 1881967715}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteDatabase.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteDatabase.class", "size": 16996, "crc": -824159393}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$Companion.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$Companion.class", "size": 931, "crc": -610316450}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$DBRefHolder.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$DBRefHolder.class", "size": 1304, "crc": 573353440}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper$CallbackException.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper$CallbackException.class", "size": 2029, "crc": 2062670205}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper$CallbackName.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper$CallbackName.class", "size": 2030, "crc": -1660751037}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper$Companion.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper$Companion.class", "size": 3017, "crc": 592946049}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper$WhenMappings.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper$WhenMappings.class", "size": 1250, "crc": -1331356766}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$OpenHelper.class", "size": 10855, "crc": -1302417490}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$lazyDelegate$1.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper$lazyDelegate$1.class", "size": 3682, "crc": -1656147366}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelper.class", "size": 6486, "crc": -445431780}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelperFactory.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteOpenHelperFactory.class", "size": 2026, "crc": 1788852752}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteProgram.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteProgram.class", "size": 2250, "crc": 2024523400}, {"key": "androidx/sqlite/db/framework/FrameworkSQLiteStatement.class", "name": "androidx/sqlite/db/framework/FrameworkSQLiteStatement.class", "size": 2014, "crc": -1915227351}, {"key": "androidx/sqlite/util/ProcessLock$Companion.class", "name": "androidx/sqlite/util/ProcessLock$Companion.class", "size": 2873, "crc": -1869193194}, {"key": "androidx/sqlite/util/ProcessLock.class", "name": "androidx/sqlite/util/ProcessLock.class", "size": 4637, "crc": -1618066634}, {"key": "META-INF/androidx.sqlite_sqlite-framework.version", "name": "META-INF/androidx.sqlite_sqlite-framework.version", "size": 6, "crc": -192393839}, {"key": "META-INF/sqlite-framework_release.kotlin_module", "name": "META-INF/sqlite-framework_release.kotlin_module", "size": 24, "crc": 1613429616}]