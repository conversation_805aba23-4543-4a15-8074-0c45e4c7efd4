[{"key": "LICENSE-junit.txt", "name": "LICENSE-junit.txt", "size": 11376, "crc": **********}, {"key": "META-INF/androidx.compose.ui_ui-test-junit4.version", "name": "META-INF/androidx.compose.ui_ui-test-junit4.version", "size": 6, "crc": **********}, {"key": "META-INF/androidx.compose.ui_ui-test.version", "name": "META-INF/androidx.compose.ui_ui-test.version", "size": 6, "crc": **********}, {"key": "META-INF/kotlinx_coroutines_test.version", "name": "META-INF/kotlinx_coroutines_test.version", "size": 5, "crc": -**********}, {"key": "META-INF/services/kotlinx.coroutines.CoroutineExceptionHandler", "name": "META-INF/services/kotlinx.coroutines.CoroutineExceptionHandler", "size": 61, "crc": -787904639}, {"key": "META-INF/services/kotlinx.coroutines.internal.MainDispatcherFactory", "name": "META-INF/services/kotlinx.coroutines.internal.MainDispatcherFactory", "size": 59, "crc": **********}, {"key": "javax/annotation/CheckForNull.java", "name": "javax/annotation/CheckForNull.java", "size": 375, "crc": 644808890}, {"key": "javax/annotation/CheckForSigned.java", "name": "javax/annotation/CheckForSigned.java", "size": 698, "crc": -651941188}, {"key": "javax/annotation/CheckReturnValue.java", "name": "javax/annotation/CheckReturnValue.java", "size": 494, "crc": 761173099}, {"key": "javax/annotation/Detainted.java", "name": "javax/annotation/Detainted.java", "size": 375, "crc": -1094912357}, {"key": "javax/annotation/MatchesPattern.java", "name": "javax/annotation/MatchesPattern.java", "size": 883, "crc": 974170160}, {"key": "javax/annotation/Nonnegative.java", "name": "javax/annotation/Nonnegative.java", "size": 1303, "crc": 1661976749}, {"key": "javax/annotation/Nonnull.java", "name": "javax/annotation/Nonnull.java", "size": 706, "crc": -1116704706}, {"key": "javax/annotation/Nullable.java", "name": "javax/annotation/Nullable.java", "size": 373, "crc": -816379449}, {"key": "javax/annotation/OverridingMethodsMustInvokeSuper.java", "name": "javax/annotation/OverridingMethodsMustInvokeSuper.java", "size": 580, "crc": -1521465386}, {"key": "javax/annotation/ParametersAreNonnullByDefault.java", "name": "javax/annotation/ParametersAreNonnullByDefault.java", "size": 866, "crc": -*********}, {"key": "javax/annotation/PropertyKey.java", "name": "javax/annotation/PropertyKey.java", "size": 366, "crc": 1370743949}, {"key": "javax/annotation/RegEx.java", "name": "javax/annotation/RegEx.java", "size": 1064, "crc": -*********}, {"key": "javax/annotation/Signed.java", "name": "javax/annotation/Signed.java", "size": 424, "crc": -*********}, {"key": "javax/annotation/Syntax.java", "name": "javax/annotation/Syntax.java", "size": 1406, "crc": 1799001529}, {"key": "javax/annotation/Tainted.java", "name": "javax/annotation/Tainted.java", "size": 372, "crc": -1775266045}, {"key": "javax/annotation/Untainted.java", "name": "javax/annotation/Untainted.java", "size": 364, "crc": -*********}, {"key": "javax/annotation/WillClose.java", "name": "javax/annotation/WillClose.java", "size": 337, "crc": 1528772292}, {"key": "javax/annotation/WillCloseWhenClosed.java", "name": "javax/annotation/WillCloseWhenClosed.java", "size": 385, "crc": -*********}, {"key": "javax/annotation/WillNotClose.java", "name": "javax/annotation/WillNotClose.java", "size": 344, "crc": 1598901612}, {"key": "javax/annotation/concurrent/GuardedBy.java", "name": "javax/annotation/concurrent/GuardedBy.java", "size": 1611, "crc": -1001210258}, {"key": "javax/annotation/concurrent/Immutable.java", "name": "javax/annotation/concurrent/Immutable.java", "size": 1333, "crc": 1107471227}, {"key": "javax/annotation/concurrent/NotThreadSafe.java", "name": "javax/annotation/concurrent/NotThreadSafe.java", "size": 892, "crc": 1175845665}, {"key": "javax/annotation/concurrent/ThreadSafe.java", "name": "javax/annotation/concurrent/ThreadSafe.java", "size": 752, "crc": 2105265368}, {"key": "javax/annotation/meta/Exclusive.java", "name": "javax/annotation/meta/Exclusive.java", "size": 650, "crc": -2102595138}, {"key": "javax/annotation/meta/Exhaustive.java", "name": "javax/annotation/meta/Exhaustive.java", "size": 1097, "crc": 51790900}, {"key": "javax/annotation/meta/TypeQualifier.java", "name": "javax/annotation/meta/TypeQualifier.java", "size": 796, "crc": -1382501031}, {"key": "javax/annotation/meta/TypeQualifierDefault.java", "name": "javax/annotation/meta/TypeQualifierDefault.java", "size": 590, "crc": 2055384079}, {"key": "javax/annotation/meta/TypeQualifierNickname.java", "name": "javax/annotation/meta/TypeQualifierNickname.java", "size": 822, "crc": -544782671}, {"key": "javax/annotation/meta/TypeQualifierValidator.java", "name": "javax/annotation/meta/TypeQualifierValidator.java", "size": 687, "crc": 1870901072}, {"key": "javax/annotation/meta/When.java", "name": "javax/annotation/meta/When.java", "size": 639, "crc": -2084870089}, {"key": "junit/runner/logo.gif", "name": "junit/runner/logo.gif", "size": 964, "crc": 1498896564}, {"key": "junit/runner/smalllogo.gif", "name": "junit/runner/smalllogo.gif", "size": 883, "crc": 1193524891}]