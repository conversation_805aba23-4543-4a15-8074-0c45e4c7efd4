# 💕 Romantic Android TV Love Messages App

A beautiful, romantic Android TV application that displays love messages from a PHP API backend, designed for sharing heartfelt messages with your girlfriend on the big screen.

## 🎯 Features

### Core Functionality
- **Full-screen love message display** with romantic typography
- **TV remote navigation** (D-pad left/right for message navigation)
- **API integration** with https://mbfjo.com/loveapp/api.php
- **Offline caching** using Room database
- **Automatic refresh** with select/enter button
- **Circular navigation** (seamless loop through messages)

### Romantic UI/UX
- **Immersive full-screen experience** (hidden system bars)
- **Dynamic background colors** from API response
- **Typewriter text effect** for message display
- **Floating hearts animation** overlay
- **Sparkle effects** for magical atmosphere
- **Smooth fade transitions** between messages
- **Message counter indicator**
- **Romantic loading animations**

### TV-Optimized Features
- **D-pad navigation support**
- **Focus management** for Android TV
- **Visual navigation feedback**
- **TV remote control hints**
- **Landscape orientation lock**
- **Keep screen on** during use

## 🏗️ Architecture

### Tech Stack
- **Platform**: Android TV (API 21+)
- **Language**: Kotlin
- **UI Framework**: Jetpack Compose for TV
- **Architecture**: MVVM with LiveData
- **Database**: Room for offline caching
- **Networking**: Retrofit2 + OkHttp
- **Animations**: Compose animations + custom effects

### Project Structure
```
app/src/main/java/com/falaileh/nisso/
├── data/
│   ├── api/
│   │   ├── LoveMessageApiService.kt
│   │   └── NetworkModule.kt
│   ├── database/
│   │   ├── LoveMessageDao.kt
│   │   └── LoveMessageDatabase.kt
│   ├── model/
│   │   └── LoveMessage.kt
│   └── repository/
│       └── MessageRepository.kt
├── ui/
│   ├── animations/
│   │   └── RomanticAnimations.kt
│   ├── components/
│   │   ├── ErrorHandling.kt
│   │   ├── FloatingHeartsOverlay.kt
│   │   ├── LoveMessageScreen.kt
│   │   └── TvNavigationHandler.kt
│   ├── utils/
│   │   ├── ColorUtils.kt
│   │   └── TypewriterText.kt
│   ├── viewmodel/
│   │   └── MessageViewModel.kt
│   └── theme/
└── MainActivity.kt
```

## 🎮 TV Remote Controls

| Button | Action |
|--------|--------|
| ◀ D-pad Left | Previous message |
| ▶ D-pad Right | Next message |
| ⚪ Select/Enter | Refresh messages from API |
| ⬅ Back | Exit app |

## 🌐 API Integration

### Endpoint
- **URL**: `https://mbfjo.com/loveapp/api.php`
- **Method**: GET
- **Response Format**: JSON

### Expected Response
```json
{
  "success": true,
  "messages": [
    {
      "id": "msg_12345",
      "message": "I love you ❤️",
      "bg_color": "#ff6b6b",
      "text_color": "#ffffff",
      "timestamp": "2025-08-30 00:18:25"
    }
  ],
  "count": 2,
  "sort_order": "oldest"
}
```

## 🎨 Visual Effects

### Animations
- **Typewriter Effect**: Text appears character by character
- **Floating Hearts**: Romantic hearts float across the screen
- **Sparkle Overlay**: Twinkling stars for magical atmosphere
- **Fade Transitions**: Smooth transitions between messages
- **Loading Animations**: Romantic loading indicators
- **Background Transitions**: Smooth color changes

### Color Themes
- Dynamic backgrounds based on API `bg_color`
- Contrasting text colors from API `text_color`
- Romantic color palette fallbacks
- Gradient effects for depth

## 📱 Installation & Setup

### Prerequisites
- Android TV device or emulator
- Android Studio
- Internet connection for API calls

### Build Instructions
1. Clone the repository
2. Open in Android Studio
3. Sync Gradle dependencies
4. Build and run on Android TV device

```bash
./gradlew assembleDebug
```

### Configuration
- API endpoint is configured in `LoveMessageApiService.kt`
- Database name and version in `LoveMessageDatabase.kt`
- Animation settings in `RomanticAnimations.kt`

## 🧪 Testing

### Manual Testing Checklist
- [ ] App launches in full-screen mode
- [ ] Messages load from API
- [ ] D-pad navigation works (left/right)
- [ ] Select button refreshes messages
- [ ] Offline mode with cached messages
- [ ] Animations are smooth
- [ ] Error handling works
- [ ] Circular navigation functions

### Test Scenarios
1. **Network Available**: Messages load from API
2. **Network Unavailable**: Cached messages display
3. **API Error**: Error screen with retry option
4. **Empty Response**: Appropriate empty state
5. **Navigation**: Smooth transitions between messages

## 💝 Romantic Features

### Emotional Design
- **Heart-shaped animations** floating across screen
- **Romantic color schemes** (pinks, reds, warm tones)
- **Elegant typography** optimized for TV viewing
- **Smooth transitions** for seamless experience
- **Immersive full-screen** for intimate viewing

### Special Effects
- **Typewriter text** creates anticipation
- **Floating particles** add magical atmosphere
- **Glow effects** for romantic ambiance
- **Pulse animations** for heartbeat feeling

## 🔧 Customization

### Adding New Animations
1. Create animation in `RomanticAnimations.kt`
2. Add to appropriate screen component
3. Configure timing and easing

### Modifying Colors
1. Update `ColorUtils.kt` for color parsing
2. Add new romantic color schemes
3. Configure gradient effects

### API Customization
1. Modify `LoveMessageApiService.kt` for different endpoints
2. Update data models in `LoveMessage.kt`
3. Adjust repository logic as needed

## 🚀 Future Enhancements

### Potential Features
- **Voice control** for hands-free operation
- **Photo backgrounds** from API
- **Sound effects** for interactions
- **Slideshow mode** with auto-advance
- **Message scheduling** for special occasions
- **Multiple language support**
- **Custom fonts** for personalization

## 💕 Perfect for Couples

This app is designed to create magical moments between couples:
- **Big screen experience** for shared viewing
- **Romantic atmosphere** with beautiful animations
- **Personal messages** from your custom API
- **Easy navigation** with TV remote
- **Offline support** for uninterrupted romance

## 📄 License

This project is created for personal use and romantic purposes. Feel free to customize and enhance for your own love story! 💕

---

*Made with ❤️ for sharing love on the big screen*
