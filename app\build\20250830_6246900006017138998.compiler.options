"-Xallow-no-source-files" "-classpath" "E:\\Nisso\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cb09b3c6d7e78b2f3e7eefd96316f9b2\\transformed\\tv-material-1.0.0-alpha07-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4d3af653d807467c49b72243df529564\\transformed\\tv-foundation-1.0.0-alpha07-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ed01123505d1d944d9a3e69d7e54c1e6\\transformed\\foundation-layout-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\307f905624f70faaefa5938229027f86\\transformed\\foundation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6126402b661d892faf884e46d877f49a\\transformed\\animation-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\32aa13c38cd4c55d666a70e938023616\\transformed\\animation-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1941a76ee78facdf8552617eb682ac1a\\transformed\\ui-unit-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55dac832d7d9ca7cd90ad450e12d26cd\\transformed\\ui-geometry-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\be7317d98c36fe976ce5cc4272e062c7\\transformed\\ui-tooling-data-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53088e63cf84af852ddb23e542354aa4\\transformed\\ui-util-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8f1f101c57de1a04c44444408cbaa176\\transformed\\ui-text-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b5ee2ae2bc96aa1cabbd4703141be9bb\\transformed\\ui-tooling-preview-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8d13b293ed259dbc2264508ff969cf54\\transformed\\ui-graphics-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ec485474c4d82a07edcc7ebaa9f49f37\\transformed\\appcompat-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5d4723b11f49281136a2c82328f97458\\transformed\\fragment-1.3.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ce9e73deb82a6e15827cc37f6efff8aa\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9001f1b8780e37537a8cf0e63c75fa44\\transformed\\appcompat-resources-1.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf49dd9d7781c8cf724a0efed474e790\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\da259f39c793eeaa23535db6b7c3652d\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e0c873df565f6d60a27da6f7a5989cc9\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9ad30122b8f21f327d062d0bd8daef2d\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e6d070c7c8705086394ae7762ac8d9\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cd4d69205bbdf1d72f211193d9e0da8e\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-jvm\\2.8.3\\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\\lifecycle-common-jvm-2.8.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4226a4b4e41d61a81953fa64d5644ed3\\transformed\\lifecycle-runtime-compose-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3272962c72c38da494ee4d6a0427d07\\transformed\\lifecycle-runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\962eece9453306e0f974599834c4553e\\transformed\\lifecycle-viewmodel-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c03015406e6cd80c6f81512c0d2f1172\\transformed\\lifecycle-viewmodel-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7e52df3a5ba6df759d9ba4fa0a0d7c1d\\transformed\\lifecycle-viewmodel-ktx-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf801e5edb07c8f46bf45746a567eb3d\\transformed\\lifecycle-livedata-core-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b4aa056cb5c039c4ca38754695a3848c\\transformed\\lifecycle-viewmodel-savedstate-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9a29bb410250aeff04502d89b44dd18c\\transformed\\lifecycle-livedata-core-ktx-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e34d6562796d16bbc1ef15b8aba7d55e\\transformed\\lifecycle-livedata-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b0cd09f0fcd79fcf0c6daf997310b676\\transformed\\lifecycle-runtime-ktx-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\69ebd3f8c1cde71917b16980fbf811cb\\transformed\\lifecycle-livedata-ktx-2.8.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f8a78e3de1ce43fbfae21b37acfc5cd2\\transformed\\lifecycle-viewmodel-compose-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d6ad351d5201063f5711817326c113ff\\transformed\\material-icons-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9f8150abc63e22d3b8d52cf6d206b97c\\transformed\\ui-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eb3c8af3301edc66439639129a8e4016\\transformed\\ui-tooling-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a5592e218bbfa77513c02f5f85fad013\\transformed\\ui-test-manifest-1.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b00c38cc55983a41e60a7ebb076aecef\\transformed\\activity-1.8.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\91e4f3240c42cde3907a1343774491b9\\transformed\\activity-compose-1.8.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6fd122619b5040c7a7f440b333af7d91\\transformed\\activity-ktx-1.8.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\819cd37eb82b4cd20af4121bc1e2288d\\transformed\\core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.room\\room-common\\2.6.1\\ff1b9580850a9b7eef56554e356628d225785265\\room-common-2.6.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5d73bb3376304130b8010663f92b85c0\\transformed\\room-runtime-2.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a93d3b40ba95421afb033b41fc08077a\\transformed\\room-ktx-2.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\16b2659fafda0c6fdbbd905abc48b979\\transformed\\savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\052af593e8a984824f27003f8741328a\\transformed\\savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.retrofit2\\converter-gson\\2.9.0\\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\\converter-gson-2.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.retrofit2\\retrofit\\2.9.0\\d8fdfbd5da952141a665a403348b74538efc05ff\\retrofit-2.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okhttp3\\logging-interceptor\\4.12.0\\e922c1f14d365c0f2bed140cc0825e18462c2778\\logging-interceptor-4.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okhttp3\\okhttp\\4.12.0\\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\\okhttp-4.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\42fd11bbe5f82927a7b08b6b958a201e\\transformed\\lottie-compose-6.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\186025db8802e61a90a9c3dad06cb410\\transformed\\runtime-saveable-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f870c665ddcf48672ad984b900994fa3\\transformed\\runtime-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.7.3\\2b09627576f0989a436a00a4a54b55fa5026fb86\\kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.7.3\\38d9cad3a0b03a10453b56577984bdeb48edeed5\\kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.squareup.okio\\okio-jvm\\3.6.0\\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\\okio-jvm-3.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.9.10\\c7510d64a83411a649c76f2778304ddf71d7437b\\kotlin-stdlib-jdk8-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\29f3a0280f3b1f653289de6593434a60\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cfd7d342a69ddcc046c916929988dd02\\transformed\\sqlite-framework-2.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d19276c1e0c4b1fd296f3123c54ff298\\transformed\\sqlite-2.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4c6339f4e73e6045b8b2e61a69950772\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5000ebfdb65dc17b2257daf341756171\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c80f5e8b887124969668a6bc1a956f7d\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection-jvm\\1.4.0\\e209fb7bd1183032f55a0408121c6251a81acb49\\collection-jvm-1.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.8.0\\b8a16fe526014b7941c1debaccaf9c5153692dbb\\annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3bbbaba43f804ed7977803c49bbbaf7\\transformed\\annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.9.10\\bc5bfc2690338defd5195b05c57562f2194eeb10\\kotlin-stdlib-jdk7-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\2.0.21\\618b539767b4899b4660a83006e052b63f1db551\\kotlin-stdlib-2.0.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.code.gson\\gson\\2.10.1\\b3add478d4382b78ea20b1671390a858002feb6c\\gson-2.10.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\362a104152a45e15ff8a6c86b9c31295\\transformed\\lottie-6.1.0-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-36\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\35.0.0\\core-lambda-stubs.jar;E:\\Nisso\\app\\build\\tmp\\kapt3\\classes\\debug" "-d" "E:\\Nisso\\app\\build\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "-Xplugin=C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-compose-compiler-plugin-embeddable\\2.0.21\\e14f003d962fb25693b461de59490c91072a7979\\kotlin-compose-compiler-plugin-embeddable-2.0.21.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-annotation-processing-gradle\\2.0.21\\b3be9823176d79cb0fc710e77309cfe599be9abf\\kotlin-annotation-processing-gradle-2.0.21.jar" "-P" "plugin:androidx.compose.compiler.plugins.kotlin:sourceInformation=true,plugin:androidx.compose.compiler.plugins.kotlin:generateFunctionKeyMetaClasses=false,plugin:androidx.compose.compiler.plugins.kotlin:traceMarkersEnabled=true" "-Xuse-inline-scopes-numbers" "-Xallow-unstable-dependencies" "E:\\Nisso\\app\\build\\generated\\source\\kapt\\debug\\com\\falaileh\\nisso\\data\\database\\LoveMessageDao_Impl.java" "E:\\Nisso\\app\\build\\generated\\source\\kapt\\debug\\com\\falaileh\\nisso\\data\\database\\LoveMessageDatabase_Impl.java" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\data\\api\\LoveMessageApiService.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\data\\api\\NetworkModule.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\data\\database\\LoveMessageDao.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\data\\database\\LoveMessageDatabase.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\data\\model\\LoveMessage.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\data\\repository\\MessageRepository.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\MainActivity.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\animations\\RomanticAnimations.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\components\\ErrorHandling.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\components\\FloatingHeartsOverlay.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\components\\LoveMessageScreen.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\components\\TvNavigationHandler.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\theme\\Color.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\theme\\Theme.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\theme\\Type.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\utils\\ColorUtils.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\utils\\TypewriterText.kt" "E:\\Nisso\\app\\src\\main\\java\\com\\falaileh\\nisso\\ui\\viewmodel\\MessageViewModel.kt"