[{"key": "androidx/activity/Api34Impl.class", "name": "androidx/activity/Api34Impl.class", "size": 1990, "crc": -61264803}, {"key": "androidx/activity/BackEventCompat$Companion.class", "name": "androidx/activity/BackEventCompat$Companion.class", "size": 892, "crc": -358212213}, {"key": "androidx/activity/BackEventCompat$SwipeEdge.class", "name": "androidx/activity/BackEventCompat$SwipeEdge.class", "size": 1134, "crc": -662310490}, {"key": "androidx/activity/BackEventCompat.class", "name": "androidx/activity/BackEventCompat.class", "size": 3536, "crc": 1190707418}, {"key": "androidx/activity/Cancellable.class", "name": "androidx/activity/Cancellable.class", "size": 421, "crc": -708940838}, {"key": "androidx/activity/ComponentDialog.class", "name": "androidx/activity/ComponentDialog.class", "size": 7578, "crc": -1729260339}, {"key": "androidx/activity/EdgeToEdge.class", "name": "androidx/activity/EdgeToEdge.class", "size": 4945, "crc": 1811806909}, {"key": "androidx/activity/EdgeToEdgeApi21.class", "name": "androidx/activity/EdgeToEdgeApi21.class", "size": 1747, "crc": -595945461}, {"key": "androidx/activity/EdgeToEdgeApi23.class", "name": "androidx/activity/EdgeToEdgeApi23.class", "size": 2182, "crc": -281883284}, {"key": "androidx/activity/EdgeToEdgeApi26.class", "name": "androidx/activity/EdgeToEdgeApi26.class", "size": 2427, "crc": 714980639}, {"key": "androidx/activity/EdgeToEdgeApi29.class", "name": "androidx/activity/EdgeToEdgeApi29.class", "size": 2660, "crc": -460924744}, {"key": "androidx/activity/EdgeToEdgeBase.class", "name": "androidx/activity/EdgeToEdgeBase.class", "size": 1402, "crc": -1734289017}, {"key": "androidx/activity/EdgeToEdgeImpl.class", "name": "androidx/activity/EdgeToEdgeImpl.class", "size": 932, "crc": 859879704}, {"key": "androidx/activity/FullyDrawnReporter.class", "name": "androidx/activity/FullyDrawnReporter.class", "size": 6883, "crc": 1058805723}, {"key": "androidx/activity/FullyDrawnReporterKt$reportWhenComplete$1.class", "name": "androidx/activity/FullyDrawnReporterKt$reportWhenComplete$1.class", "size": 2011, "crc": 1693911518}, {"key": "androidx/activity/FullyDrawnReporterKt.class", "name": "androidx/activity/FullyDrawnReporterKt.class", "size": 3116, "crc": 21589385}, {"key": "androidx/activity/FullyDrawnReporterOwner.class", "name": "androidx/activity/FullyDrawnReporterOwner.class", "size": 670, "crc": 339222718}, {"key": "androidx/activity/OnBackPressedCallback.class", "name": "androidx/activity/OnBackPressedCallback.class", "size": 4631, "crc": 1259570835}, {"key": "androidx/activity/OnBackPressedDispatcher$1.class", "name": "androidx/activity/OnBackPressedDispatcher$1.class", "size": 1793, "crc": 858506618}, {"key": "androidx/activity/OnBackPressedDispatcher$2.class", "name": "androidx/activity/OnBackPressedDispatcher$2.class", "size": 1796, "crc": 912983702}, {"key": "androidx/activity/OnBackPressedDispatcher$3.class", "name": "androidx/activity/OnBackPressedDispatcher$3.class", "size": 1242, "crc": 729229894}, {"key": "androidx/activity/OnBackPressedDispatcher$4.class", "name": "androidx/activity/OnBackPressedDispatcher$4.class", "size": 1251, "crc": -599734795}, {"key": "androidx/activity/OnBackPressedDispatcher$5.class", "name": "androidx/activity/OnBackPressedDispatcher$5.class", "size": 1242, "crc": 657272376}, {"key": "androidx/activity/OnBackPressedDispatcher$Api33Impl.class", "name": "androidx/activity/OnBackPressedDispatcher$Api33Impl.class", "size": 3101, "crc": -1241373420}, {"key": "androidx/activity/OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1.class", "name": "androidx/activity/OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1.class", "size": 2959, "crc": -1467618544}, {"key": "androidx/activity/OnBackPressedDispatcher$Api34Impl.class", "name": "androidx/activity/OnBackPressedDispatcher$Api34Impl.class", "size": 2569, "crc": -1944311729}, {"key": "androidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable.class", "name": "androidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable.class", "size": 3083, "crc": -682888943}, {"key": "androidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable.class", "name": "androidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable.class", "size": 2502, "crc": -143188941}, {"key": "androidx/activity/OnBackPressedDispatcher$addCallback$1.class", "name": "androidx/activity/OnBackPressedDispatcher$addCallback$1.class", "size": 1391, "crc": -122303386}, {"key": "androidx/activity/OnBackPressedDispatcher$addCancellableCallback$1.class", "name": "androidx/activity/OnBackPressedDispatcher$addCancellableCallback$1.class", "size": 1436, "crc": 1325878599}, {"key": "androidx/activity/OnBackPressedDispatcher.class", "name": "androidx/activity/OnBackPressedDispatcher.class", "size": 13120, "crc": -913331987}, {"key": "androidx/activity/OnBackPressedDispatcherKt$addCallback$callback$1.class", "name": "androidx/activity/OnBackPressedDispatcherKt$addCallback$callback$1.class", "size": 1519, "crc": 47840396}, {"key": "androidx/activity/OnBackPressedDispatcherKt.class", "name": "androidx/activity/OnBackPressedDispatcherKt.class", "size": 2481, "crc": 1123803977}, {"key": "androidx/activity/OnBackPressedDispatcherOwner.class", "name": "androidx/activity/OnBackPressedDispatcherOwner.class", "size": 780, "crc": -1780134358}, {"key": "androidx/activity/SystemBarStyle$Companion$auto$1.class", "name": "androidx/activity/SystemBarStyle$Companion$auto$1.class", "size": 2022, "crc": 936826787}, {"key": "androidx/activity/SystemBarStyle$Companion$dark$1.class", "name": "androidx/activity/SystemBarStyle$Companion$dark$1.class", "size": 1737, "crc": 675704507}, {"key": "androidx/activity/SystemBarStyle$Companion$light$1.class", "name": "androidx/activity/SystemBarStyle$Companion$light$1.class", "size": 1741, "crc": 316178958}, {"key": "androidx/activity/SystemBarStyle$Companion.class", "name": "androidx/activity/SystemBarStyle$Companion.class", "size": 3172, "crc": -333420160}, {"key": "androidx/activity/SystemBarStyle.class", "name": "androidx/activity/SystemBarStyle.class", "size": 3699, "crc": -198487015}, {"key": "androidx/activity/ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1.class", "name": "androidx/activity/ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1.class", "size": 1837, "crc": -1235656802}, {"key": "androidx/activity/ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2.class", "name": "androidx/activity/ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2.class", "size": 2002, "crc": 1499649627}, {"key": "androidx/activity/ViewTreeFullyDrawnReporterOwner.class", "name": "androidx/activity/ViewTreeFullyDrawnReporterOwner.class", "size": 2521, "crc": -260827646}, {"key": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$1.class", "name": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$1.class", "size": 1871, "crc": -1858978807}, {"key": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2.class", "name": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2.class", "size": 2081, "crc": 1066734303}, {"key": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner.class", "name": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner.class", "size": 2650, "crc": 1480814886}, {"key": "androidx/activity/contextaware/ContextAware.class", "name": "androidx/activity/contextaware/ContextAware.class", "size": 989, "crc": -731652454}, {"key": "androidx/activity/contextaware/ContextAwareHelper.class", "name": "androidx/activity/contextaware/ContextAwareHelper.class", "size": 2642, "crc": -737783095}, {"key": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$1.class", "name": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$1.class", "size": 2889, "crc": -1657592583}, {"key": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$listener$1.class", "name": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$listener$1.class", "size": 3237, "crc": -977261674}, {"key": "androidx/activity/contextaware/ContextAwareKt.class", "name": "androidx/activity/contextaware/ContextAwareKt.class", "size": 5086, "crc": -1052591437}, {"key": "androidx/activity/contextaware/OnContextAvailableListener.class", "name": "androidx/activity/contextaware/OnContextAvailableListener.class", "size": 682, "crc": 1640788270}, {"key": "androidx/activity/result/ActivityResultCallback.class", "name": "androidx/activity/result/ActivityResultCallback.class", "size": 628, "crc": 1970847939}, {"key": "androidx/activity/result/ActivityResultRegistryOwner.class", "name": "androidx/activity/result/ActivityResultRegistryOwner.class", "size": 726, "crc": 1586312733}, {"key": "androidx/activity/result/IntentSenderRequest$Builder$Flag.class", "name": "androidx/activity/result/IntentSenderRequest$Builder$Flag.class", "size": 816, "crc": 812248084}, {"key": "androidx/activity/result/IntentSenderRequest$Builder.class", "name": "androidx/activity/result/IntentSenderRequest$Builder.class", "size": 2552, "crc": 634140502}, {"key": "androidx/activity/result/IntentSenderRequest$Companion$CREATOR$1.class", "name": "androidx/activity/result/IntentSenderRequest$Companion$CREATOR$1.class", "size": 1958, "crc": -1695759292}, {"key": "androidx/activity/result/IntentSenderRequest$Companion.class", "name": "androidx/activity/result/IntentSenderRequest$Companion.class", "size": 1070, "crc": 589185882}, {"key": "androidx/activity/result/IntentSenderRequest.class", "name": "androidx/activity/result/IntentSenderRequest.class", "size": 3926, "crc": 179881267}, {"key": "androidx/activity/result/PickVisualMediaRequest$Builder.class", "name": "androidx/activity/result/PickVisualMediaRequest$Builder.class", "size": 2378, "crc": 548726142}, {"key": "androidx/activity/result/PickVisualMediaRequest.class", "name": "androidx/activity/result/PickVisualMediaRequest.class", "size": 2042, "crc": 1955974580}, {"key": "androidx/activity/result/PickVisualMediaRequestKt.class", "name": "androidx/activity/result/PickVisualMediaRequestKt.class", "size": 2319, "crc": 531241200}, {"key": "androidx/activity/result/contract/ActivityResultContract$SynchronousResult.class", "name": "androidx/activity/result/contract/ActivityResultContract$SynchronousResult.class", "size": 1068, "crc": 1193330250}, {"key": "androidx/activity/result/contract/ActivityResultContract.class", "name": "androidx/activity/result/contract/ActivityResultContract.class", "size": 2153, "crc": 1301119215}, {"key": "androidx/activity/result/contract/ActivityResultContracts$CaptureVideo.class", "name": "androidx/activity/result/contract/ActivityResultContracts$CaptureVideo.class", "size": 3553, "crc": 19664806}, {"key": "androidx/activity/result/contract/ActivityResultContracts$CreateDocument.class", "name": "androidx/activity/result/contract/ActivityResultContracts$CreateDocument.class", "size": 5018, "crc": -1172869576}, {"key": "androidx/activity/result/contract/ActivityResultContracts$GetContent.class", "name": "androidx/activity/result/contract/ActivityResultContracts$GetContent.class", "size": 4248, "crc": 1841910523}, {"key": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents$Companion.class", "size": 2857, "crc": -498980290}, {"key": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents.class", "name": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents.class", "size": 4689, "crc": 1998484215}, {"key": "androidx/activity/result/contract/ActivityResultContracts$OpenDocument.class", "name": "androidx/activity/result/contract/ActivityResultContracts$OpenDocument.class", "size": 4414, "crc": -456867863}, {"key": "androidx/activity/result/contract/ActivityResultContracts$OpenDocumentTree.class", "name": "androidx/activity/result/contract/ActivityResultContracts$OpenDocumentTree.class", "size": 4333, "crc": -560525556}, {"key": "androidx/activity/result/contract/ActivityResultContracts$OpenMultipleDocuments.class", "name": "androidx/activity/result/contract/ActivityResultContracts$OpenMultipleDocuments.class", "size": 4764, "crc": -1329635726}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickContact.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickContact.class", "size": 3280, "crc": -1019344133}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia$Companion.class", "size": 1822, "crc": 1167856042}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia.class", "size": 9191, "crc": -1162235409}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$Companion.class", "size": 5626, "crc": 685131519}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageAndVideo.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageAndVideo.class", "size": 1229, "crc": -1547086560}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageOnly.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageOnly.class", "size": 1217, "crc": -1047177687}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$SingleMimeType.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$SingleMimeType.class", "size": 1567, "crc": -49394751}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VideoOnly.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VideoOnly.class", "size": 1217, "crc": 1996719395}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VisualMediaType.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VisualMediaType.class", "size": 1121, "crc": -810064056}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia.class", "size": 9913, "crc": 2062349499}, {"key": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion.class", "size": 2161, "crc": 244335017}, {"key": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions.class", "name": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions.class", "size": 8429, "crc": 707861711}, {"key": "androidx/activity/result/contract/ActivityResultContracts$RequestPermission.class", "name": "androidx/activity/result/contract/ActivityResultContracts$RequestPermission.class", "size": 5298, "crc": 31111760}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion.class", "size": 1121, "crc": -914616939}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult.class", "size": 2895, "crc": -159364259}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult$Companion.class", "size": 1241, "crc": -336422495}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult.class", "size": 3601, "crc": -844575887}, {"key": "androidx/activity/result/contract/ActivityResultContracts$TakePicture.class", "name": "androidx/activity/result/contract/ActivityResultContracts$TakePicture.class", "size": 3550, "crc": -178264390}, {"key": "androidx/activity/result/contract/ActivityResultContracts$TakePicturePreview.class", "name": "androidx/activity/result/contract/ActivityResultContracts$TakePicturePreview.class", "size": 4112, "crc": 1555426930}, {"key": "androidx/activity/result/contract/ActivityResultContracts$TakeVideo.class", "name": "androidx/activity/result/contract/ActivityResultContracts$TakeVideo.class", "size": 4558, "crc": 721697374}, {"key": "androidx/activity/result/contract/ActivityResultContracts.class", "name": "androidx/activity/result/contract/ActivityResultContracts.class", "size": 2507, "crc": -1296753956}, {"key": "androidx/activity/ComponentActivity$1$1.class", "name": "androidx/activity/ComponentActivity$1$1.class", "size": 1345, "crc": -675717406}, {"key": "androidx/activity/ComponentActivity$1$2.class", "name": "androidx/activity/ComponentActivity$1$2.class", "size": 1758, "crc": 660472155}, {"key": "androidx/activity/ComponentActivity$1.class", "name": "androidx/activity/ComponentActivity$1.class", "size": 5565, "crc": -1219462765}, {"key": "androidx/activity/ComponentActivity$2.class", "name": "androidx/activity/ComponentActivity$2.class", "size": 1513, "crc": 1157120034}, {"key": "androidx/activity/ComponentActivity$3.class", "name": "androidx/activity/ComponentActivity$3.class", "size": 1732, "crc": 808138335}, {"key": "androidx/activity/ComponentActivity$4.class", "name": "androidx/activity/ComponentActivity$4.class", "size": 1225, "crc": 1698406681}, {"key": "androidx/activity/ComponentActivity$5.class", "name": "androidx/activity/ComponentActivity$5.class", "size": 1375, "crc": 103959859}, {"key": "androidx/activity/ComponentActivity$6.class", "name": "androidx/activity/ComponentActivity$6.class", "size": 1735, "crc": 982443138}, {"key": "androidx/activity/ComponentActivity$Api19Impl.class", "name": "androidx/activity/ComponentActivity$Api19Impl.class", "size": 691, "crc": 1412043632}, {"key": "androidx/activity/ComponentActivity$Api33Impl.class", "name": "androidx/activity/ComponentActivity$Api33Impl.class", "size": 834, "crc": 476396876}, {"key": "androidx/activity/ComponentActivity$NonConfigurationInstances.class", "name": "androidx/activity/ComponentActivity$NonConfigurationInstances.class", "size": 565, "crc": 1255618607}, {"key": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutor.class", "name": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutor.class", "size": 459, "crc": -1842703936}, {"key": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutorApi1.class", "name": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutorApi1.class", "size": 1578, "crc": -1807165027}, {"key": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutorApi16Impl.class", "name": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutorApi16Impl.class", "size": 3495, "crc": 1654466985}, {"key": "androidx/activity/ComponentActivity.class", "name": "androidx/activity/ComponentActivity.class", "size": 28218, "crc": -1356832838}, {"key": "androidx/activity/ImmLeaksCleaner.class", "name": "androidx/activity/ImmLeaksCleaner.class", "size": 3285, "crc": 824388980}, {"key": "androidx/activity/result/ActivityResult$1.class", "name": "androidx/activity/result/ActivityResult$1.class", "size": 1292, "crc": 1705882238}, {"key": "androidx/activity/result/ActivityResult.class", "name": "androidx/activity/result/ActivityResult.class", "size": 2722, "crc": 440775188}, {"key": "androidx/activity/result/ActivityResultCaller.class", "name": "androidx/activity/result/ActivityResultCaller.class", "size": 1249, "crc": 1554217623}, {"key": "androidx/activity/result/ActivityResultLauncher.class", "name": "androidx/activity/result/ActivityResultLauncher.class", "size": 1321, "crc": 1156758701}, {"key": "androidx/activity/result/ActivityResultRegistry$1.class", "name": "androidx/activity/result/ActivityResultRegistry$1.class", "size": 3391, "crc": -161300276}, {"key": "androidx/activity/result/ActivityResultRegistry$2.class", "name": "androidx/activity/result/ActivityResultRegistry$2.class", "size": 3198, "crc": -1800957786}, {"key": "androidx/activity/result/ActivityResultRegistry$3.class", "name": "androidx/activity/result/ActivityResultRegistry$3.class", "size": 3163, "crc": -1232273819}, {"key": "androidx/activity/result/ActivityResultRegistry$CallbackAndContract.class", "name": "androidx/activity/result/ActivityResultRegistry$CallbackAndContract.class", "size": 1312, "crc": 1930263048}, {"key": "androidx/activity/result/ActivityResultRegistry$LifecycleContainer.class", "name": "androidx/activity/result/ActivityResultRegistry$LifecycleContainer.class", "size": 1651, "crc": -1895247229}, {"key": "androidx/activity/result/ActivityResultRegistry.class", "name": "androidx/activity/result/ActivityResultRegistry.class", "size": 11867, "crc": 471035416}, {"key": "META-INF/activity_release.kotlin_module", "name": "META-INF/activity_release.kotlin_module", "size": 282, "crc": -1692061131}, {"key": "META-INF/androidx.activity_activity.version", "name": "META-INF/androidx.activity_activity.version", "size": 6, "crc": 952795271}]